package com.fangcang.hotel.mtosbld.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/8/19 17:02
 * @Description:
 */

@Data
public class RatesDTO implements Serializable {

    /**
     * 餐食，RO 不含餐；BB 含早餐
     */
    private String boardCode;

    /**
     * 价格计划ID
     */
    private String ratePlanCode;

    /**
     * 渠道，默认：1
     */
    private Integer channel;

    /**
     * 房间数
     */
    private Integer allotment;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 取消政策
     */
    private CancellationPolicyDTO cancellationPolicy;

}
