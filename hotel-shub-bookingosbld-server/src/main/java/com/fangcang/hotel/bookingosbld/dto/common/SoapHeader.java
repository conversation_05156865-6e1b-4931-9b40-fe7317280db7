package com.fangcang.hotel.bookingosbld.dto.common;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/5/31 11:26
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "soapenv:Header")
public class SoapHeader {

    @XmlElement(name = "add:MessageID")
    private AddMessageID messageID;

    @XmlElement(name = "add:Action")
    private AddAction addAction;

    @XmlElement(name = "add:To")
    private AddTo addTo;

    @XmlElement(name = "link:TransactionFlowLink")
    private TransactionFlowLink transactionFlowLink;

    @XmlElement(name = "oas:Security")
    private OasSecurity oasSecurity;

    @XmlElement(name = "AMA_SecurityHostedUser")
    private AMASecurityHostedUser amaSecurityHostedUser;

    @XmlElement(name = "awsse:Session")
    private AwsseSession awsseSession;

    public AddMessageID getMessageID() {
        return messageID;
    }

    public void setMessageID(AddMessageID messageID) {
        this.messageID = messageID;
    }

    public AddAction getAddAction() {
        return addAction;
    }

    public void setAddAction(AddAction addAction) {
        this.addAction = addAction;
    }

    public AddTo getAddTo() {
        return addTo;
    }

    public void setAddTo(AddTo addTo) {
        this.addTo = addTo;
    }

    public TransactionFlowLink getTransactionFlowLink() {
        return transactionFlowLink;
    }

    public void setTransactionFlowLink(TransactionFlowLink transactionFlowLink) {
        this.transactionFlowLink = transactionFlowLink;
    }

    public OasSecurity getOasSecurity() {
        return oasSecurity;
    }

    public void setOasSecurity(OasSecurity oasSecurity) {
        this.oasSecurity = oasSecurity;
    }

    public AMASecurityHostedUser getAmaSecurityHostedUser() {
        return amaSecurityHostedUser;
    }

    public void setAmaSecurityHostedUser(AMASecurityHostedUser amaSecurityHostedUser) {
        this.amaSecurityHostedUser = amaSecurityHostedUser;
    }

    public AwsseSession getAwsseSession() {
        return awsseSession;
    }

    public void setAwsseSession(AwsseSession awsseSession) {
        this.awsseSession = awsseSession;
    }
}
