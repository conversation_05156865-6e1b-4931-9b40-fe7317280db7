package com.fangcang.hotel.amadeusbld.dto.request.hotel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/22 10:48
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "AreaInfo")
public class AreaInfo {

    @XmlAttribute(name = "SendRefPoints")
    private String sendRefPoints;

    @XmlAttribute(name = "SendAttractions")
    private String sendAttractions;

    @XmlAttribute(name = "SendRecreations")
    private String sendRecreations;

    public String getSendRefPoints() {
        return sendRefPoints;
    }

    public void setSendRefPoints(String sendRefPoints) {
        this.sendRefPoints = sendRefPoints;
    }

    public String getSendAttractions() {
        return sendAttractions;
    }

    public void setSendAttractions(String sendAttractions) {
        this.sendAttractions = sendAttractions;
    }

    public String getSendRecreations() {
        return sendRecreations;
    }

    public void setSendRecreations(String sendRecreations) {
        this.sendRecreations = sendRecreations;
    }
}
