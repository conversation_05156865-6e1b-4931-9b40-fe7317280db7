spring:
  cloud:
    nacos:
      #服务注册到nacos
      discovery:
        server-addr: 192.168.78.240:8848
        namespace: dev
        enabled: true
        group: DEFAULT_GROUP #默认 可以不用写
        register-enabled: true #是否把自己注册到注册中心的地址
      config: #读取nacos中的配置，单个配置文件直接配置prefix,否则读取不到配置，多个则采用extension-configs
        server-addr: 192.168.78.240:8848
        namespace: dev
        enabled: true #是否使用nacos的配置中心配置覆盖本地配置
        #context-path: /nacos
        file-extension: yml  #该配置的缺省值为properties，即默认是读取properties格式的配置文件
        extension-configs:
          - data-id: hotel-shub-ratehawkosbld-server.yml
            group: DEFAULT_GROUP #默认 可以不用写
            refresh: true #默认不刷新
          - data-id: hotel-shub-mq.yml
            group: DEFAULT_GROUP #默认 可以不用写
            refresh: true #默认不刷新