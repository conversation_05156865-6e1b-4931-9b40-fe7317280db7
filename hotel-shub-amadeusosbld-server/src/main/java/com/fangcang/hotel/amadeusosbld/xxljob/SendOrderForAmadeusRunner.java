package com.fangcang.hotel.amadeusosbld.xxljob;

import com.alibaba.fastjson.JSON;
import com.fangcang.hotel.amadeusosbld.constants.AmadeusOSBldRedisKey;
import com.fangcang.hotel.amadeusosbld.properties.AMADEUSOSBLDApplicationProperties;
import com.fangcang.hotel.amadeusosbld.service.impl.AMADEUSOSBLDOrderServiceImpl;
import com.fangcang.hotel.amadeusosbld.thread.AmadeusOSBldSendOrderThread;
import com.fangcang.hotel.amadeusosbld.utils.StringUtil;
import com.fangcang.hotel.common.api.common.order.SupplyOrderApi;
import com.fangcang.hotel.common.api.common.order.dto.SupplyOrderQueryReqDTO;
import com.fangcang.hotel.common.api.common.order.dto.SupplyOrderRespDTO;
import com.fangcang.hotel.data.api.dto.order.create.CreateSupplyOrderRequest;
import com.fangcang.hotel.data.api.enums.OrderStatusEnum;
import com.fangcang.hotel.data.api.enums.SupplyClassEnum;
import com.fangcang.hotel.framework.common.domain.ResultX;
import com.fangcang.hotel.framework.redis.core.RedisTemplateX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName SendOrderForAmadeusRunner
 * @Description 消费待发单队列供货单进行发单，并确认给SaaS
 * @createTime 2022-06-22 10:56:42
 * @Param
 * @return
 */
@Component
@Slf4j
public class SendOrderForAmadeusRunner {


    @Autowired
    private ThreadPoolTaskExecutor sendAmadeusOrderExecutor;

    @Autowired
    private SupplyOrderApi supplyOrderApi;

    @Autowired
    private AMADEUSOSBLDOrderServiceImpl amadeusosbldOrderService;

    @Autowired
    private AMADEUSOSBLDApplicationProperties amadeusosbldApplicationProperties;


    @XxlJob("sendOrderForAmadeusRunner")
    public void sendOrderForAmadeusRunner() {
        try {
            XxlJobHelper.log("启动消费待发单队列进行发单开始.");

            String key = AmadeusOSBldRedisKey.SEND_ORDER;
            if (RedisTemplateX.lLen(key) == 0) {
                XxlJobHelper.log("待发单队列为空，结束执行.");
                return;
            }
            for (int i = 0; i < amadeusosbldApplicationProperties.getSendOrderCount(); i++) {
                // 获取数据
                String merchantOrderCode = RedisTemplateX.lRightPop(key);
                if (StringUtil.isValidString(merchantOrderCode)) {
                    log.info("开始执行AMADEUSBLD发单，merchantOrderCode={}" , merchantOrderCode);

                    // 先查询和校验供应端订单及其状态信息
                    SupplyOrderQueryReqDTO supplyOrderQueryReqDTO = new SupplyOrderQueryReqDTO();
                    supplyOrderQueryReqDTO.setMerchantOrderCode(merchantOrderCode);
                    supplyOrderQueryReqDTO.setOrderStatus(OrderStatusEnum.NEED_CONFIRM.getResult());
                    supplyOrderQueryReqDTO.setSupplyClass(SupplyClassEnum.AMADEUSOSBLD.getSupplierClass());

                    ResultX<SupplyOrderRespDTO> supplyOrderDtoResult = supplyOrderApi.supplyOrder(supplyOrderQueryReqDTO);
                    SupplyOrderRespDTO supplyOrderDto = null;
                    if (!supplyOrderDtoResult.isSuccess() || supplyOrderDtoResult.getData() != null) {
                        supplyOrderDto = supplyOrderDtoResult.getData();
                        if (!supplyOrderQueryReqDTO.getMerchantOrderCode().equals(supplyOrderDto.getMerchantOrderCode())) {
                            log.error("AMADEUSOSBLD非即时确认下单-发供货单失败【串单】，调用common-server查询供货单数据不正确！MerchantOrderCode:" + supplyOrderQueryReqDTO.getMerchantOrderCode());
                            delLock(supplyOrderDto.getMerchantOrderCode());
                            continue;
                        } else {
                            if (null != supplyOrderDto.getSupplyOrderStatus()) {
                                if (Integer.valueOf(supplyOrderDto.getSupplyOrderStatus()).intValue() == OrderStatusEnum.NO_CONFIRM.getResult().intValue()) {
                                    log.error("AMADEUSOSBLD非即时确认下单-下单未发成功，或被拒单重新发单，MerchantOrderCode：" + supplyOrderDto.getMerchantOrderCode());
                                    continue;
                                } else if (Integer.valueOf(supplyOrderDto.getSupplyOrderStatus()).intValue() == OrderStatusEnum.CONFIRM.getResult().intValue()) {
                                    log.error("AMADEUSOSBLD非即时确认下单-订单已确认，不需要重复发单，MerchantOrderCode：" + supplyOrderDto.getMerchantOrderCode());
                                    continue;
                                }
                            }
                        }
                    } else {
                        log.error("AMADEUSOSBLD非即时确认下单失败，供应端订单不存在，supplyOrderCode：" + supplyOrderQueryReqDTO.getMerchantOrderCode());
                        continue;
                    }

                    CreateSupplyOrderRequest createOrderRequest = null;
                    try {
                        // 缓存的供货单详情请求
                        String supplyOrderDetailKey = AmadeusOSBldRedisKey.SEND_ORDER_DETAIL + supplyOrderDto.getMerchantOrderCode();
                        String supplyOrderReqJosn = RedisTemplateX.get(supplyOrderDetailKey);
                        log.info("AMADEUSOSBLD非即时确认下单获取缓存供货单详情：supplyOrderReqJosn={}" , supplyOrderReqJosn);
                        createOrderRequest = JSON.parseObject(supplyOrderReqJosn, CreateSupplyOrderRequest.class);
                    } catch (Exception e) {
                        log.error("AMADEUSOSBLD非即时确认下单获取缓存供货单详情失败，供货单编码=" + supplyOrderDto.getMerchantOrderCode(), ",merchantCode=" + supplyOrderDto.getMerchantCode() + ",supplyCode=" + supplyOrderDto.getSupplyCode(), e);
                    }


                    if (createOrderRequest != null) {
                        //创建订单原始请求
                        AmadeusOSBldSendOrderThread amadeusOSBldSendOrderThread = new AmadeusOSBldSendOrderThread(createOrderRequest, amadeusosbldOrderService, amadeusosbldApplicationProperties, supplyOrderApi);
                        sendAmadeusOrderExecutor.execute(amadeusOSBldSendOrderThread);
                        Thread.sleep(10);
                    } else {
                        //查询失败|| 缓存有问题
                        log.error("异步发单失败,从缓存中获取创建订单原始请求失败");
                    }
                }
            }
            XxlJobHelper.log("启动消费待发单队列供货单进行发单成功.");
        } catch (Exception e) {
            log.error("启动消费待发单队列供货单进行发单，发生未知异常.", e);
        }
    }

    /**
     * 删除锁
     *
     * @param supplyOrderCode
     */
    private void delLock(String supplyOrderCode) {
        if (StringUtil.isValidString(supplyOrderCode)) {
            String lockKey = AmadeusOSBldRedisKey.SEND_ORDER_LOCK + supplyOrderCode;
            RedisTemplateX.delete(lockKey);
            log.info("删除锁lockKey={}" , lockKey);
        }
    }
}
