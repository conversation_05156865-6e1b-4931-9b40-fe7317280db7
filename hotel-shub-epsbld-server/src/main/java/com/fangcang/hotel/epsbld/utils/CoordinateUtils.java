package com.fangcang.hotel.epsbld.utils;

import com.fangcang.hotel.epsbld.entity.Point;

/**
 * 地图坐标转换
 */
public class CoordinateUtils {

    /**
     * 谷歌转换百度
     */
    public static Point googleConvertBaidu(Double lat, Double lon) {
        double x_pi = 3.14159265358979324 * 3000.0 / 180.0;
        double x = lon, y = lat;
        double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
        double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
        double bd_lon = z * Math.cos(theta) + 0.0065;
        double bd_lat = z * Math.sin(theta) + 0.006;
        Point point = new Point();
        point.setLat(bd_lat);
        point.setLng(bd_lon);
        return point;
    }

    /**
     * 百度转换高德
     */
    public static Point baiduConvertGaode(Double lat, Double lon) {
        double x_pi = 3.14159265358979324 * 3000.0 / 180.0;
        double x = lon - 0.0065, y = lat - 0.006;
        double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
        double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
        double tempLon = z * Math.cos(theta);
        double tempLat = z * Math.sin(theta);
        Point point = new Point();
        point.setLat(tempLat);
        point.setLng(tempLon);
        return point;
    }
}