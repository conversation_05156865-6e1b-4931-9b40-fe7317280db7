package com.fangcang.hotel.mtosbld.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/8/26 11:08
 * @Description:
 */

@Getter
public enum MTOSBLDLogEnum {

    // 代表日志级别为INFO，用于记录一般的信息性消息
    INFO,

    // 代表日志级别为ERROR，用于记录错误消息，通常指出了程序中需要关注的问题
    ERROR,

    // 代表日志级别为WARN，用于记录警告消息，提示可能存在潜在问题或风险
    WARN,

    // 代表日志级别为DEBUG，用于记录调试信息，帮助开发者在开发过程中定位问题
    DEBUG,

    // 代表日志级别为TRACE，用于记录详细的调试信息，通常包含程序的执行流程细节
    TRACE;

}
