package com.fangcang.hotel.rjbld.dto.response.hotel;

import java.util.List;
import com.fangcang.hotel.rjbld.dto.response.hotel.Province;

import com.fangcang.hotel.rjbld.dto.response.BaseResponse;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  获取省份响应参数
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:14
 */
@Data
public class ProvinceDataResp extends BaseResponse implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 返回结果代码
     */
    @JSONField(name = "ResCode")
    private Integer resCode;

    /**
     * 返回结果中文说明
     */
    @JSONField(name = "ResDesc")
    private String resDesc;

    /**
     * 省份信息列表
     */
    @JSONField(name = "provinceList")
    private List<Province> provinceList;

}
