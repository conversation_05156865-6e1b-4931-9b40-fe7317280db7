package com.fangcang.hotel.common.service.mapping;

import com.fangcang.hotel.common.api.common.mapping.dto.HotelMappingPageRespDTO;
import com.fangcang.hotel.common.controller.admin.mapping.vo.*;
import com.fangcang.hotel.common.controller.admin.open.vo.mapping.HotelMappingEdit;
import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyHotelRespVO;
import com.fangcang.hotel.common.dal.db.mapping.HotelMappingDO;
import com.fangcang.hotel.framework.common.domain.PageResult;

import javax.validation.Valid;
import java.util.List;

/**
 * 酒店映射 Service 接口
 *
 * <AUTHOR>
 */
public interface HotelMappingService {


    /**
     * 批量新增酒店映射
     *
     * @param reqVOs 更新信息
     * @return 成功数量
     */
    Long hotelMappingAdds(@Valid List<HotelMappingAditReqVO> reqVOs);


    /**
     * 新增酒店映射
     *
     * @param reqVO 更新信息
     * @return 成功数量
     */
    Long hotelMappingAdd(@Valid HotelMappingAditReqVO reqVO);

    /**
     * 修改酒店映射
     *
     * @param reqVO 更新信息
     * @return id
     */
    Long hotelMappingEdit(HotelMappingAditReqVO reqVO);

    /**
     * 分页查询有效房型映射的已映射供应商酒店ID
     *
     * @param hotelMappingPageReqVO
     * @return spHotelId-hotelId
     */
    @Deprecated
    PageResult<String> querySpHotelIdAndHotelIdWithRoomMapping(@Valid HotelMappingPageReqVO hotelMappingPageReqVO);

    /**
     * 分页查询有效房型映射的已映射供应商酒店ID 分页优化
     *
     * @param hotelMappingPageReqVO
     * @return spHotelId-hotelId
     */
    PageResult<HotelMappingPageRespDTO> querySpHotelIdHotelMappingPage(@Valid HotelMappingPageReqVO hotelMappingPageReqVO);

    /**
     * 分页查询已映射的供应商酒店id
     *
     * @param hotelMappingPageReqVO
     * @return
     */
    List<HotelMappingPageRespDTO> querySpHotelIdHotelMapping(@Valid HotelMappingPageReqVO hotelMappingPageReqVO);

    /**
     * 获得酒店映射列表
     *
     * @param reqVO 查询条件
     * @return 酒店映射列表
     */
    List<HotelMappingDO> hotelMappingList(@Valid HotelMappingQueryReqVO reqVO);

    /**
     * 获得酒店映射列表
     *
     * @param reqVO 请求参数
     * @return
     */
    List<HotelMappingDO> hotelMappingListBySupplyClassAndSpHotelId(@Valid HotelMappingQueryByArgReqVO reqVO);

    /**
     * 获得酒店映射分页
     *
     * @param reqVO 查询条件
     * @return 酒店映射分页
     */
    PageResult<HotelMappingDO> hotelMappingPage(@Valid HotelMappingPageReqVO reqVO);

    /**
     * 获得酒店映射分页
     *
     * @param reqVO 查询条件
     * @return 酒店映射分页
     */
    PageResult<HotelMappingDO> hotelMappingPageToUpdate(@Valid HotelMappingPageReqVO reqVO);


    /**
     * 分页查询指定供应商未映射酒店
     *
     * @param reqVO 查询条件
     * @return 供应商酒店基础信息
     */
    PageResult<SupplyHotelRespVO> queryNotMappingHotel(@Valid HotelMappingPageReqVO reqVO);


    /**
     * 查询指定供应商未映射酒店
     *
     * @param reqVO 查询条件
     * @return 供应商酒店基础信息
     * <p>
     * 此接口返回数据和queryNotMappingHotel一样，只是没有分页插件。
     * 是否存在下一页，根据使用方式区分
     */
    List<SupplyHotelRespVO> queryNotMappingHotelList(@Valid HotelMappingPageReqVO reqVO);

    /**
     * 查询指定供应商未映射酒店数量
     *
     * @param reqVO 查询条件
     * @return 未映射酒店数量
     */
    Integer queryNotMappingHotelSize(@Valid HotelMappingPageReqVO reqVO);

    /**
     * 查询指定供应商未映射酒店id列表
     *
     * @param reqVo
     * @return
     */
    List<SupplyHotelRespVO> queryNotMappingHotelIdList(HotelMappingPageReqVO reqVo);


    /**
     * 查询酒店映射并分页
     *
     * @param reqVO 分页请求
     * @return 分页结果
     */
    PageResult<HotelMappingRespVO> queryHotelMappingPage(@Valid HotelMappingPageReqVO reqVO);


    /**
     * 查询酒店映射 单个
     *
     * @param reqVO
     * @return
     */
    HotelMappingRespVO queryHotelMapping(@Valid HotelMappingQueryReqVO reqVO);

    /**
     * 批量更新酒店映射国家省份编码
     *
     * @param hotelMappingEdit
     */
    void hotelMappingEditBatch(@Valid HotelMappingEdit hotelMappingEdit);
}
