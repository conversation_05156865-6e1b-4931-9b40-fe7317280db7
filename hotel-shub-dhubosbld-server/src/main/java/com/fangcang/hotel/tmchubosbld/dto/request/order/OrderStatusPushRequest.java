package com.fangcang.hotel.tmchubosbld.dto.request.order;

import lombok.Data;

@Data
public class OrderStatusPushRequest {

    /**
     * 合作商订单号
     */
    private String coOrderCode;

    /**
     * 房仓订单号
     */
    private String fcOrderCode;

    /**
     * 订单状态 3-确认，4-拒绝 6-取消成功;7-取消失败
     */
    private Integer orderStatus;

    /**
     * 酒店订单确认号
     */
    private String hotelConfirmNo;

    /**
     * 拒单原因返回码
     */
    private String refuseCode;

    /**
     * 拒单原因：
     * 001-满房
     * 002-变价
     * 003-酒店被征用
     * 004-酒店暂停营业
     * 005-分销商要求取消
     * 006-其它
     */
    private String message;


}

