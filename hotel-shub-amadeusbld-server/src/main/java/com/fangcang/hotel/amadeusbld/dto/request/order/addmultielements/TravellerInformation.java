package com.fangcang.hotel.amadeusbld.dto.request.order.addmultielements;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/28 16:59
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "travellerInformation")
public class TravellerInformation {

    @XmlElement(name = "traveller")
    private Traveller traveller;

    @XmlElement(name = "passenger")
    private Passenger passenger;

    public Traveller getTraveller() {
        return traveller;
    }

    public void setTraveller(Traveller traveller) {
        this.traveller = traveller;
    }

    public Passenger getPassenger() {
        return passenger;
    }

    public void setPassenger(Passenger passenger) {
        this.passenger = passenger;
    }
}
