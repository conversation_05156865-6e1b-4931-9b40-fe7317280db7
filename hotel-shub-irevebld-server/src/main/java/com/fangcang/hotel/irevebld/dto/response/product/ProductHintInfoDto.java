package com.fangcang.hotel.irevebld.dto.response.product;

import com.fangcang.hotel.irevebld.enums.IREVEBLDDateShowTypeEnum;
import com.fangcang.hotel.irevebld.enums.IREVEBLDGiveTypeEnum;
import com.fangcang.hotel.irevebld.enums.IREVEBLDHintInfoTypeEnum;
import com.fangcang.hotel.irevebld.enums.IREVEBLDShowTypeEnum;
import lombok.Data;

import java.util.Date;

@Data
public class ProductHintInfoDto {
    /**
     * 产品Id
     */
    private Integer productId;
    /**
     * 信息类型(0提示信息 1促销信息 2内部提示)
     * @see IREVEBLDHintInfoTypeEnum
     */
    private Integer infoType;
    /**
     * 备注内容
     */
    private String content;
    /**
     * 展示类型(0一直展示 1指定日期内展示)
     * @see IREVEBLDShowTypeEnum
     */
    private Integer showType;
    /**
     * 日期展示类型(0下单日期 1入住日期 2下单+入住日期)
     * @see IREVEBLDDateShowTypeEnum
     */
    private Integer dateShowType;
    /**
     * 信息标题
     */
    private String infoTitle;
    /**
     * 赠送类型(0每房送一次 1每房每日送)
     * @see IREVEBLDGiveTypeEnum
     */
    private Integer giveType;
    /**
     * 开始日期
     */
    private Date startDate;
    /**
     * 结束日期
     */
    private Date endDate;
}
