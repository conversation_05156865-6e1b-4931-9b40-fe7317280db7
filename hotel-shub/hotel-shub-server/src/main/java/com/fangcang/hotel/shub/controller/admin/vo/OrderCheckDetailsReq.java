package com.fangcang.hotel.shub.controller.admin.vo;

import com.fangcang.hotel.data.api.dto.BaseReq;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @description: 查询供应商入住明细请求参数
 * @author: qiu
 * @create: 2023-12-03 14:27
 */
@Data
public class OrderCheckDetailsReq extends BaseReq {

    /** 商家订单号 */
    @NotBlank(message = "商家订单号不能为空")
    private String merchantOrderCode;

    /** 供应商订单号 */
    private String supplyOrderCode;
}