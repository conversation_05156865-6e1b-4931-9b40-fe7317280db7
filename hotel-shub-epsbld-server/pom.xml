<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mongoso.cloud</groupId>
        <artifactId>supply-hub-project</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>hotel-shub-epsbld-server</artifactId>
    <description>
        对接国内eps供应商接口项目
    </description>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <repositories>
        <repository>
            <id>central</id>
            <name>Maven Central</name>
            <layout>default</layout>
            <url>https://repo.maven.apache.org/maven2</url>
        </repository>
    </repositories>
    <dependencies>
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>mgs-starter-web</artifactId>
        </dependency>
        <!-- Web 相关 -->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-web</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--引入nacos config配置依赖-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--  使用spring cloud的负载均衡  -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.expediagroup.openworld.sdk</groupId>-->
<!--            <artifactId>openworld-java-sdk-rapid</artifactId>-->
<!--            <version>3.0.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.expediagroup</groupId>
            <artifactId>rapid-sdk</artifactId>
            <version>5.0.1</version>
        </dependency>
        <!-- eps 的sdk 要导入这个依赖包，不然启动不起来       -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>1.9.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.hibernate.validator/hibernate-validator -->
<!--        <dependency>-->
<!--            <groupId>org.hibernate.validator</groupId>-->
<!--            <artifactId>hibernate-validator</artifactId>-->
<!--            <version>7.0.1.Final</version>-->
<!--        </dependency>-->


        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>hotel-shub-common-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>hotel-shub-data-base</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.21.graal</version>
        </dependency>
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>mgs-starter-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>hotel-shub-data-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.maxmind.geoip2</groupId>
            <artifactId>geoip2</artifactId>
            <version>2.15.0</version>
        </dependency>

        <!-- 汉字转拼音 -->
        <dependency>
            <groupId>com.github.stuxuhai</groupId>
            <artifactId>jpinyin</artifactId>
            <version>1.1.8</version>
        </dependency>
    </dependencies>

    <build>
        <!--包名-->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.8</version> <!-- 如果 spring.boot.version 版本修改，则这里也要跟着修改 -->
                <configuration>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>pfx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>crt</nonFilteredFileExtension>
                        <nonFilteredFileExtension>cer</nonFilteredFileExtension>
                        <nonFilteredFileExtension>key</nonFilteredFileExtension>
                        <nonFilteredFileExtension>dll</nonFilteredFileExtension>
                        <nonFilteredFileExtension>so</nonFilteredFileExtension>
                        <nonFilteredFileExtension>mmdb</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <!-- 指定配置文件所在的resource目录 -->
                <directory>src/main/resources</directory>
                <!--打包包含的文件-->
                <includes>
                    <include>logback-spring.xml</include>
                    <include>application.yml</include>
                    <include>bootstrap.yml</include>
                    <include>bootstrap-*.yml</include>
                    <include>geo/*</include>
                </includes>
                <!--打包需要排除的文件-->
                <excludes>
                    <exclude>test/*</exclude>
                </excludes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <!-- 指定配置文件所在的resource目录 -->
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>**/**</exclude>
                </excludes>
            </resource>

        </resources>
    </build>
</project>