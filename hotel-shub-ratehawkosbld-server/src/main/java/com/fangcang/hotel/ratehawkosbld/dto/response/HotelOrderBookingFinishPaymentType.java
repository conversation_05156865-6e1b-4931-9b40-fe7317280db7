package com.fangcang.hotel.ratehawkosbld.dto.response;

import java.math.BigDecimal;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  预定付款信息
 *
 * <AUTHOR>
 * @date 2024-05-30 17:04:51
 */
@Data
public class HotelOrderBookingFinishPaymentType implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 付款类型 (now, hotel, deposit)
     */
    @JSONField(name = "type")
    private String type;

    /**
     * 金额
     */
    @JSONField(name = "amount")
    private BigDecimal amount;

    /**
     * 币种
     */
    @JSONField(name = "currency_code")
    private String currencyCode;

    /**
     * 预订付款支票的通用唯一标识符 （UUID4） 令牌
     */
    @JSONField(name = "init_uuid")
    private String initUuid;

    /**
     * 预订付款支票的通用唯一标识符 （UUID4） 令牌
     */
    @JSONField(name = "pay_uuid")
    private String payUuid;

}
