package com.fangcang.hotel.tmchubosbld.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fangcang.hotel.common.api.common.mapping.dto.RoomMappingRespDTO;
import com.fangcang.hotel.common.api.common.order.SupplyOrderApi;
import com.fangcang.hotel.common.api.common.order.dto.SupplyOrderQueryReqDTO;
import com.fangcang.hotel.common.api.common.order.dto.SupplyOrderRespDTO;
import com.fangcang.hotel.common.api.common.order.dto.SupplyOrderUpdateDTO;
import com.fangcang.hotel.core.enums.GlobalErrorCodeEnum;
import com.fangcang.hotel.data.api.dto.*;
import com.fangcang.hotel.data.api.dto.order.OrderCheckDetailPriceItem;
import com.fangcang.hotel.data.api.dto.order.*;
import com.fangcang.hotel.data.api.dto.order.cancel.CancelSupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.cancel.CancelSupplyOrderResponse;
import com.fangcang.hotel.data.api.dto.order.create.CreateSupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.create.CreateSupplyOrderResponse;
import com.fangcang.hotel.data.api.dto.order.prebooking.PreBookingSupplyRequest;
import com.fangcang.hotel.data.api.dto.order.prebooking.PreBookingSupplyResponse;
import com.fangcang.hotel.data.api.dto.order.query.QuerySupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.query.QuerySupplyOrderResponse;
import com.fangcang.hotel.data.api.enums.CurrencyEnum;
import com.fangcang.hotel.data.api.enums.OrderStatusEnum;
import com.fangcang.hotel.data.api.enums.RoomStateEnum;
import com.fangcang.hotel.data.api.enums.SupplyClassEnum;
import com.fangcang.hotel.data.api.service.SupplyOrderService;
import com.fangcang.hotel.data.base.services.CacheService;
import com.fangcang.hotel.framework.common.domain.ResultX;
import com.fangcang.hotel.framework.common.exception.BizException;
import com.fangcang.hotel.framework.common.util.CollUtilX;
import com.fangcang.hotel.framework.common.util.DateUtilX;
import com.fangcang.hotel.framework.common.util.StrUtilX;
import com.fangcang.hotel.framework.operatelog.auto.SlsLogger;
import com.fangcang.hotel.framework.operatelog.core.sls.SlsEnum;
import com.fangcang.hotel.tmchubosbld.config.TMCHUBOSBLDConfig;
import com.fangcang.hotel.tmchubosbld.config.TMCHUBOSBLDExtendConfig;
import com.fangcang.hotel.tmchubosbld.constants.TMCHUBOSBLDConstant;
import com.fangcang.hotel.tmchubosbld.convert.TmcHubConvert;
import com.fangcang.hotel.tmchubosbld.dto.entity.AccountReq;
import com.fangcang.hotel.tmchubosbld.dto.entity.ResponseResult;
import com.fangcang.hotel.tmchubosbld.dto.request.order.*;
import com.fangcang.hotel.tmchubosbld.dto.response.order.*;
import com.fangcang.hotel.tmchubosbld.enums.TMCHUBOSBLDErrorResultEnum;
import com.fangcang.hotel.tmchubosbld.enums.TMCHUBOSBLDOrderStatusEnum;
import com.fangcang.hotel.tmchubosbld.enums.TMCHUBOSBLDPushOrderStatusEnum;
import com.fangcang.hotel.tmchubosbld.manger.TMCHUBOSBLDManager;
import com.fangcang.hotel.tmchubosbld.util.TMCHUBOSBLDUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单服务实现类
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@Slf4j
@Service("tmchubosbldOrderService")
public class TMCHUBOSBLDOrderServiceImpl implements SupplyOrderService {

    @Autowired
    private TMCHUBOSBLDManager tmchubosbldManager;

    @Autowired
    private TMCHUBOSBLDConfig tmchubosbldConfig;

    @Autowired
    private CacheService<TMCHUBOSBLDExtendConfig> cacheService;

    @Autowired
    private SupplyOrderApi supplyOrderApi;

    @Autowired
    private SlsLogger slsLogger;

    @Override
    public ResultX<PreBookingSupplyResponse> preBooking(PreBookingSupplyRequest preBookingSupplyRequest) throws Exception {
        // 入参校验
        if (CollUtilX.isEmpty(preBookingSupplyRequest.getRoomGuestNumbers())) {
            log.error("preBooking======tmchubosbld不落地供应商试预订接口，缺少必要参数！preBookingRequest:" + JSONUtil.toJsonStr(preBookingSupplyRequest));
            return ResultX.error(GlobalErrorCodeEnum.MISSING_PARAM);
        }

        // 获取缓存配置信息
        String cacheConfigExtend = cacheService.getCacheConfigExtend(preBookingSupplyRequest.getMerchantSource(), preBookingSupplyRequest.getMerchantCode(),
                SupplyClassEnum.TMCHUBOSBLD, preBookingSupplyRequest.getSupplyCode());
        TMCHUBOSBLDExtendConfig TMCHUBOSBLDExtendConfig = JSONUtil.toBean(cacheConfigExtend, TMCHUBOSBLDExtendConfig.class);

        if (TMCHUBOSBLDExtendConfig == null) {
            log.error("preBooking======tmchubosbld不落地供应商试预订接口，没有找到商家配置！preBookingRequest:" + JSONUtil.toJsonStr(preBookingSupplyRequest));
            return ResultX.error(GlobalErrorCodeEnum.SUPPLIER_NOT_CONFIGURED);
        }
        PreBookingSupplyResponse preBookingSupplyResponse = new PreBookingSupplyResponse();
        RoomMappingRespDTO roomMapping = preBookingSupplyRequest.getRoomMapping();

        // 组装试预订参数
        CheckBookingInfoRequestBase checkBookingRequest = new CheckBookingInfoRequestBase();
        checkBookingRequest.setHotelId(Long.valueOf(preBookingSupplyRequest.getHotelId()));
        //价格计划ID = 供应商编码-价格计划ID
        String oldSupplyRateId = preBookingSupplyRequest.getSupplyRateId();
        String[] split = preBookingSupplyRequest.getSupplyRateId().split("-");

        checkBookingRequest.setSupplyCode(split[0]);
        String supplyRateId = oldSupplyRateId.substring(oldSupplyRateId.indexOf("-") + 1, oldSupplyRateId.length());
        checkBookingRequest.setRatePlanId(supplyRateId);
        checkBookingRequest.setRoomId(Long.valueOf(roomMapping.getSpRoomId()));
        checkBookingRequest.setCheckInDate(DateUtilX.dateToString(preBookingSupplyRequest.getCheckInDate()));
        checkBookingRequest.setCheckOutDate(DateUtilX.dateToString(preBookingSupplyRequest.getCheckOutDate()));
        checkBookingRequest.setRoomNum(preBookingSupplyRequest.getRoomNum());
        checkBookingRequest.setRoomGuestNumber(TMCHUBOSBLDUtil.getRoomGuestNumber(preBookingSupplyRequest.getRoomGuestNumbers().get(0)));


        // 设置账号参数
        AccountReq accountReq = new AccountReq();
        accountReq.setPartnerCode(TMCHUBOSBLDExtendConfig.getPartnerCode());// 合作商编码
        accountReq.setSecurityKey(TMCHUBOSBLDExtendConfig.getSignature());// 合作商秘钥

        // 调用供应商接口
        preBookingSupplyResponse.setRequestContent("checkBookingRequest=" + JSONUtil.toJsonStr(checkBookingRequest) + ";accountReq=" + JSONUtil.toJsonStr(accountReq));
        preBookingSupplyResponse.setRequestTime(new Date());
        ResponseResult<CheckBookingInfoResponse> checkBookingResult = tmchubosbldManager.checkBooking(checkBookingRequest, accountReq);
        preBookingSupplyResponse.setResponseTime(new Date());
        preBookingSupplyResponse.setResponseContent(JSONUtil.toJsonStr(checkBookingResult));

        // 4、处理返回结果
        if (checkBookingResult.isError()) {
            // todo 转换错误信息
            log.error("preBooking======tmchubosbld不落地供应商试预订接口，请求供应商接口失败，checkBookingRequest={}, accountReq={}, checkBookingResult={}",
                    JSONUtil.toJsonStr(checkBookingRequest), JSONUtil.toJsonStr(accountReq), JSONUtil.toJsonStr(checkBookingResult));
            return convertPreBookingErrorResult(checkBookingResult.getReturnCode(), checkBookingResult.getReturnMsg());
        }
        CheckBookingInfoResponse checkBookingResponse = checkBookingResult.getBussinessResponse();
        if (checkBookingResponse == null) {
            log.error("preBooking======tmchubosbld不落地供应商试预订接口，请求供应商接口返回数据为空，checkBookingRequest={}, accountReq={}, checkBookingResult={}",
                    JSONUtil.toJsonStr(checkBookingRequest), JSONUtil.toJsonStr(accountReq), JSONUtil.toJsonStr(checkBookingResult));
            return ResultX.error(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR);
        }
        preBookingSupplyResponse.setCanBook(checkBookingResponse.getCanBook());// 是否可订
        preBookingSupplyResponse.setHotelId(preBookingSupplyRequest.getHotelId());// 酒店ID
        preBookingSupplyResponse.setRoomId(roomMapping.getRoomId());// 房型ID

        preBookingSupplyResponse.setSupplyRateId(oldSupplyRateId);// 价格计划ID
        preBookingSupplyResponse.setSupplyCode(preBookingSupplyRequest.getSupplyCode());// 供应商编码
        preBookingSupplyResponse.setCheckInDate(DateUtilX.dateToString(preBookingSupplyRequest.getCheckInDate()));// 入住日期
        preBookingSupplyResponse.setCheckOutDate(DateUtilX.dateToString(preBookingSupplyRequest.getCheckOutDate()));// 离店日期
        preBookingSupplyResponse.setCanImmediate(0);// 是否即时确认(1:即时确认 0：非即时确认)
        // 设置每日详情
        if (CollUtilX.isNotEmpty(checkBookingResponse.getPriceItems())) {
            List<OrderRoomDetailDTO> roomDetailList = new ArrayList<>();
            for (RoomGuestNumberDTO roomGuestNumber : preBookingSupplyRequest.getRoomGuestNumbers()) {
                OrderRoomDetailDTO orderRoomDetailDTO = new OrderRoomDetailDTO();
                orderRoomDetailDTO.setAdultNum(roomGuestNumber.getAdultNum());
                orderRoomDetailDTO.setRoomIndex(roomGuestNumber.getRoomIndex());
                // 组装儿童年龄，用","拼接
                if (CollUtilX.isNotEmpty(roomGuestNumber.getChildrenInfos())) {
                    StringBuilder childAges = new StringBuilder();
                    for (ChildrenInfoDTO childrenInfo : roomGuestNumber.getChildrenInfos()) {
                        childAges.append(childrenInfo.getChildrenAge()).append(",");
                    }
                    String childAgesStr = childAges.toString();
                    orderRoomDetailDTO.setChildAges(childAgesStr.substring(0, childAgesStr.length() - 1));
                }
                List<PriceInfoDetail> priceInfoDetails = checkBookingResponse.getPriceItems().stream().map(item -> {
                    PriceInfoDetail priceInfoDetail = new PriceInfoDetail();
                    priceInfoDetail.setSaleDate(DateUtilX.stringToDate(item.getSaleDate()));// 售卖日期

                    //设置房费和税费
                    TaxDetailsDTO taxDetailsDTO = new TaxDetailsDTO();
                    BigDecimal salePrice = item.getSalePrice();
                    BigDecimal roomPrice = salePrice.divide(new BigDecimal(String.valueOf(tmchubosbldConfig.getTaxPercentage())), 2, RoundingMode.HALF_UP);

                    taxDetailsDTO.setRoomPrice(roomPrice);// 房费
                    taxDetailsDTO.setOtherTax(salePrice.subtract(roomPrice));// 其他税费=税后价格-房费
                    priceInfoDetail.setTaxDetail(taxDetailsDTO);

                    priceInfoDetail.setBasePrice(item.getSalePrice());// 售价
                    priceInfoDetail.setNeedQuery(0);// 是否待查(1:待查 0：非待查)
                    priceInfoDetail.setBreakfastType(item.getBreakfastType());// 早餐类型
                    priceInfoDetail.setBreakfastNum(item.getBreakfastNum());// 早餐数量
                    priceInfoDetail.setCanbook(checkBookingResponse.getCanBook());// 每日是否可定订 1:可定 0：不可订

                    if (checkBookingResponse.getCanBook() != null && checkBookingResponse.getCanBook() == 1 && (item.getQuotaNum() == null || item.getQuotaNum() <= 0)) {
                        priceInfoDetail.setQuotaNum(0);//配额0
                        priceInfoDetail.setCanOverDraft(1);//可超
                        priceInfoDetail.setRoomStatus(RoomStateEnum.HAVA_ROOM.key);//有房
                        priceInfoDetail.setQuotaNum(0);// 可售房间数量
                    }else {
                        priceInfoDetail.setQuotaNum(item.getQuotaNum());// 可售房间数量
                        priceInfoDetail.setRoomStatus(item.getRoomStatus());// 房态
                        priceInfoDetail.setCanOverDraft(0);// 是否可超(1：可超 0：不可超)
                    }

                    return priceInfoDetail;
                }).collect(Collectors.toList());
                orderRoomDetailDTO.setPriceInfoDetails(priceInfoDetails);
                roomDetailList.add(orderRoomDetailDTO);// 每间每晚的数据
            }
            preBookingSupplyResponse.setOrderRoomDetails(roomDetailList);// 每一天的试预定结果详情
            preBookingSupplyResponse.setBaseCurrency(CurrencyEnum.ONE.key);// 售价币种
        }

        return ResultX.success(preBookingSupplyResponse);
    }

    /**
     * 转换试预订错误结果
     */
    private static ResultX<PreBookingSupplyResponse> convertPreBookingErrorResult(String code, String msg) {
        if (StrUtilX.isNotEmpty(code)) {
            // 无效参数
            if (code.contains("EMPTY") || code.contains("NOT_NULL") || code.contains("INVALID")) {
                return ResultX.error(GlobalErrorCodeEnum.MISSING_PARAM);
            }
        }
        return ResultX.error(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION,msg);
    }

    @Override
    public ResultX<CreateSupplyOrderResponse> createSupplierOrder(CreateSupplyOrderRequest createOrderRequest) throws Exception {
        // 入参校验
        if (CollUtilX.isEmpty(createOrderRequest.getRoomGuestNumbers())) {
            log.error("createSupplierOrder======tmchubosbld不落地供应商试预订接口，缺少必要参数！createOrderRequest:" + JSONUtil.toJsonStr(createOrderRequest));
            return ResultX.error(GlobalErrorCodeEnum.MISSING_PARAM);
        }

        // 1、获取缓存配置信息
        String cacheConfigExtend = cacheService.getCacheConfigExtend(createOrderRequest.getMerchantSource(), createOrderRequest.getMerchantCode(),
                SupplyClassEnum.TMCHUBOSBLD, createOrderRequest.getSupplyCode());
        TMCHUBOSBLDExtendConfig TMCHUBOSBLDExtendConfig = JSONUtil.toBean(cacheConfigExtend, TMCHUBOSBLDExtendConfig.class);

        if (TMCHUBOSBLDExtendConfig == null) {
            log.error("createSupplierOrder======tmchubosbld不落地供应商创建订单接口，没有找到商家配置！createOrderRequest:" + JSONUtil.toJsonStr(createOrderRequest));
            return ResultX.error(GlobalErrorCodeEnum.SUPPLIER_NOT_CONFIGURED);
        }
        CreateSupplyOrderResponse createSupplyOrderResponse = new CreateSupplyOrderResponse();

        // 2、组装查询订单参数
        // 2.1、设置业务参数
        CreateOrderInfoRequestBase createOrderReq = new CreateOrderInfoRequestBase();
        createOrderReq.setHotelId(Long.valueOf(createOrderRequest.getHotelId()));// 酒店id
        createOrderReq.setRoomId(Long.valueOf(createOrderRequest.getRoomMapping().getSpRoomId()));// 房型id
        String oldSupplyRateId = createOrderRequest.getSupplyRateId();

        String[] split = createOrderRequest.getSupplyRateId().split("-");
        String supplyRateId = oldSupplyRateId.substring(oldSupplyRateId.indexOf("-") + 1, oldSupplyRateId.length());
        createOrderReq.setSupplyCode(split[0]);// 供应商编码
        createOrderReq.setRatePlanId(supplyRateId);// 产品id
        // 设置入住人
        if (CollUtilX.isNotEmpty(createOrderRequest.getGuests())) {
            List<GuestInfo> guestList = new ArrayList<>();
            for (GuestDTO guest : createOrderRequest.getGuests()) {
                GuestInfo guestInfo = new GuestInfo();
                guestInfo.setGuestName(guest.getGuestName());
                guestInfo.setFirstName(guest.getFirstName());
                guestInfo.setLastName(guest.getLastName());
                guestInfo.setRoomNumber(guest.getRoomIndex().toString());
                guestInfo.setMobileNo(guest.getPhone());
                guestInfo.setIdCardNo(guest.getIdCardNo());
                guestInfo.setIdCardType(guest.getIdCardType());
                guestInfo.setMembershipCardNumber(guest.getMembershipCardNumber());
                guestList.add(guestInfo);
            }
            createOrderReq.setGuestInfos(guestList);
        }

        createOrderReq.setTotalAmount(createOrderRequest.getTotalAmount());// 总金额
        createOrderReq.setRemark(createOrderRequest.getRemark());// 备注
        createOrderReq.setLinkMan(createOrderRequest.getLinkMan());// 订单联系人
        createOrderReq.setLinkPhone(createOrderRequest.getLinkPhone());// 联系电话
        createOrderReq.setEmail(createOrderRequest.getLinkEmail());// 邮箱
        createOrderReq.setCoOrderCode(createOrderRequest.getMerchantOrderCode());// 合作商订单号
        createOrderReq.setArriveTime(createOrderRequest.getArriveTime());// 最早时间
        createOrderReq.setLatestArriveTime(createOrderRequest.getLatestArriveTime());// 最晚时间
        createOrderReq.setCheckInDate(DateUtilX.dateToString(createOrderRequest.getCheckInDate()));// 入住日期
        createOrderReq.setCheckOutDate(DateUtilX.dateToString(createOrderRequest.getCheckOutDate()));// 离店日期
        createOrderReq.setRoomNum(createOrderRequest.getRoomNum());// 间数
        createOrderReq.setRemark(createOrderRequest.getRemark());// 备注
//        createOrderReq.setPayStatus(1);//支付状态：0待支付 1已支付
        createOrderReq.setTravelType(createOrderRequest.getTravelType());// 出行类型
        createOrderReq.setGuaranteeFlag(createOrderRequest.getGuaranteeFlag());// 担保类型
        // 设置产品每日详情
        Integer breakfastNum = null;
        List<CreateOrderPriceItem> priceItems = new ArrayList<>();
        for (PriceInfoDetail priceInfoDetail : createOrderRequest.getPriceInfoDetails()) {
            CreateOrderPriceItem priceItem = new CreateOrderPriceItem();
            if (priceInfoDetail.getBreakfastNum() != null) {
                if (breakfastNum == null) {
                    breakfastNum = priceInfoDetail.getBreakfastNum();
                } else {
                    // 早餐数量不一致
                    if (breakfastNum.compareTo(priceInfoDetail.getBreakfastNum()) != 0) {
                        throw new BizException(GlobalErrorCodeEnum.BREAKFAST_NUM_UNLIKE);
                    }
                }
            }
            priceItem.setBreakfastNum(priceInfoDetail.getBreakfastNum());// 早餐数量
            priceItem.setBreakfastType(priceInfoDetail.getBreakfastType());// 早餐类型
            priceItem.setSaleDate(DateUtilX.dateToString(priceInfoDetail.getSaleDate()));// 日期
            if (createOrderRequest.getRoomNum() != null && createOrderRequest.getRoomNum() > 1) {
                priceItem.setSalePrice(priceInfoDetail.getBasePrice().divide(new BigDecimal(createOrderRequest.getRoomNum()), 2, RoundingMode.HALF_UP));// 价格
            } else {
                priceItem.setSalePrice(priceInfoDetail.getBasePrice());// 价格
            }

            priceItems.add(priceItem);
        }
        createOrderReq.setPriceItems(priceItems);


        // 设置账号
        AccountReq accountReq = new AccountReq();
        accountReq.setPartnerCode(TMCHUBOSBLDExtendConfig.getPartnerCode());// 合作商编码
        accountReq.setSecurityKey(TMCHUBOSBLDExtendConfig.getSignature());// 合作商秘钥

        // 调用供应商接口
        createSupplyOrderResponse.setRequestContent("createOrderReq=" + JSONUtil.toJsonStr(createOrderReq) + ";accountReq=" + JSONUtil.toJsonStr(accountReq));
        createSupplyOrderResponse.setRequestTime(new Date());
        ResponseResult<CreateOrderInfoResponse> createOrderResult = tmchubosbldManager.createOrder(createOrderReq, accountReq);
        createSupplyOrderResponse.setResponseTime(new Date());
        createSupplyOrderResponse.setResponseContent(JSONUtil.toJsonStr(createOrderResult));

        // 处理返回结果
        if (createOrderResult.isError()) {
            log.error("createSupplierOrder======tmchubosbld不落地供应商创建订单接口，请求供应商接口失败，createOrderReq={}, accountReq={}, createOrderResult={}",
                    JSONUtil.toJsonStr(createOrderReq), JSONUtil.toJsonStr(accountReq), JSONUtil.toJsonStr(createOrderResult));
            return convertCreateSupplierOrderErrorResult(createOrderResult.getReturnCode(), createOrderResult.getReturnMsg());
        }
        CreateOrderInfoResponse createOrderResponse = createOrderResult.getBussinessResponse();
        if (createOrderResponse == null) {
            log.error("createSupplierOrder======tmchubosbld不落地供应商创建订单接口，请求供应商接口返回数据为空，createOrderReq={}, accountReq={}, createOrderResult={}",
                    JSONUtil.toJsonStr(createOrderReq), JSONUtil.toJsonStr(accountReq), JSONUtil.toJsonStr(createOrderResult));
            return ResultX.error(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR);
        }
        if (createOrderResponse.getResult() == 2) {
            log.error("createSupplierOrder======tmchubosbld不落地供应商创建订单接口，请求供应商接口返回预订失败，createOrderReq={}, accountReq={}, createOrderResult={}",
                    JSONUtil.toJsonStr(createOrderReq), JSONUtil.toJsonStr(accountReq), JSONUtil.toJsonStr(createOrderResult));
            return ResultX.error(GlobalErrorCodeEnum.SUPPLY_CREATE_ORDER_FIAIL, createOrderResponse.getMessage());
        }
        // 发起支付 
        PayOrderInfoRequestBase payOrderInfoRequest = new PayOrderInfoRequestBase();
        payOrderInfoRequest.setCoOrderCode(createOrderResponse.getCoOrderCode());
        payOrderInfoRequest.setFcOrderCode(createOrderResponse.getFcOrderCode());

        ResponseResult<PayOrderInfoResponse> payOrder = tmchubosbldManager.payOrder(payOrderInfoRequest, accountReq);
        if (payOrder == null || payOrder.getBussinessResponse() == null) {
            log.error("payOrder======tmchubosbld不落地供应商创建订单接口成功，请求供应商支付接口返回数据为空，payOrder={}, accountReq={}, ResponseResult={}",
                    JSONUtil.toJsonStr(payOrderInfoRequest), JSONUtil.toJsonStr(accountReq), JSONUtil.toJsonStr(payOrder));
            return ResultX.error(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR);
        }else if (payOrder.isError()) {
            log.error("payOrder======tmchubosbld不落地供应商创建订单接口成功，支付失败，payOrder={}, accountReq={}, ResponseResult={}",
                    JSONUtil.toJsonStr(payOrderInfoRequest), JSONUtil.toJsonStr(accountReq), JSONUtil.toJsonStr(payOrder));
            return convertCreateSupplierOrderErrorResult(createOrderResult.getReturnCode(), createOrderResult.getReturnMsg());
        }else if(payOrder.getBussinessResponse().getPayStatus() == 2){
            log.error("payOrder======tmchubosbld不落地供应商创建订单接口成功，请求供应商接口返回支付失败，payOrder={}, accountReq={}, ResponseResult={}",
                    JSONUtil.toJsonStr(payOrderInfoRequest), JSONUtil.toJsonStr(accountReq), JSONUtil.toJsonStr(payOrder));
            return ResultX.error(GlobalErrorCodeEnum.SUPPLY_CREATE_ORDER_FIAIL, createOrderResponse.getMessage());
        }
        createSupplyOrderResponse.setSupplyOrderCode(createOrderResponse.getFcOrderCode());// 供应商订单号
        createSupplyOrderResponse.setMerchantOrderCode(createOrderResponse.getCoOrderCode());// 合作商订单号
        createSupplyOrderResponse.setSupplyOrderStatus(TMCHUBOSBLDOrderStatusEnum.PROCESSING.getKey()+"");// 供应商订单状态
        createSupplyOrderResponse.setOrderStatus(OrderStatusEnum.NEED_CONFIRM.getResult());// 订单状态
        return ResultX.success(createSupplyOrderResponse);
    }

    /**
     * 转换下单错误结果
     */
    private static ResultX<CreateSupplyOrderResponse> convertCreateSupplierOrderErrorResult(String code, String msg) {
        if (StrUtilX.isNotEmpty(code)) {
            // 产品不存在
            if (TMCHUBOSBLDErrorResultEnum.HUB_INVALID_PRICEPLANID.getCode().equals(code)) {
                return ResultX.error(GlobalErrorCodeEnum.PRODUCT_NOT_FOUND);
            }
            // 不满足条款
            if (TMCHUBOSBLDErrorResultEnum.CENTER_ERROR_RESTRICT.getCode().equals(code)) {
                if (StrUtilX.isNotEmpty(msg)) {
                    if (msg.contains("条款间数")) {
                        return ResultX.error(GlobalErrorCodeEnum.SUPPLY_DISSATISFY_BOOKROOMSCLAUSE);
                    }
                    if (msg.contains("不满足条款时间")) {
                        return ResultX.error(GlobalErrorCodeEnum.SUPPLY_ADVANCE_BOOKING_IN_ADVANCE_DAY);
                    }
                }
                return ResultX.error(GlobalErrorCodeEnum.SUPPLY_PRE_DISSATISFY_BOOKINGCLAUSE);
            }
            // 配额不足或满房
            if (TMCHUBOSBLDErrorResultEnum.HUB_FULL_ROOM.getCode().equals(code)) {
                return ResultX.error(GlobalErrorCodeEnum.SUPPLY_CREATE_QUOTA_ENOUGH);
            }
            // 订单已存在
            if (TMCHUBOSBLDErrorResultEnum.HUB_ORDER_HAVE_EXISTED.getCode().equals(code)) {
                return ResultX.error(GlobalErrorCodeEnum.ORDER_REPETITION);
            }
            // 价格不对
            if (TMCHUBOSBLDErrorResultEnum.HUB_CHARGE_PRICE.getCode().equals(code) || TMCHUBOSBLDErrorResultEnum.HUB_SUPPLY_ORDER_PRICE_CHECK_FAIL.getCode().equals(code)) {
                return ResultX.error(GlobalErrorCodeEnum.CHARGE_PRICE);
            }
            // 无效参数
            if (code.contains("EMPTY") || code.contains("NOT_NULL") || code.contains("INVALID")) {
                return ResultX.error(GlobalErrorCodeEnum.MISSING_PARAM);
            }
        }
        return ResultX.error(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
    }

    @Override
    public ResultX<QuerySupplyOrderResponse> querySupplierOrderStatus(QuerySupplyOrderRequest querySupplyOrderRequest) throws Exception {
        // 获取缓存配置信息
        String cacheConfigExtend = cacheService.getCacheConfigExtend(querySupplyOrderRequest.getMerchantSource(), querySupplyOrderRequest.getMerchantCode(),
                SupplyClassEnum.TMCHUBOSBLD, querySupplyOrderRequest.getSupplyCode());
        TMCHUBOSBLDExtendConfig TMCHUBOSBLDExtendConfig = JSONUtil.toBean(cacheConfigExtend, TMCHUBOSBLDExtendConfig.class);

        if (TMCHUBOSBLDExtendConfig == null) {
            log.error("querySupplierOrderStatus======tmchubosbld不落地供应商查询订单详情接口，没有找到商家配置！querySupplyOrderRequest:" + JSONUtil.toJsonStr(querySupplyOrderRequest));
            return ResultX.error(GlobalErrorCodeEnum.SUPPLIER_NOT_CONFIGURED);
        }
        QuerySupplyOrderResponse querySupplyOrderResponse = new QuerySupplyOrderResponse();

        // 组装查询订单参数
        OrderDetailInfoRequestBase orderReq = new OrderDetailInfoRequestBase();
        orderReq.setFcOrderCode(querySupplyOrderRequest.getSupplyOrderCode());// 供应商订单号
        orderReq.setCoOrderCode(querySupplyOrderRequest.getMerchantOrderCode());// 商家订单号

        // 设置账号参数
        AccountReq accountReq = new AccountReq();
        accountReq.setPartnerCode(TMCHUBOSBLDExtendConfig.getPartnerCode());// 合作商编码
        accountReq.setSecurityKey(TMCHUBOSBLDExtendConfig.getSignature());// 合作商秘钥

        // 调用供应商接口
        querySupplyOrderResponse.setRequestContent("orderReq=" + JSONUtil.toJsonStr(orderReq) + ";accountReq=" + JSONUtil.toJsonStr(accountReq));
        querySupplyOrderResponse.setRequestTime(new Date());
        ResponseResult<OrderDetailInfoResponse> orderDetailResult = tmchubosbldManager.queryOrderDetail(orderReq, accountReq);
        querySupplyOrderResponse.setResponseTime(new Date());
        querySupplyOrderResponse.setResponseContent(JSONUtil.toJsonStr(orderDetailResult));

        // 处理返回结果
        if (orderDetailResult.isError()) {
            // todo 转换错误信息
            log.error("querySupplierOrderStatus======tmchubosbld不落地供应商查询订单详情接口，请求供应商接口失败，orderReq={}, accountReq={}, orderDetailResult={}",
                    JSONUtil.toJsonStr(orderReq), JSONUtil.toJsonStr(accountReq), JSONUtil.toJsonStr(orderDetailResult));
            return convertQuerySupplierOrderStatusErrorResult(orderDetailResult.getReturnCode());
        }
        OrderDetailInfoResponse orderDetailResponse = orderDetailResult.getBussinessResponse();
        if (orderDetailResponse == null) {
            log.error("querySupplierOrderStatus======tmchubosbld不落地供应商查询订单详情接口，请求供应商接口返回数据为空，orderReq={}, accountReq={}, orderDetailResult={}",
                    JSONUtil.toJsonStr(orderReq), JSONUtil.toJsonStr(accountReq), JSONUtil.toJsonStr(orderDetailResult));
            return ResultX.failure(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getNumber(), GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getCode(), "查询订单详情", querySupplyOrderResponse);
        }
        querySupplyOrderResponse.setSupplyOrderCode(orderDetailResponse.getFcOrderCode());// 供应商订单号
        querySupplyOrderResponse.setSupplyConfirmNo(orderDetailResponse.getHotelConfirmNo());// 供应商确认号
        // 设置订单状态
        if (orderDetailResponse.getOrderStatus() != null) {
            querySupplyOrderResponse.setSupplyOrderStatus(String.valueOf(orderDetailResponse.getOrderStatus()));// 供应商订单状态
            querySupplyOrderResponse.setOrderStatus(convertOrderStatus(orderDetailResponse.getOrderStatus()));// shub订单状态
        }
        return ResultX.success(querySupplyOrderResponse);
    }

    /**
     * 转换查询订单错误结果
     */
    private static ResultX<QuerySupplyOrderResponse> convertQuerySupplierOrderStatusErrorResult(String code) {
        if (StrUtilX.isNotEmpty(code)) {
            // 订单不存在
            if (TMCHUBOSBLDErrorResultEnum.ORDER_NOT_EXISTS.getCode().equals(code)) {
                return ResultX.error(GlobalErrorCodeEnum.SUPPLY_ORDER_NOT_EXIST);
            }
            // 无效参数
            if (code.contains("EMPTY") || code.contains("NOT_NULL") || code.contains("INVALID")) {
                return ResultX.error(GlobalErrorCodeEnum.MISSING_PARAM);
            }
        }
        return ResultX.error(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION);
    }

    @Override
    public ResultX<CancelSupplyOrderResponse> cancelSupplierOrder(CancelSupplyOrderRequest cancelSupplyOrderRequest) throws Exception {
        //获取缓存配置信息
        String cacheConfigExtend = cacheService.getCacheConfigExtend(cancelSupplyOrderRequest.getMerchantSource(), cancelSupplyOrderRequest.getMerchantCode(),
                SupplyClassEnum.TMCHUBOSBLD, cancelSupplyOrderRequest.getSupplyCode());
        TMCHUBOSBLDExtendConfig TMCHUBOSBLDExtendConfig = JSONUtil.toBean(cacheConfigExtend, TMCHUBOSBLDExtendConfig.class);

        if (TMCHUBOSBLDExtendConfig == null) {
            log.error("cancelSupplierOrder======tmchubosbld不落地供应商取消订单接口，没有找到商家配置！cancelOrderRequest:" + JSONUtil.toJsonStr(cancelSupplyOrderRequest));
            return ResultX.error(GlobalErrorCodeEnum.SUPPLIER_NOT_CONFIGURED);
        }
        CancelSupplyOrderResponse cancelSupplyOrderResponse = new CancelSupplyOrderResponse();

        //组装取消订单参数
        CancelOrderInfoRequestBase cancelOrderRequest = new CancelOrderInfoRequestBase();
        cancelOrderRequest.setFcOrderCode(cancelSupplyOrderRequest.getSupplyOrderCode());// 供应商订单号
        cancelOrderRequest.setCoOrderCode(cancelSupplyOrderRequest.getMerchantOrderCode());// 商家订单号
        cancelOrderRequest.setCancelReason(cancelSupplyOrderRequest.getCacelReason());// 取消原因

        //设置账号参数
        AccountReq accountReq = new AccountReq();
        accountReq.setPartnerCode(TMCHUBOSBLDExtendConfig.getPartnerCode());// 合作商编码
        accountReq.setSecurityKey(TMCHUBOSBLDExtendConfig.getSignature());// 合作商秘钥

        //调用供应商接口
        cancelSupplyOrderResponse.setRequestContent("orderReq=" + JSONUtil.toJsonStr(cancelOrderRequest) + ";accountReq=" + JSONUtil.toJsonStr(accountReq));
        cancelSupplyOrderResponse.setRequestTime(new Date());
        ResponseResult<CancelOrderInfoResponse> cancelOrderResult = tmchubosbldManager.cancelOrder(cancelOrderRequest, accountReq);
        cancelSupplyOrderResponse.setResponseTime(new Date());
        cancelSupplyOrderResponse.setResponseContent(JSONUtil.toJsonStr(cancelOrderResult));

        cancelSupplyOrderResponse.setSupplyOrderCode(cancelSupplyOrderRequest.getSupplyOrderCode());// 供应商订单号
        cancelSupplyOrderResponse.setSupplierOrderId(cancelSupplyOrderRequest.getMerchantOrderCode());// 供应商订单号
        if (cancelOrderResult.isError()) {
            // todo 转换错误信息
            log.error("cancelSupplierOrder======tmchubosbld不落地供应商取消订单接口，请求供应商接口失败，orderReq={}, accountReq={}, cancelOrderResultDto={}",
                    JSONUtil.toJsonStr(cancelOrderRequest), JSONUtil.toJsonStr(accountReq), JSONUtil.toJsonStr(cancelOrderResult));
            return convertCancelSupplierOrderErrorResult(cancelOrderResult.getReturnCode(), cancelOrderResult.getReturnMsg());
        }
        // 响应数据
        CancelOrderInfoResponse response = cancelOrderResult.getBussinessResponse();
        if (response.getCancelResult() == 2) {
            // 取消中或取消失败
            log.error("cancelSupplierOrder======tmchubosbld不落地供应商取消订单接口，请求供应商接口失败，orderReq={}, accountReq={}, cancelOrderResultDto={}",
                    JSONUtil.toJsonStr(cancelOrderRequest), JSONUtil.toJsonStr(accountReq), JSONUtil.toJsonStr(cancelOrderResult));
            return ResultX.error(GlobalErrorCodeEnum.NO_CANCEL_ORDER);
        }

        if (response.getCancelResult() == 1) {
            // 设置订单状态
            cancelSupplyOrderResponse.setSupplyOrderStatus(TMCHUBOSBLDOrderStatusEnum.CANCELED.getValue());
            cancelSupplyOrderResponse.setOrderStatus(OrderStatusEnum.CANCEL.getResult());// shub订单状态
        } else {
            // 设置订单状态
            cancelSupplyOrderResponse.setSupplyOrderStatus(TMCHUBOSBLDOrderStatusEnum.PROCESSING.getValue());
            cancelSupplyOrderResponse.setOrderStatus(OrderStatusEnum.CANCEL_PENDING.getResult());// shub订单状态
        }

        return ResultX.success(cancelSupplyOrderResponse);
    }

    @Override
    public ResultX<OrderCheckDetailsResponse> querySupplyOrderCheckDetail(QueryCheckDetailRequest queryCheckDetailRequest) throws Exception {
        OrderCheckDetailsResponse orderCheckDetailsResponse = new OrderCheckDetailsResponse();
        //获取供应商配置
        String cacheConfigExtend = cacheService.getCacheConfigExtend(queryCheckDetailRequest.getMerchantSource(), queryCheckDetailRequest.getMerchantCode(),
                SupplyClassEnum.TMCHUBOSBLD, queryCheckDetailRequest.getSupplyCode());
        TMCHUBOSBLDExtendConfig TMCHUBOSBLDExtendConfig = JSONUtil.toBean(cacheConfigExtend, TMCHUBOSBLDExtendConfig.class);

        String supplyOrderCode = queryCheckDetailRequest.getSupplyOrderCode();

        orderCheckDetailsResponse.setOrderCheckDetailPriceItems(new ArrayList<>());

        OrderCheckRequestBase orderCheckRequest = new OrderCheckRequestBase();
        orderCheckRequest.setFcOrderCode(supplyOrderCode);

        //设置账号参数
        AccountReq accountReq = new AccountReq();
        accountReq.setPartnerCode(TMCHUBOSBLDExtendConfig.getPartnerCode());// 合作商编码
        accountReq.setSecurityKey(TMCHUBOSBLDExtendConfig.getSignature());// 合作商秘钥

        orderCheckDetailsResponse.setRequestTime(new Date());
        orderCheckDetailsResponse.setRequestContent(JSON.toJSONString(orderCheckRequest));
        ResponseResult<OrderCheckDetailResponse> orderCheckResponse = tmchubosbldManager.queryOrderCheckDetailInfo(orderCheckRequest, accountReq);
        orderCheckDetailsResponse.setResponseTime(new Date());
        orderCheckDetailsResponse.setResponseContent(JSON.toJSONString(orderCheckResponse));

        if (orderCheckResponse.isError()) {
            // todo 转换错误信息
            log.error("cancelSupplierOrder======tmchubosbld不落地供应商取消订单接口，请求供应商接口失败，orderCheckRequest={}, accountReq={}, orderCheckResponse={}",
                    JSONUtil.toJsonStr(orderCheckRequest), JSONUtil.toJsonStr(accountReq), JSONUtil.toJsonStr(orderCheckResponse));
            return ResultX.error(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION);
        }

        OrderCheckDetailResponse response = orderCheckResponse.getBussinessResponse();

        //每间价格明细
        orderCheckDetailsResponse.setExistOrderCheckDetail(true);
        if (CollUtilX.isNotEmpty(response.getCheckDetails())) {
            List<RoomDetail> roomDetails = new ArrayList<>();
            for (OrderCheckDetails checkDetail : response.getCheckDetails()) {
                RoomDetail roomDetail = new RoomDetail();
                roomDetail.setCheckInState(checkDetail.getCheckInState());
                roomDetail.setCheckInDate(DateUtilX.stringToDate(checkDetail.getCheckInDate()));
                roomDetail.setCheckOutDate(DateUtilX.stringToDate(checkDetail.getCheckOutDate()));
                roomDetail.setRoomNumber(checkDetail.getRoomNumber());

                //客人信息
                List<GuestDTO> guests = new ArrayList<>();
                for (GuestInfo guestInfo : checkDetail.getGuestInfos()) {
                    GuestDTO guest =TmcHubConvert.INSTANCE.conmvert(guestInfo);
                    guests.add(guest);
                }
                roomDetail.setGuests(guests);

                //每晚明细
                List<OrderCheckDetailPriceItem> detailPrices = new ArrayList<>();
                for (com.fangcang.hotel.tmchubosbld.dto.response.order.OrderCheckDetailPriceItem priceItem : checkDetail.getPriceItems()) {
                    OrderCheckDetailPriceItem item = TmcHubConvert.INSTANCE.convert(priceItem);
                    detailPrices.add(item);
                }
                roomDetail.setOrderCheckDetailPriceItems(detailPrices);
                roomDetails.add(roomDetail);
            }
        }
        return ResultX.success(orderCheckDetailsResponse);
    }

    /**
     * 转换取消订单错误结果
     */
    private static ResultX<CancelSupplyOrderResponse> convertCancelSupplierOrderErrorResult(String code, String msg) {
        if (StrUtilX.isNotEmpty(code)) {
            // 订单已取消
            if (code.equals(TMCHUBOSBLDErrorResultEnum.CENTER_ORDER_IS_ALREADY_CANCEL.getCode())) {
                return ResultX.error(GlobalErrorCodeEnum.SUPPLY_CANCEL_ORDER_FIAIL, "订单已取消");
            }
            // 不满足条款
            if (StrUtilX.isNotEmpty(msg) && msg.contains("条款")) {
                return ResultX.error(GlobalErrorCodeEnum.SUPPLY_DISSATISFY_CANCELCLAUSE);
            }
            // 无效参数
            if (code.contains("EMPTY") || code.contains("NOT_NULL") || code.contains("INVALID")) {
                return ResultX.error(GlobalErrorCodeEnum.MISSING_PARAM);
            }
        }
        return ResultX.error(GlobalErrorCodeEnum.SUPPLY_CANCEL_ORDER_FIAIL);
    }

    /**
     * 接收订单状态
     */
    public ResponseResult<OrderStatusPushResponse> orderStatusPush(OrderStatusPushRequest orderStateRequest) {
        log.info("orderStatusPush orderStateRequest = {}", JSON.toJSONString(orderStateRequest));
        ResponseResult<OrderStatusPushResponse> resultDto = new ResponseResult<>();
        OrderStatusPushResponse orderStatusPushResponse = new OrderStatusPushResponse();
        orderStatusPushResponse.setCoOrderCode(orderStateRequest.getCoOrderCode());
        resultDto.setReturnCode(TMCHUBOSBLDErrorResultEnum.SUCCESS.getCode());
        resultDto.setReturnMsg(TMCHUBOSBLDErrorResultEnum.SUCCESS.getMessage());

        String errorMsg = null;
        // 开始时间
        Date start = new Date();
        try {
            // 1、校验参数
            checkOrderStatusPushParam(orderStateRequest, orderStatusPushResponse);
            if (StrUtilX.isNotEmpty(orderStatusPushResponse.getMessage())) {
                resultDto.setBussinessResponse(orderStatusPushResponse);
                return resultDto;
            }
            // 2、查询订单映射
            SupplyOrderRespDTO supplyOrderRespDTO = queryOrderInfo(orderStateRequest.getCoOrderCode(), orderStateRequest.getFcOrderCode());

            // 3、组装参数保存数据
            SupplyOrderUpdateDTO supplyOrderUpdateDTO = new SupplyOrderUpdateDTO();
            if (orderStateRequest.getOrderStatus() == TMCHUBOSBLDPushOrderStatusEnum.CONFIRMED.key){
                supplyOrderUpdateDTO.setOrderStatus(OrderStatusEnum.CONFIRM.getResult());
                supplyOrderUpdateDTO.setSupplyConfirmNo(orderStateRequest.getHotelConfirmNo());
                supplyOrderUpdateDTO.setSupplyOrderStatus(TMCHUBOSBLDOrderStatusEnum.CONFIRMED.getKey()+"");
            } else if (orderStateRequest.getOrderStatus() == TMCHUBOSBLDPushOrderStatusEnum.REFUTE.key ||
                    orderStateRequest.getOrderStatus() == TMCHUBOSBLDPushOrderStatusEnum.CANCEL_SUCCESS.key) {
                supplyOrderUpdateDTO.setSupplyOrderStatus(TMCHUBOSBLDOrderStatusEnum.CANCELED.getKey()+"");
                supplyOrderUpdateDTO.setOrderStatus(OrderStatusEnum.CANCEL.getResult());
            } else {
                supplyOrderUpdateDTO.setOrderStatus(supplyOrderRespDTO.getOrderStatus());
            }
            supplyOrderUpdateDTO.setOrderStatus(convertOrderStatus(orderStateRequest.getOrderStatus()));
            supplyOrderUpdateDTO.setMerchantOrderCode(orderStateRequest.getCoOrderCode());
            supplyOrderUpdateDTO.setMerchantCode(supplyOrderRespDTO.getMerchantCode());
            supplyOrderUpdateDTO.setSupplyOrderCode(supplyOrderRespDTO.getSupplyOrderCode());
            supplyOrderUpdateDTO.setMerchantSource(supplyOrderRespDTO.getMerchantSource());
            // 更新订单
            ResultX<Integer> resultX = supplyOrderApi.supplyOrderUpdateAndPushChannel(supplyOrderUpdateDTO);
            if (resultX.isSuccess()) {
                orderStatusPushResponse.setReceiveStatus(1);
                orderStatusPushResponse.setMessage("修改订单状态成功");
            } else {
                orderStatusPushResponse.setReceiveStatus(0);
                orderStatusPushResponse.setMessage("更改订单状态失败");
            }
            resultDto.setBussinessResponse(orderStatusPushResponse);
        } catch (Exception e) {
            log.error("tmchubosbld不落地接收订单状态异常，orderStatusPush={}", JSONUtil.toJsonStr(orderStateRequest), e);
            orderStatusPushResponse.setReceiveStatus(0);
            orderStatusPushResponse.setMessage("系统异常");
            errorMsg = e.getMessage();
        } finally {
            // 记录SLS（Structured Logging Service）日志信息
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "info");
            map.put(SlsEnum.NAME.getType(), "orderStatusPush");
            map.put(SlsEnum.MESSAGE.getType(), "异常信息：" + (errorMsg == null ? "none" : errorMsg));

            map.put("request", JSONUtil.toJsonStr(orderStateRequest));
            map.put("response", JSONUtil.toJsonStr(resultDto));
            map.put("start", DateUtilX.dateToString(start, DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND_MS));
            slsLogger.saveLog(map, "orderStatusPush", TMCHUBOSBLDConstant.SLS_SOURCE);
        }
        return resultDto;
    }

    /**
     * 查询订单信息
     */
    private SupplyOrderRespDTO queryOrderInfo(String merchantOrderCode, String supplyOrderCode) {
        SupplyOrderQueryReqDTO supplyOrderQueryReqDTO = new SupplyOrderQueryReqDTO();
        supplyOrderQueryReqDTO.setSupplyClass(SupplyClassEnum.TMCHUBOSBLD.getSupplierClass());
        supplyOrderQueryReqDTO.setMerchantOrderCode(merchantOrderCode);
        supplyOrderQueryReqDTO.setSupplyOrderCode(supplyOrderCode);
        Date thisDate = new Date();
        thisDate = DateUtilX.getDate(DateUtilX.stringToDate(DateUtilX.dateToString(thisDate)), -3, 0);
        supplyOrderQueryReqDTO.setCreatedDt(DateUtilX.getLocalDateTime(thisDate));
        ResultX<List<SupplyOrderRespDTO>> supplyOrderListResult = supplyOrderApi.supplyOrderList(supplyOrderQueryReqDTO);
        if (supplyOrderListResult.isError()) {
            log.error("tmchubosbld不落地调用common公共服务查询订单映射失败，req={}, resp={}", JSONUtil.toJsonStr(supplyOrderQueryReqDTO), JSONUtil.toJsonStr(supplyOrderListResult));
            throw new BizException(supplyOrderListResult.getNumber(), supplyOrderListResult.getCode(), supplyOrderListResult.getMsg());
        }

        if (CollUtilX.isEmpty(supplyOrderListResult.getData())) {
            log.error("tmchubosbld不落地调用common公共服务查询订单映射返回为空，req={}, resp={}", JSONUtil.toJsonStr(supplyOrderQueryReqDTO), JSONUtil.toJsonStr(supplyOrderListResult));
            throw new BizException(GlobalErrorCodeEnum.ORDER_NOT_EXIST_MAPPING);
        }

        if (supplyOrderListResult.getData().size() > 1) {
            log.error("tmchubosbld不落地调用common公共服务查询订单映射返回为多条，req={}, resp={}", JSONUtil.toJsonStr(supplyOrderQueryReqDTO), JSONUtil.toJsonStr(supplyOrderListResult));
            throw new BizException(GlobalErrorCodeEnum.ORDER_EXIST_MUCH_MAPPING);
        }
        return supplyOrderListResult.getData().get(0);
    }

    /**
     * 校验接收订单状态参数
     */
    private void checkOrderStatusPushParam(OrderStatusPushRequest request, OrderStatusPushResponse response) {
        if (StrUtilX.isEmpty(request.getFcOrderCode())) {
            response.setReceiveStatus(0);
            response.setMessage("参数不能为空: 红色加力订单号\t");
        }
        if (StrUtilX.isEmpty(request.getCoOrderCode())) {
            response.setReceiveStatus(0);
            response.setMessage("参数不能为空: 合作商的订单号");
        }
        if (request.getOrderStatus() == null) {
            response.setReceiveStatus(0);
            response.setMessage("参数不能为空: 订单状态");
        }
        if (request.getOrderStatus() == TMCHUBOSBLDOrderStatusEnum.CANCELED.getKey()) {
            if (StrUtilX.isEmpty(request.getRefuseCode())) {
                response.setReceiveStatus(0);
                response.setMessage("参数不能为空: 拒单原因返回码");
            }
        }
    }

    /**
     * 同步tmchubosbld不落地订单状态任务
     */
    public void syncTmchubosBLDOrderStatusTask() {
        // 获取订单状态为未确认的数据
        SupplyOrderQueryReqDTO supplyOrderQueryReqDTO = new SupplyOrderQueryReqDTO();
        supplyOrderQueryReqDTO.setSupplyClass(SupplyClassEnum.TMCHUBOSBLD.getSupplierClass());
        supplyOrderQueryReqDTO.setOrderStatus(OrderStatusEnum.NEED_CONFIRM.getResult());
        Date thisDate = new Date();
        thisDate = DateUtilX.getDate(DateUtilX.stringToDate(DateUtilX.dateToString(thisDate)), -3, 0);
        supplyOrderQueryReqDTO.setCreatedDt(DateUtilX.getLocalDateTime(thisDate));
        ResultX<List<SupplyOrderRespDTO>> supplyOrderResultX = supplyOrderApi.supplyOrderList(supplyOrderQueryReqDTO);

        // 判断请求结果
        if (supplyOrderResultX.isError()) {
            log.error("同步tmchubosbld不落地订单状态任务，获取订单状态为未确认的数据失败，req={}, resp={}", JSONUtil.toJsonStr(supplyOrderQueryReqDTO), JSONUtil.toJsonStr(supplyOrderResultX));
            return;
        }
        List<SupplyOrderRespDTO> supplyOrderRespDTOList = supplyOrderResultX.getData();
        if (CollUtilX.isNotEmpty(supplyOrderRespDTOList)) {
            for (SupplyOrderRespDTO supplyOrderRespDTO : supplyOrderRespDTOList) {
                String request = null;
                String result = null;
                // 开始时间
                Date start = new Date();
                String errorMsg = null;// 错误描述
                String error = null;// 错误详细信息
                try {
                    // 构建查询订单参数
                    QuerySupplyOrderRequest supplyOrderReq = new QuerySupplyOrderRequest();
                    supplyOrderReq.setSupplyClass(supplyOrderRespDTO.getSupplyClass());
                    supplyOrderReq.setSupplyCode(supplyOrderRespDTO.getSupplyCode());
                    supplyOrderReq.setMerchantCode(supplyOrderRespDTO.getMerchantCode());
                    supplyOrderReq.setSupplyOrderCode(supplyOrderRespDTO.getSupplyOrderCode());
                    supplyOrderReq.setMerchantOrderCode(supplyOrderRespDTO.getMerchantOrderCode());
                    supplyOrderReq.setMerchantSource(supplyOrderRespDTO.getMerchantSource());
                    // 获取订单信息
                    ResultX<QuerySupplyOrderResponse> querySupplyOrderResultX = querySupplierOrderStatus(supplyOrderReq);

                    // 判断请求是否成功，并且状态不一样需要更新
                    if (querySupplyOrderResultX.isSuccess() && OrderStatusEnum.NEED_CONFIRM.getResult().compareTo(querySupplyOrderResultX.getData().getOrderStatus()) != 0) {
                        // 订单信息
                        QuerySupplyOrderResponse orderResponse = querySupplyOrderResultX.getData();
                        // 组装参数更新订单
                        SupplyOrderUpdateDTO supplyOrderUpdateDTO = new SupplyOrderUpdateDTO();
                        supplyOrderUpdateDTO.setSupplyConfirmNo(orderResponse.getSupplyConfirmNo());
                        supplyOrderUpdateDTO.setSupplyOrderStatus(orderResponse.getSupplyOrderStatus());
                        supplyOrderUpdateDTO.setOrderStatus(orderResponse.getOrderStatus());
                        supplyOrderUpdateDTO.setMerchantOrderCode(supplyOrderRespDTO.getMerchantOrderCode());
                        supplyOrderUpdateDTO.setMerchantCode(supplyOrderRespDTO.getMerchantCode());
                        supplyOrderUpdateDTO.setSupplyOrderCode(supplyOrderRespDTO.getSupplyOrderCode());
                        supplyOrderUpdateDTO.setMerchantSource(supplyOrderRespDTO.getMerchantSource());
                        request = JSONUtil.toJsonStr(supplyOrderUpdateDTO);
                        // 更新订单
                        ResultX<Integer> resultX = supplyOrderApi.supplyOrderUpdateAndPushChannel(supplyOrderUpdateDTO);
                        result = JSONUtil.toJsonStr(resultX);
                        if (resultX.isError()) {
                            log.error("同步tmchubosbld不落地订单状态任务，更新订单状态失败，req={}, resp={}", JSONUtil.toJsonStr(supplyOrderUpdateDTO), JSONUtil.toJsonStr(resultX));
                        }
                    }
                } catch (Exception e) {
                    // 记录其他异常
                    errorMsg = e.getMessage();
                    error = TMCHUBOSBLDUtil.getStackTraceAsString(e);
                    log.error("同步tmchubosbld不落地订单状态任务异常", e);
                } finally {
                    // 记录SLS（Structured Logging Service）日志信息
                    Map<String, String> map = new HashMap<>();
                    map.put(SlsEnum.LEVEL.getType(), "info");
                    map.put(SlsEnum.NAME.getType(), "synctmchubosBLDOrderStatusTask");
                    map.put(SlsEnum.MESSAGE.getType(), "异常信息：" + (errorMsg == null ? "none" : errorMsg));

                    map.put("orderInfo", JSONUtil.toJsonStr(supplyOrderRespDTO));
                    map.put("request", request);
                    map.put("result", result);
                    map.put("start", DateUtilX.dateToString(start, DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND_MS));
                    if (StrUtilX.isNotEmpty(error)) {
                        map.put("error", error);
                    }

                    slsLogger.saveLog(map, "synctmchubosLDOrderStatusTask", TMCHUBOSBLDConstant.SLS_SOURCE);
                }
            }
        }
    }

    /**
     * 转换订单状态
     */
    private Integer convertOrderStatus(Integer orderStatus) {
        if (TMCHUBOSBLDOrderStatusEnum.PROCESSING.getKey() == orderStatus) {
            return OrderStatusEnum.NEED_CONFIRM.getResult();// 未确认
        } else if (TMCHUBOSBLDOrderStatusEnum.CONFIRMED.getKey() == orderStatus) {
            return OrderStatusEnum.CONFIRM.getResult();// 确认
        } else if (TMCHUBOSBLDOrderStatusEnum.CANCELED.getKey() == orderStatus) {
            return OrderStatusEnum.CANCEL.getResult();// 已取消
        }else if(TMCHUBOSBLDOrderStatusEnum.NEW.getKey() == orderStatus){
            return OrderStatusEnum.NEED_CONFIRM.getResult();// 未确认
        }
        //未确认
        return OrderStatusEnum.NEED_CONFIRM.getResult();
    }
}
