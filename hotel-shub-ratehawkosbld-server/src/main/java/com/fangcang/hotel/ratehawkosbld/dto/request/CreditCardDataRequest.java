package com.fangcang.hotel.ratehawkosbld.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  信用卡数据令牌化
 *
 * <AUTHOR>
 * @date 2024-05-30 17:04:51
 */
@Data
public class CreditCardDataRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * item_id值
     */
    @JSONField(name = "object_id")
    private String objectId;

    /**
     * 合作伙伴开具的预订付款支票的通用唯一标识符 （UUID4） 令牌
     */
    @JSONField(name = "pay_uuid")
    private String payUuid;

    /**
     * 合作伙伴执行的预订付款操作的通用唯一标识符 （UUID4） 令牌
     */
    @JSONField(name = "init_uuid")
    private String initUuid;

    /**
     * 客人的名字
     */
    @JSONField(name = "user_first_name")
    private String userFirstName;

    /**
     * 客人的姓氏
     */
    @JSONField(name = "user_last_name")
    private String userLastName;

    /**
     * CVC代码
     */
    @JSONField(name = "cvc")
    private String cvc;

    /**
     * 是否需要CVC代码
     */
    @JSONField(name = "is_cvc_required")
    private Boolean isCvcRequired;

    /**
     * 信用卡数据信息
     */
    @JSONField(name = "credit_card_data_core")
    private CreditCardDataCore creditCardDataCore;

}
