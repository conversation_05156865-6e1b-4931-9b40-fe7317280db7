package com.fangcang.hotel.rjbld.dto.request;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  基本请求参数
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:14
 */
@Data
public class BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * ${field.fieldName}
     */
    @JSONField(name = "Terminal_License")
    private String license;

    /**
     * ${field.fieldName}
     */
    @JSONField(name = "Terminal_Seq")
    private String seq;

    /**
     * ${field.fieldName}
     */
    @JSONField(name = "Terminal_OprId")
    private String oprId;

}
