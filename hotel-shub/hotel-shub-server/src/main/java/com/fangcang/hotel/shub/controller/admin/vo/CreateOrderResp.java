package com.fangcang.hotel.shub.controller.admin.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 创建订单响应参数
 * @author: qiu
 * @create: 2023-10-17 17:21
 */
@Data
public class CreateOrderResp {

    /**
     * 订单号
     */
    private String merchantOrderCode;

    /**
     * 供应商订单号
     */
    private String supplyOrderCode;

    /**
     * 供应商确认号
     */
    private String supplyConfirmNo;

    /**
     * 订单状态
     *  OrderStatusEnum
     */
    private Integer orderStatus;

    /**
     * 供应奖励
     */
    private BigDecimal supplyReward;

    /**
     * 供应商返佣
     */
    private BigDecimal supplyShouldRackBack;
}