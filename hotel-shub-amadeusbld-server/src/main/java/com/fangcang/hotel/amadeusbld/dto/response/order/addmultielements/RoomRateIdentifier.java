package com.fangcang.hotel.amadeusbld.dto.response.order.addmultielements;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/7/1 12:34
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "roomRateIdentifier")
public class RoomRateIdentifier {

    @XmlElement(name = "roomType")
    private String roomType;

    @XmlElement(name = "ratePlanCode")
    private String ratePlanCode;

    @XmlElement(name = "rateQualifiedIndic")
    private String rateQualifiedIndic;

    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    public String getRatePlanCode() {
        return ratePlanCode;
    }

    public void setRatePlanCode(String ratePlanCode) {
        this.ratePlanCode = ratePlanCode;
    }

    public String getRateQualifiedIndic() {
        return rateQualifiedIndic;
    }

    public void setRateQualifiedIndic(String rateQualifiedIndic) {
        this.rateQualifiedIndic = rateQualifiedIndic;
    }
}
