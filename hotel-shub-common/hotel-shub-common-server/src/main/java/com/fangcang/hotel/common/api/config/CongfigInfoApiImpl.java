package com.fangcang.hotel.common.api.config;

import com.fangcang.hotel.common.api.common.config.CongfigInfoApi;
import com.fangcang.hotel.common.api.common.config.dto.AutomaticRegisterSupplyDTO;
import com.fangcang.hotel.common.api.common.config.dto.ConfigInfoQueryReqDTO;
import com.fangcang.hotel.common.api.common.config.dto.ConfigInfoRespDTO;
import com.fangcang.hotel.common.controller.admin.config.vo.ConfigInfoQueryReqVO;
import com.fangcang.hotel.common.convert.config.ConfigInfoConvert;
import com.fangcang.hotel.common.dal.db.config.ConfigInfoDO;
import com.fangcang.hotel.common.service.config.ConfigInfoService;
import com.fangcang.hotel.framework.common.domain.ResultX;
import com.fangcang.hotel.framework.common.util.CollUtilX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description:
 * @author: qiu
 * @create: 2023-12-02 18:46
 */
@RestController
public class CongfigInfoApiImpl implements CongfigInfoApi {


    @Autowired
    private ConfigInfoService configInfoService;

    @Override
    public ResultX<List<ConfigInfoRespDTO>> configInfoList(ConfigInfoQueryReqDTO reqDTO) {
        ConfigInfoQueryReqVO reqVO = ConfigInfoConvert.INSTANCE.convert(reqDTO);

        List<ConfigInfoDO> list = configInfoService.configInfoList(reqVO);
        if (CollUtilX.isEmpty(list)) {
            return ResultX.success(null);
        }

        List<ConfigInfoRespDTO> configInfoRespDTOS = ConfigInfoConvert.INSTANCE.convertApiList(list);
        return ResultX.successList(configInfoRespDTOS);
    }

    @Override
    public ResultX<Boolean> automaticRegisterSupply(List<AutomaticRegisterSupplyDTO> reqs) {
        return configInfoService.automaticRegisterSupply(reqs);
    }
}