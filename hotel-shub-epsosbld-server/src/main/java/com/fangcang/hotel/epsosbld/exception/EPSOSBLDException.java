package com.fangcang.hotel.epsosbld.exception;

import com.fangcang.hotel.core.enums.GlobalErrorCodeEnum;
import com.fangcang.hotel.data.base.exception.BaseException;

/**
 * eps 异常类
 */
public class EPSOSBLDException extends BaseException {

    /**
     * Constructs a new runtime exception with {@code null} as its
     * detail message.  The cause is not initialized, and may subsequently be
     * initialized by a call to {@link #initCause}.
     *
     * @param globalErrorCodeEnum
     */
    public EPSOSBLDException(GlobalErrorCodeEnum globalErrorCodeEnum) {
        super(globalErrorCodeEnum);
    }

    /**
     * Constructs a new runtime exception with the specified detail message.
     * The cause is not initialized, and may subsequently be initialized by a
     * call to {@link #initCause}.
     *
     * @param message   the detail message. The detail message is saved for
     *                  later retrieval by the {@link #getMessage()} method.
     * @param globalErrorCodeEnum
     */
    public EPSOSBLDException(String message, GlobalErrorCodeEnum globalErrorCodeEnum) {
        super(message, globalErrorCodeEnum);
    }

    /**
     * Constructs a new runtime exception with the specified detail message and
     * cause.  <p>Note that the detail message associated with
     * {@code cause} is <i>not</i> automatically incorporated in
     * this runtime exception's detail message.
     *
     * @param message   the detail message (which is saved for later retrieval
     *                  by the {@link #getMessage()} method).
     * @param cause     the cause (which is saved for later retrieval by the
     *                  {@link #getCause()} method).  (A <tt>null</tt> value is
     *                  permitted, and indicates that the cause is nonexistent or
     *                  unknown.)
     * @param globalErrorCodeEnum
     * @since 1.4
     */
    public EPSOSBLDException(String message, Throwable cause, GlobalErrorCodeEnum globalErrorCodeEnum) {
        super(message, cause, globalErrorCodeEnum);
    }

    /**
     * Constructs a new runtime exception with the specified cause and a
     * detail message of <tt>(cause==null ? null : cause.toString())</tt>
     * (which typically contains the class and detail message of
     * <tt>cause</tt>).  This constructor is useful for runtime exceptions
     * that are little more than wrappers for other throwables.
     *
     * @param cause     the cause (which is saved for later retrieval by the
     *                  {@link #getCause()} method).  (A <tt>null</tt> value is
     *                  permitted, and indicates that the cause is nonexistent or
     *                  unknown.)
     * @param globalErrorCodeEnum
     * @since 1.4
     */
    public EPSOSBLDException(Throwable cause, GlobalErrorCodeEnum globalErrorCodeEnum) {
        super(cause, globalErrorCodeEnum);
    }

    /**
     * Constructs a new runtime exception with the specified detail
     * message, cause, suppression enabled or disabled, and writable
     * stack trace enabled or disabled.
     *
     * @param message            the detail message.
     * @param cause              the cause.  (A {@code null} value is permitted,
     *                           and indicates that the cause is nonexistent or unknown.)
     * @param enableSuppression  whether or not suppression is enabled
     *                           or disabled
     * @param writableStackTrace whether or not the stack trace should
     *                           be writable
     * @param globalErrorCodeEnum
     * @since 1.7
     */
    public EPSOSBLDException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace, GlobalErrorCodeEnum globalErrorCodeEnum) {
        super(message, cause, enableSuppression, writableStackTrace, globalErrorCodeEnum);
    }
}
