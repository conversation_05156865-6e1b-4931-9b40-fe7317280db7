package com.fangcang.hotel.hzbld.enums;

import com.fangcang.hotel.data.api.enums.BreakfastTypeEnum;

/**
 *
 *  早餐映射枚举
 *
 * <AUTHOR>
 * @date 2023-12-11 14:22:35
 */
public enum HZBLDBreakfastTypeEnum {

    HZBLD_2102("2102", "全餐", BreakfastTypeEnum.CHINESE),
    HZBLD_2103("2103", "欧式早", BreakfastTypeEnum.CONTINENTAL);

    public String key;

    public String name;

    public BreakfastTypeEnum value;

    private HZBLDBreakfastTypeEnum (String key, String name, BreakfastTypeEnum value) {
        this.key = key;
        this.name = name;
        this.value = value;
    }

    public static HZBLDBreakfastTypeEnum getEnumByKey(String key) {
        for (HZBLDBreakfastTypeEnum enumObj : HZBLDBreakfastTypeEnum.values()) {
            if (enumObj.key.equals(key)) {
                return enumObj;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BreakfastTypeEnum getValue() {
        return value;
    }

    public void setValue(BreakfastTypeEnum value) {
        this.value = value;
    }
}
