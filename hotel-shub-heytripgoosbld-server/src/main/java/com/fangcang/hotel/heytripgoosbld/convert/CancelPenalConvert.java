package com.fangcang.hotel.heytripgoosbld.convert;

import com.fangcang.hotel.data.api.dto.CancelPenal;
import com.fangcang.hotel.data.api.dto.CancelPenalties;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CancelPenalConvert {

    public static CancelPenalConvert INSTANCE = Mappers.getMapper(CancelPenalConvert.class);

    CancelPenal convert(CancelPenalties cancelPenalties);

}
