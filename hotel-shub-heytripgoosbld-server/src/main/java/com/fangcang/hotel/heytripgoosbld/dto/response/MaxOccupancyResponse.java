package com.fangcang.hotel.heytripgoosbld.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/26 11:04
 * @Description:
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaxOccupancyResponse {

    /**
     * 最大入住人数
     */
    @JSONField(name = "Total")
    private Integer total;

    /**
     * 最大入住儿童数
     */
    @JSONField(name = "Children")
    private Integer children;

    /**
     * 最大入住成人数
     */
    @JSONField(name = "Adults")
    private Integer adults;

    /**
     * 入住人年龄分类
     */
    @JSONField(name = "AgeCategories")
    private List<AgeCategoryResponse> ageCategories;

}
