package com.fangcang.hotel.ratehawkosbld.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ： 2024/5/23
 */
@Data
public class CancellationPenaltyData {

    /**
     * 免费取消时间，返回null表示不可取消
     */
    @JSONField(name = "free_cancellation_before")
    private Date freeCancellationBefore;

    /**
     * 按时间段划分的取消政策明细
     */
    private List<CancellationPolicyData> policies;
}
