package com.fangcang.hotel.amadeusosbld.dto.response.price;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/18 19:22
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "RoomRateDescription")
public class RoomRateDescription {

    @XmlAttribute(name = "Name")
    private String name;

    @XmlElement(name = "Text")
    private List<RoomRateDescriptionText> roomRateDescriptionTextList;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<RoomRateDescriptionText> getRoomRateDescriptionTextList() {
        return roomRateDescriptionTextList;
    }

    public void setRoomRateDescriptionTextList(List<RoomRateDescriptionText> roomRateDescriptionTextList) {
        this.roomRateDescriptionTextList = roomRateDescriptionTextList;
    }
}
