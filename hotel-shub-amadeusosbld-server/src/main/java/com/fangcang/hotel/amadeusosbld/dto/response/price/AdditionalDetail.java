package com.fangcang.hotel.amadeusosbld.dto.response.price;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/18 18:57
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "AdditionalDetail")
public class AdditionalDetail {

    @XmlAttribute(name = "Type")
    private String type;

    @XmlElementWrapper(name = "DetailDescription")
    @XmlElement(name = "Text")
    private List<String> textList;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getTextList() {
        return textList;
    }

    public void setTextList(List<String> textList) {
        this.textList = textList;
    }
}
