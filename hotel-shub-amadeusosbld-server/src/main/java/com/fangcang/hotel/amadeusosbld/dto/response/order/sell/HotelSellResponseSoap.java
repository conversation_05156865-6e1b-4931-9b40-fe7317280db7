package com.fangcang.hotel.amadeusosbld.dto.response.order.sell;


import com.fangcang.hotel.amadeusosbld.dto.response.common.ResponseHeader;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2021/6/21 10:18
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "soapenv:Envelope")
public class HotelSellResponseSoap {

    @XmlAttribute(name = "xmlns:soap")
    private String xmlnsSoap = "http://schemas.xmlsoap.org/soap/envelope/";

    @XmlAttribute(name = "xmlns:awsse")
    private String xmlnsAwsse = "http://xml.amadeus.com/2010/06/Session_v3";

    @XmlAttribute(name = "xmlns:wsa")
    private String xmlnsWsa = "http://www.w3.org/2005/08/addressing";

    @XmlElement(name = "soapenv:Header")
    private ResponseHeader responseHeader;

    @XmlElement(name = "soapenv:Body")
    private HotelSellResponseBody hotelSellResponseBody;

    public HotelSellResponseBody getHotelSellResponseBody() {
        return hotelSellResponseBody;
    }

    public void setHotelSellResponseBody(HotelSellResponseBody hotelSellResponseBody) {
        this.hotelSellResponseBody = hotelSellResponseBody;
    }

    public ResponseHeader getResponseHeader() {
        return responseHeader;
    }

    public void setResponseHeader(ResponseHeader responseHeader) {
        this.responseHeader = responseHeader;
    }
}
