package com.fangcang.hotel.irevebld.dto.response.product;

import com.fangcang.hotel.irevebld.enums.*;
import lombok.Data;

import java.util.List;

@Data
public class ProductDto {
    /**
     * 产品id
     */
    private Integer productId;
    /**
     * 酒店id
     */
    private Integer hotelId;
    /**
     * 房型id
     */
    private Integer roomId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 床型
     * @see IREVEBLDBedTypeEnum
     */
    private String bedTypes;
    /**
     * 币种
     * @see IREVEBLDCurrencyEnum
     */
    private String currency;
    /**
     * 产品类型
     * @see IREVEBLDProductTypeEnum
     */
    private Integer productType;
    /**
     * 早餐
     * @see IREVEBLDBreakfastNumEnum
     */
    private Integer breakfastQty;
    /**
     * 宽带
     * @see IREVEBLDNetTypeEnum
     */
    private Integer internetFeeType;
    /**
     * 适用人群
     * @see IREVEBLDAdaptableNationTypeEnum
     */
    private Integer adaptableNation;
    /**
     * 适用人群名称(选自定义才需要填写)
     */
    private String adaptableNationName;
    /**
     * 产品条款
     */
    private ProductRestrictDto productRestrictDto;
    /**
     * 产品提示信息
     */
    private List<ProductHintInfoDto> hintInfoList;
    /**
     * 每日价格配额信息
     */
    private List<PriceQuotaDto> priceQuotaDtoList;
}
