package com.fangcang.hotel.hzbld.dto.request;

import java.util.Date;
import java.util.Date;
import java.util.Date;
import java.util.List;
import com.fangcang.hotel.hzbld.dto.request.DailyPriceParameter;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  创建订单请求参数
 *
 * <AUTHOR>
 * @date 2023-12-11 14:22:34
 */
@Data
public class CreateOrderRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 是否总对总
     */
    private Boolean isGroup;

    /**
     * 担保模式
     */
    private String guaranteeMode;

    /**
     * API 类型
     */
    private String apiType;

    /**
     * 下单请求号
     */
    private String requestNo;

    /**
     * 关联号(OA Id)
     */
    private String outerRefId;

    /**
     * 预订人公司卡ID
     */
    private String bookerId;

    /**
     * 是否为入住人赠送积分
     */
    private Boolean isNeedRegisterMember;

    /**
     * 预订人员工编号
     */
    private String bookerPersonId;

    /**
     * 预订人姓名
     */
    private String bookerPersonName;

    /**
     * 预订人手机号
     */
    private String bookerPersonMobile;

    /**
     * 预订人证件号
     */
    private String bookerPersonIDNumber;

    /**
     * 成人数，>=1
     */
    private Integer adults;

    /**
     * 儿童数
     */
    private Integer childs;

    /**
     * 差旅档次和级别
     */
    private String travelLevel;

    /**
     * 差旅类型
     */
    private String travelType;

    /**
     * 联系人姓名
     */
    private String linkName;

    /**
     * 联系人手机
     */
    private String linkMobile;

    /**
     * 联系人邮箱
     */
    private String linkEmail;

    /**
     * 酒店Id
     */
    private String hotelId;

    /**
     * 房型
     */
    private String roomTypeId;

    /**
     * 入住类型
     */
    private String checkInType;

    /**
     * 入住日期
     */
    private Date checkInDate;

    /**
     * 离店日期
     */
    private Date checkOutDate;

    /**
     * 抵达时间
     */
    private Date arriveTime;

    /**
     * 房间数量,>=1
     */
    private Integer roomCount;

    /**
     * 客人备注
     */
    private String remark;

    /**
     * 每日房价列表
     */
    private List<DailyPriceParameter> dailyPriceList;

    /**
     * 房价码
     */
    private String rateCode;

    /**
     * 活动Id
     */
    private String activityId;

    /**
     * 是否需要一次性用品
     */
    private Boolean isNeed;

    /**
     * 纳税人识别号
     */
    private String invoiceNo;

    /**
     * 预订价格类型
     */
    private String bookerPriceType;

    /**
     * 扩展字段。客户可以传递需要的信息，后期方便对账。
     */
    private String customerDictionary;

}
