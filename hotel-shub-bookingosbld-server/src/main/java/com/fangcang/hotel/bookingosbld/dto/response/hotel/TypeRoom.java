package com.fangcang.hotel.bookingosbld.dto.response.hotel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/22 12:33
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "TypeRoom")
public class TypeRoom {

    @XmlAttribute(name = "Name")
    private String name;

    @XmlAttribute(name = "RoomTypeCode")
    private String roomTypeCode;

    @XmlAttribute(name = "BedTypeCode")
    private String bedTypeCode;

    @XmlAttribute(name = "RoomCategory")
    private String roomCategory;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRoomTypeCode() {
        return roomTypeCode;
    }

    public void setRoomTypeCode(String roomTypeCode) {
        this.roomTypeCode = roomTypeCode;
    }

    public String getBedTypeCode() {
        return bedTypeCode;
    }

    public void setBedTypeCode(String bedTypeCode) {
        this.bedTypeCode = bedTypeCode;
    }

    public String getRoomCategory() {
        return roomCategory;
    }

    public void setRoomCategory(String roomCategory) {
        this.roomCategory = roomCategory;
    }
}
