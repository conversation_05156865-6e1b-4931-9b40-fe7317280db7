package com.fangcang.hotel.shub.service;

import com.fangcang.hotel.framework.common.domain.ResultX;
import com.fangcang.hotel.shub.controller.admin.vo.OrderCheckDetailsReq;
import com.fangcang.hotel.shub.controller.admin.vo.*;

/**
 * @description: 订单服务
 * @author: qiu
 * @create: 2023-10-17 15:36
 */
public interface OrderService {

    /**
     * 试预定
     * @param baseVo 商家数据
     * @param req 试预定请求
     * @return
     */
    ResultX<PreBookingResp> preBooking(BaseVo baseVo, PreBookingReq req);


    /**
     * 创建订单
     * @param baseVo 商家数据
     * @param req 创建订单请求
     * @return 创建订单结果
     */
    ResultX<CreateOrderResp> createOrder(BaseVo baseVo, CreateOrderReq req);


    /**
     * 查询订单
     * @param baseVo 商家数据
     * @param req 查询订单请求
     * @return 查询订单结果
     */
    ResultX<QueryOrderResp> queryOrder(BaseVo baseVo, QueryOrderReq req);

    /**
     * 取消订单
     * @param baseVo 商家数据
     * @param req 取消订单请求
     * @return 取消订单结果
     */
    ResultX<CancelOrderResp> cancelOrder(BaseVo baseVo, CancelOrderReq req);


    /**
     * 查询供应商订单入住明细
     * @param baseVo 商家数据
     * @param orderCheckDetailsReq  查询供应商订单入住明细请求参数
     * @return 供应商入住明细
     */
    ResultX<OrderCheckDetailsResp> queryOrderCheckDetail(BaseVo baseVo, OrderCheckDetailsReq orderCheckDetailsReq);

}