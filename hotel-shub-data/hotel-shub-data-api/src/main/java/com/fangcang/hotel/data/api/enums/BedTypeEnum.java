package com.fangcang.hotel.data.api.enums;

import java.io.Serializable;

/**
 * 床型
 * <AUTHOR>
 *
 */
public enum BedTypeEnum implements Serializable {

	
	KING("1000000","大床"),
	TWIN("2000000","双床"),
	SINGLE("A000000","单人床"),
	THREE("3000000", "三床"),
	FOUR("4000000","四床"),
	BDdoble("5000000","大/双床"),
	BUNK("B000000","上下铺"),
	BUNKHOUSE("C000000","通铺"),
	TATAMI("D000000","榻榻米"),
	WATER("E000000","水床"),
	ROUND("F000000","圆床"),
	FIGHT("G000000","拼床"),
	KANG("H000000","炕"),



	//ctrip5.0新加
	SOFA("I000000", "沙发床"),
	SPACE("J000000","太空舱"),
	SEMIDoble("K000000","小型双人床"),
	JUMBO("L000000","特大床"),
	FLOOR("M000000","地面床铺"),
	CAPSULE("N000000","胶囊床"),
	DOUBLEBED("P000000","双人床"),

	//eps1.0新加
	TrundleBed("Q000000","伸缩床"),
	DayBed("R000000","两用长椅"),
	Futon("S000000","双人日式床"),
	RollawayBed("T000000","简易折叠床"),
	MurphyBed("U000000","壁柜折叠床"),
	Crib("V000000","婴儿床"),
//
//	// 床型一级分类0
//	Big("BIG","大床"),
//	Double("DOUBLE","双床"),
//	Single("SINGLE","单人床"),
//	More("MORE","多张床")
	;

	public String key;
	public String value;

	private BedTypeEnum(String key, String value) {
		this.key = key;
		this.value = value;
	}
	
	public static String getKeyByValue(String value) {
		String key = null;
		for(BedTypeEnum bedTypeEnum : BedTypeEnum.values()) {
			if(bedTypeEnum.value.equals(value)) {
				key = bedTypeEnum.key;
				break;
			}
		}
		return key;
	}
	
	public static String getValueByKey(String key) {
		String value = null;
		for(BedTypeEnum bedTypeEnum : BedTypeEnum.values()) {
			if(bedTypeEnum.key.equals(key)) {
				value = bedTypeEnum.value;
				break;
			}
		}
		return value;
	}
	
	public static BedTypeEnum  getEnumByKey(String key){
		BedTypeEnum bedTypeEnum = null;
		for(BedTypeEnum bedType : BedTypeEnum.values()) {
			if(bedType.key.equals(key)) {
				bedTypeEnum = bedType;
				break;
			}
		}
		return bedTypeEnum;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
	
}
