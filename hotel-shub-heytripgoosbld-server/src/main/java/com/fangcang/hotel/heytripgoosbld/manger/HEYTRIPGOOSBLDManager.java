package com.fangcang.hotel.heytripgoosbld.manger;

import com.fangcang.hotel.heytripgoosbld.config.HEYTRIPGOOSBLDExtendConfig;
import com.fangcang.hotel.heytripgoosbld.dto.request.*;
import com.fangcang.hotel.heytripgoosbld.dto.response.*;

/**
 * <AUTHOR>
 * @Date 2025/2/26 09:44
 * @Description:
 */
public interface HEYTRIPGOOSBLDManager {

    /**
     * 获取有效酒店编号
     *
     * @param request
     * @return
     */
    AvailableAccommodationIdsResponse availableAccommodationIds(AvailableAccommodationIdsRequest request, HEYTRIPGOOSBLDExtendConfig extendConfig);

    /**
     * 获取酒店房型相关静态信息
     *
     * @param request
     * @param extendConfig
     * @return
     */
    AccommodationsDetailsResponse accommodationsDetails(AccommodationsDetailsRequest request, HEYTRIPGOOSBLDExtendConfig extendConfig);

    /**
     * 获取酒店报价
     *
     * @param request
     * @param extendConfig
     * @return
     */
    AvailabilityResponse availability(AvailabilityRequest request, HEYTRIPGOOSBLDExtendConfig extendConfig);

    /**
     * 下单前验证报价是否可订
     *
     * @param request
     * @param extendConfig
     * @return
     */
    PriceCheckResponse priceCheck(PriceCheckRequest request, HEYTRIPGOOSBLDExtendConfig extendConfig);

    /**
     * 酒店起价查询接口
     *
     * @param request
     * @param extendConfig
     * @return
     */
    QuotedHotelsPriceResponse quotedHotelsPrice(QuotedHotelsPriceRequest request, HEYTRIPGOOSBLDExtendConfig extendConfig);

    /**
     * 创建订单
     *
     * @param request
     * @param extendConfig
     * @return
     */
    CreateResponse create(CreateRequest request, HEYTRIPGOOSBLDExtendConfig extendConfig);

    /**
     * 取消订单
     *
     * @param request
     * @param extendConfig
     * @return
     */
    CancelResponse cancel(CancelRequest request, HEYTRIPGOOSBLDExtendConfig extendConfig);

    /**
     * 查询订单
     *
     * @param request
     * @param extendConfig
     * @return
     */
    PreviewResponse preview(PreviewRequest request, HEYTRIPGOOSBLDExtendConfig extendConfig);

    /**
     * 新增热销酒店Id获取接口
     *
     * @param request
     * @param extendConfig
     * @return
     */
    GetHotelIncrementResponse getHotelIncrement(GetHotelIncrementRequest request, HEYTRIPGOOSBLDExtendConfig extendConfig);

}
