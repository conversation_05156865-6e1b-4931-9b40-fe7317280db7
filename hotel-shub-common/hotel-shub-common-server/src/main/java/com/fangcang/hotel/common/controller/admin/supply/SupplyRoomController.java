package com.fangcang.hotel.common.controller.admin.supply;

import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyRoomAditReqVO;
import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyRoomPageReqVO;
import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyRoomQueryReqVO;
import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyRoomRespVO;
import com.fangcang.hotel.common.convert.supply.SupplyRoomConvert;
import com.fangcang.hotel.common.dal.db.supply.SupplyRoomDO;
import com.fangcang.hotel.common.service.supply.SupplyRoomService;
import com.fangcang.hotel.framework.common.domain.PageResult;
import com.fangcang.hotel.framework.common.domain.ResultX;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.fangcang.hotel.framework.common.domain.ResultX.success;

/**
 * 供应商房型信息 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/supply")
@Validated
public class SupplyRoomController {

    @Resource
    private SupplyRoomService supplyRoomService;


    @PostMapping("/supplyRoomEdits")
    public ResultX supplyRoomEdits(@Valid @RequestBody List<SupplyRoomAditReqVO> supplyHotelReqDTOS) {
        supplyRoomService.supplyRoomEdits(supplyHotelReqDTOS);
        return ResultX.success();
    }

    @PostMapping("/supplyRoomList")
    public ResultX<List<SupplyRoomRespVO>> supplyRoomList(@Valid @RequestBody SupplyRoomQueryReqVO reqVO) {
        List<SupplyRoomDO> list = supplyRoomService.supplyRoomList(reqVO);
        return success(SupplyRoomConvert.INSTANCE.convertList(list));
    }

    @PostMapping("/supplyRoomPage")
    public ResultX<PageResult<SupplyRoomRespVO>> supplyRoomPage(@Valid @RequestBody SupplyRoomPageReqVO reqVO) {
        PageResult<SupplyRoomRespVO> supplyRoomRespVOPageResult = supplyRoomService.querySupplyRoomByPage(reqVO);
        return success(supplyRoomRespVOPageResult);
    }

}
