package com.fangcang.hotel.rjbld.dto.response.price;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  礼品礼包
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:14
 */
@Data
public class Gift implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 礼品包ID
     */
    @JSONField(name = "GiftID")
    private String giftID;

    /**
     * 礼品包名字
     */
    @JSONField(name = "GiftName")
    private String giftName;

    /**
     * 数量
     */
    @JSONField(name = "Qty")
    private Integer qty;

}
