package com.fangcang.hotel.common.dal.db.config;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.hotel.core.pojo.BaseDOX;
import lombok.*;

/**
 * 供应商信息 DO
 *
 * <AUTHOR>
 */
@TableName("t_htlsync_config_info")
@KeySequence("t_htlsync_config_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigInfoDO extends BaseDOX {

    /** id */
    @TableId
    private Long id;

    /** 供应商类型 */
    private String supplyClass;

    /** 商家编码 */
    private String merchantCode;

    /** 商家名称 */
    private String merchantName;

    /** 商家来源 */
    private String merchantSource;

    /** 供应商编码 */
    private String supplyCode;

    /** 供应商名称 */
    private String supplyName;

    /** 状态 [有效,无效] */
    private Integer status;

    /** 是否查询缓存 */
    private Integer isQueryCache;

    /** 是否更新缓存 */
    private Integer isUpdateCache;

    /** 缓存有效期（单位s） */
    private Integer cacheTime;

    /** 是否启用配置 0关闭 1开启 */
    private Integer configEnable;
}
