package com.fangcang.hotel.irevebld.xxljob;

import com.fangcang.hotel.irevebld.enums.SlsLevelEnum;
import com.fangcang.hotel.irevebld.service.IREVEBLDHotelService;
import com.fangcang.hotel.irevebld.service.impl.IREVEBLDLogServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 起价获取ireve不落地酒店id存进缓存任务
 */
@Slf4j
@Component
public class SyncLowestPriceIREVEBLDHotelIdToRedisRunner {

    @Autowired
    private IREVEBLDHotelService irevebldHotelService;

    @Autowired
    private IREVEBLDLogServiceImpl irevebldLogServiceImpl;

    @XxlJob("syncLowestPriceIREVEBLDHotelIdToRedisRunner")
    public void syncLowestPriceIREVEBLDHotelIdToRedisRunner() {
        try {
            XxlJobHelper.log("执行起价获取ireve不落地酒店oid存进缓存任务开始");
            irevebldHotelService.syncLowestPriceIREVEBLDHotelIdToRedisTask();
            XxlJobHelper.log("执行起价获取ireve不落地酒店id存进缓存任务结束");
        } catch (Exception e) {
            log.error("执行起价获取ireve不落地酒店id存进缓存任务异常", e);
            irevebldLogServiceImpl.saveSlsLog("执行起价获取ireve不落地酒店id存进缓存任务异常", SlsLevelEnum.ERROR, e);
            XxlJobHelper.log("执行起价获取ireve不落地酒店id存进缓存任务异常", e);
        }
    }
}
