package com.fangcang.hotel.rjbld.controller;

import com.fangcang.hotel.data.api.dto.order.OrderCheckDetailsResponse;
import com.fangcang.hotel.data.api.dto.order.QueryCheckDetailRequest;
import com.fangcang.hotel.data.api.dto.order.cancel.CancelSupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.cancel.CancelSupplyOrderResponse;
import com.fangcang.hotel.data.api.dto.order.create.CreateSupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.create.CreateSupplyOrderResponse;
import com.fangcang.hotel.data.api.dto.order.prebooking.PreBookingSupplyRequest;
import com.fangcang.hotel.data.api.dto.order.prebooking.PreBookingSupplyResponse;
import com.fangcang.hotel.data.api.dto.order.query.QuerySupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.query.QuerySupplyOrderResponse;
import com.fangcang.hotel.data.api.remote.SupplyOrderServiceRemote;
import com.fangcang.hotel.data.base.services.CommonOrderServices;
import com.fangcang.hotel.framework.common.domain.ResultX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class RJBLDOrderController implements SupplyOrderServiceRemote {

    @Autowired
    private CommonOrderServices commonOrderServices;

    /**
     *  订单入住详情
     * @param queryCheckDetailRequest 查询入住明细请求
     * @return 入住明细
     */
    @Override
    public ResultX<OrderCheckDetailsResponse> querySupplyOrderCheckDetail(QueryCheckDetailRequest queryCheckDetailRequest) {
        return commonOrderServices.querySupplyOrderCheckDetail(queryCheckDetailRequest);
    }

    /**
     * 供应商试预定
     *
     * @param preBookingSupplyRequest
     * @return
     */
    @Override
    public ResultX<PreBookingSupplyResponse> proBooking(PreBookingSupplyRequest preBookingSupplyRequest) {
        return commonOrderServices.proBooking(preBookingSupplyRequest);
    }

    /**
     * 供应商创建订单
     *
     * @param createSupplyOrderRequest
     * @return
     */
    @Override
    public ResultX<CreateSupplyOrderResponse> createSupplyOrder(CreateSupplyOrderRequest createSupplyOrderRequest) {
        return commonOrderServices.createSupplyOrder(createSupplyOrderRequest);
    }

    /**
     * 供应商查询订单
     *
     * @param querySupplyOrderRequest
     * @return
     */
    @Override
    public ResultX<QuerySupplyOrderResponse> querySupplyOrder(QuerySupplyOrderRequest querySupplyOrderRequest) {
        return commonOrderServices.querySupplyOrder(querySupplyOrderRequest);
    }

    /**
     * 供应商取消订单
     *
     * @param cancelSupplyOrderRequest
     * @return
     */
    @Override
    public ResultX<CancelSupplyOrderResponse> cancelSupplyOrder(CancelSupplyOrderRequest cancelSupplyOrderRequest) {
        return commonOrderServices.cancelSupplyOrder(cancelSupplyOrderRequest);
    }

}
