package com.fangcang.hotel.huizhiosbld.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/10/30 10:57
 * @Description:
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckAvailabilityNightlyRateResponse {

    /**
     * 日期
     */
    private String date;

    /**
     * 价格
     */
    private BigDecimal cost;

    /**
     * 房态
     */
    private Integer status;

    /**
     * 房量
     */
    private Integer roomnum;

    /**
     * 可超售
     */
    private Integer is_oversell;

    /**
     * 最大入住人数
     */
    private Integer max_occupancy;

    /**
     * 早餐数
     */
    private Integer breakfast_count;

}
