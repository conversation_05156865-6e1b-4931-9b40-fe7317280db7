package com.fangcang.hotel.rjbld.dto.response.price;

import java.util.Date;
import java.util.Date;
import java.util.Date;
import java.util.Date;
import java.util.List;
import com.fangcang.hotel.rjbld.dto.response.price.Commission;
import java.util.List;
import com.fangcang.hotel.rjbld.dto.response.price.ExclsvDate;
import java.util.List;
import com.fangcang.hotel.rjbld.dto.response.price.CancelRule;
import java.util.List;
import com.fangcang.hotel.rjbld.dto.response.price.DepositRule;
import java.util.List;
import com.fangcang.hotel.rjbld.dto.response.price.Gift;
import java.util.List;
import com.fangcang.hotel.rjbld.dto.response.price.PromotionPolicyItems;
import java.util.List;
import com.fangcang.hotel.rjbld.dto.response.price.PromotionPolicyCombinationProduct;
import java.util.List;
import com.fangcang.hotel.rjbld.dto.response.price.PromotionRuleConditions;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  促销
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:15
 */
@Data
public class Promotion implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 促销ID
     */
    @JSONField(name = "PromotionID")
    private String promotionID;

    /**
     * 促销名称
     */
    @JSONField(name = "PromotionName")
    private String promotionName;

    /**
     * 促销内容
     */
    @JSONField(name = "PromotionContent")
    private String promotionContent;

    /**
     * 活动类型
     */
    @JSONField(name = "ActivityType")
    private String activityType;

    /**
     * 促销类型
     */
    @JSONField(name = "PromotionType")
    private String promotionType;

    /**
     * 活动开始日期
     */
    @JSONField(name = "StartDate")
    private Date startDate;

    /**
     * 活动结束日期
     */
    @JSONField(name = "EndDate")
    private Date endDate;

    /**
     * 价格开始日期
     */
    @JSONField(name = "PriceStartDate")
    private Date priceStartDate;

    /**
     * 价格结束日期
     */
    @JSONField(name = "PriceEndDate")
    private Date priceEndDate;

    /**
     * 品牌ID
     */
    @JSONField(name = "Brandlds")
    private String brandlds;

    /**
     * 品牌名称
     */
    @JSONField(name = "BrandNames")
    private String brandNames;

    /**
     * 参与酒店
     */
    @JSONField(name = "HotelCds")
    private String hotelCds;

    /**
     * 参与会员
     */
    @JSONField(name = "MemberShips")
    private String memberShips;

    /**
     * 是否有效
     */
    @JSONField(name = "IsValid")
    private String isValid;

    /**
     * 支付类型
     */
    @JSONField(name = "PaymentType")
    private String paymentType;

    /**
     * 商务日方产品时长
     */
    @JSONField(name = "HourRentTime")
    private String hourRentTime;

    /**
     * 预付产品扣款类型
     */
    @JSONField(name = "DeductType")
    private String deductType;

    /**
     * 担保类型
     */
    @JSONField(name = "GuaranteeType")
    private String guaranteeType;

    /**
     * 保留时间
     */
    @JSONField(name = "KeepTime")
    private String keepTime;

    /**
     * 到店时间
     */
    @JSONField(name = "Arrival_time")
    private String arrivalTime;

    /**
     * 是否要求到店时间
     */
    @JSONField(name = "Arrival_time_required")
    private String arrivalTimeRequired;

    /**
     * 佣金
     */
    @JSONField(name = "Commission")
    private List<Commission> commission;

    /**
     * 提前预定天数
     */
    @JSONField(name = "BookInAdvance")
    private String bookInAdvance;

    /**
     * 排他日期
     */
    @JSONField(name = "ExclsvDate")
    private List<ExclsvDate> exclsvDate;

    /**
     * 限时特惠
     */
    @JSONField(name = "limttime")
    private String limttime;

    /**
     * 连住天数
     */
    @JSONField(name = "Resvdays")
    private String resvdays;

    /**
     * 星期数
     */
    @JSONField(name = "Weekday")
    private String weekday;

    /**
     * 取消规则
     */
    @JSONField(name = "CancelRules")
    private List<CancelRule> cancelRules;

    /**
     * 保证金规则
     */
    @JSONField(name = "DepositRules")
    private List<DepositRule> depositRules;

    /**
     * 礼品
     */
    @JSONField(name = "Gifts")
    private List<Gift> gifts;

    /**
     * 促销政策
     */
    @JSONField(name = "PromotionPolicyItems")
    private List<PromotionPolicyItems> promotionPolicyItems;

    /**
     * 组合产品
     */
    @JSONField(name = "PromotionPolicyCombinationProduct")
    private List<PromotionPolicyCombinationProduct> promotionPolicyCombinationProduct;

    /**
     * 促销条件
     */
    @JSONField(name = "PromotionRuleConditions")
    private List<PromotionRuleConditions> promotionRuleConditions;

}
