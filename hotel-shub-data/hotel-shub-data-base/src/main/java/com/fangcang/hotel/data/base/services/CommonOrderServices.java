package com.fangcang.hotel.data.base.services;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fangcang.hotel.core.enums.GlobalErrorCodeEnum;
import com.fangcang.hotel.core.util.StringUtilExtend;
import com.fangcang.hotel.data.api.constants.BaseConstant;
import com.fangcang.hotel.data.api.constants.MqConstants;
import com.fangcang.hotel.data.api.constants.RedisKeyConstants;
import com.fangcang.hotel.data.api.dto.EsLogDTO;
import com.fangcang.hotel.data.api.dto.RoomGuestNumberDTO;
import com.fangcang.hotel.data.api.dto.SupplyProductRequest;
import com.fangcang.hotel.data.api.dto.SupplyProductResponse;
import com.fangcang.hotel.data.api.dto.order.OrderCheckDetailsResponse;
import com.fangcang.hotel.data.api.dto.order.OrderRoomDetailDTO;
import com.fangcang.hotel.data.api.dto.order.PriceInfoDetail;
import com.fangcang.hotel.data.api.dto.order.QueryCheckDetailRequest;
import com.fangcang.hotel.data.api.dto.order.cancel.CancelSupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.cancel.CancelSupplyOrderResponse;
import com.fangcang.hotel.data.api.dto.order.create.CreateSupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.create.CreateSupplyOrderResponse;
import com.fangcang.hotel.data.api.dto.order.prebooking.PreBookingSupplyRequest;
import com.fangcang.hotel.data.api.dto.order.prebooking.PreBookingSupplyResponse;
import com.fangcang.hotel.data.api.dto.order.query.QuerySupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.query.QuerySupplyOrderResponse;
import com.fangcang.hotel.data.api.enums.CanBookEnum;
import com.fangcang.hotel.data.api.enums.OrderStatusEnum;
import com.fangcang.hotel.data.api.enums.RoomStateEnum;
import com.fangcang.hotel.data.api.enums.SupplyTypeEnum;
import com.fangcang.hotel.data.api.service.SupplyOrderService;
import com.fangcang.hotel.data.base.config.DataBaseConfig;
import com.fangcang.hotel.data.base.constants.LogIndexSuffixConstants;
import com.fangcang.hotel.data.base.convert.RoomGuestNumbersConvert;
import com.fangcang.hotel.data.base.util.CommonUtil;
import com.fangcang.hotel.data.base.util.LogIndexUtil;
import com.fangcang.hotel.framework.common.domain.ResultX;
import com.fangcang.hotel.framework.common.exception.BizException;
import com.fangcang.hotel.framework.common.util.DateUtilX;
import com.fangcang.hotel.framework.operatelog.auto.SlsLogger;
import com.fangcang.hotel.framework.operatelog.core.sls.SlsEnum;
import com.fangcang.hotel.framework.redis.core.RedisTemplateX;
import lombok.extern.slf4j.Slf4j;
import net.openhft.hashing.LongHashFunction;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 基础订单服务
 * @author: qiu
 * @create: 2023-11-02 11:59
 */
@Slf4j
@Service
public class CommonOrderServices {

    @Autowired
    private RegisterSupplyService registerSupplyService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private SlsLogger slsLogger;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private DataBaseConfig dataBaseConfig;

    public ResultX<OrderCheckDetailsResponse> querySupplyOrderCheckDetail(QueryCheckDetailRequest queryCheckDetailRequest) {
        Date startdate = new Date();
        ResultX<OrderCheckDetailsResponse> resultX = null;

        String supplyClass = queryCheckDetailRequest.getSupplyClass();
        String index = LogIndexUtil.getIndex(supplyClass, LogIndexSuffixConstants.SUPPLYORDERCHECKDETAIL);
        try {
            SupplyOrderService orderService = registerSupplyService.getOrderService(supplyClass);
            //返回
            if (orderService == null) {
                log.error("查询入住明细无法根据供应类型获取对应实时查询服务,{} ", supplyClass);
                resultX = ResultX.error(GlobalErrorCodeEnum.SUPPLY_SERVICE_NOT_FOUND);
            } else {
                resultX = orderService.querySupplyOrderCheckDetail(queryCheckDetailRequest);
            }

        } catch (Exception e) {
            if (e instanceof BizException) {
                resultX = ResultX.error(((BizException) e).getNumber(), ((BizException) e).getCode(), ((BizException) e).getMessage());
            } else {
                resultX = ResultX.error(GlobalErrorCodeEnum.SYSTEM_ERROR);
            }
            log.error("查询入住明细异常", e);
            slsLogger.saveErrorLog(e);
        } finally {
            //SLS日志接入
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "info");
            map.put(SlsEnum.NAME.getType(), index);
            map.put(SlsEnum.MESSAGE.getType(), "查询供应商入住明细");
            map.put("requestId", queryCheckDetailRequest.getRequestId());
            if (resultX != null && resultX.getData() != null) {
                if (resultX.getData().getResponseTime() != null && resultX.getData().getRequestTime() != null) {
                    map.put("requestTime", DateUtilX.dateToString(resultX.getData().getRequestTime(), DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    map.put("responseTime", DateUtilX.dateToString(resultX.getData().getResponseTime(), DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    map.put("supplyCostTime", String.valueOf(resultX.getData().getResponseTime().getTime() - resultX.getData().getRequestTime().getTime()));
                }
                map.put("requestContent", resultX.getData().getRequestContent());
                map.put("responseContent", resultX.getData().getResponseContent());
                map.put("status", "1");
                //日志数据删除不在进行记录
                resultX.getData();
                resultX.getData().setResponseContent(null);
                resultX.getData().setRequestTime(null);
                resultX.getData().setResponseTime(null);
            } else {
                map.put("request",  JSONUtil.toJsonStr(queryCheckDetailRequest));
                map.put("response",  JSONUtil.toJsonStr(resultX));
                map.put("status", "0");
            }
            map.put("costTime", String.valueOf(startdate.getTime() - System.currentTimeMillis()));
            map.put("request",  JSONUtil.toJsonStr(queryCheckDetailRequest));
            map.put("response",  JSONUtil.toJsonStr(resultX));
            map.put("MerchantSource", queryCheckDetailRequest.getMerchantSource());
            map.put("MerchantCode", queryCheckDetailRequest.getMerchantCode());
            String topic = "查询供应商入住明细";
            String source = index;
            slsLogger.saveLog(map, topic, source);

            return resultX;
        }
    }

    /**
     * 试预定
     *
     * @param preBookingSupplyRequest
     * @return
     */
    public ResultX<PreBookingSupplyResponse> proBooking(PreBookingSupplyRequest preBookingSupplyRequest) {
        Date startdate = new Date();
        ResultX<PreBookingSupplyResponse> resultX = null;

        String supplyClass = preBookingSupplyRequest.getSupplyClass();

        String applicationName = LogIndexUtil.getIndex(supplyClass, LogIndexSuffixConstants.PRE_BOOKING);
        try {
            SupplyOrderService orderService = registerSupplyService.getOrderService(supplyClass);
            //返回
            if (orderService == null) {
                log.error("试预定无法根据供应类型获取对应实时查询服务,{} ", supplyClass);
                resultX = ResultX.error(GlobalErrorCodeEnum.SUPPLY_SERVICE_NOT_FOUND);
            } else {
                resultX = orderService.preBooking(preBookingSupplyRequest);

                /**
                 * 是否国内供应商家类型 国内 true 海外 false
                 */
                boolean cacheSupplyInfo = cacheService.getCacheSupplyInfo(SupplyTypeEnum.DOMESTIC, preBookingSupplyRequest.getSupplyClass());

                if (resultX.isSuccess() && resultX.getData() != null) {
                    if (cacheSupplyInfo) {
                        //国内供应商 设置海外数据为空
                        resultX.getData().setOrderRoomDetails(null);
                    } else {
                        //海外供应商 国内数据设置为空
                        resultX.getData().setPriceInfoDetails(null);
                    }
                }
                //统一处理
                preBookingResponseHandle(preBookingSupplyRequest, resultX.getData());
            }
        } catch (Exception e) {
            if (e instanceof BizException) {
                resultX = ResultX.error(((BizException) e).getNumber(), ((BizException) e).getCode(), ((BizException) e).getMessage());
            } else {
                resultX = ResultX.error(GlobalErrorCodeEnum.SYSTEM_ERROR);
            }
            log.error("供应商试预定异常", e);
            slsLogger.saveErrorLog(e);
        } finally {
            //SLS日志接入
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "info");
            map.put(SlsEnum.NAME.getType(), applicationName);
            map.put(SlsEnum.MESSAGE.getType(), "试预定");
            map.put("requestId", preBookingSupplyRequest.getRequestId());
            if (resultX.getData() != null) {
                if (resultX.getData().getResponseTime() != null && resultX.getData().getRequestTime() != null) {
                    map.put("requestTime", DateUtilX.dateToString(resultX.getData().getRequestTime(), DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    map.put("responseTime", DateUtilX.dateToString(resultX.getData().getResponseTime(), DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    map.put("supplyCostTime", String.valueOf(resultX.getData().getResponseTime().getTime() - resultX.getData().getRequestTime().getTime()));
                }
                map.put("requestContent", resultX.getData().getRequestContent());
                map.put("responseContent", resultX.getData().getResponseContent());
                map.put("status", "1");
                resultX.getData().setRequestContent(null);
                resultX.getData().setResponseContent(null);
                resultX.getData().setRequestTime(null);
                resultX.getData().setResponseTime(null);
            } else {
                map.put("status", "0");
            }
            map.put("costTime", String.valueOf(startdate.getTime() - System.currentTimeMillis()));
            map.put("request",  JSONUtil.toJsonStr(preBookingSupplyRequest));
            map.put("response",  JSONUtil.toJsonStr(resultX));
            map.put("MerchantSource", preBookingSupplyRequest.getMerchantSource());
            map.put("MerchantCode", preBookingSupplyRequest.getMerchantCode());
            String topic = "试预定";
            slsLogger.saveLog(map, topic, applicationName);
            savePreBookingEsLog(applicationName,preBookingSupplyRequest,resultX);
            //查询产品时执行缓存的删除操作
            deleteSupplyHotelDetailCache(preBookingSupplyRequest);
            return resultX;
        }
    }


    /**
     * 保存试预定产品日志
     * @param applicationName 应用名称
     * @param preBookingSupplyRequest 供应商请求参数
     * @param resultX 供应商响应结果
     */
    private void savePreBookingEsLog(String applicationName, PreBookingSupplyRequest preBookingSupplyRequest, ResultX<PreBookingSupplyResponse> resultX){
        EsLogDTO esLogDTO = new EsLogDTO();
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        esLogDTO.setLog_id(snowflake.nextId());
        esLogDTO.setTrace_id(preBookingSupplyRequest.getRequestId());
        esLogDTO.setApplication_name(applicationName);
        esLogDTO.setAccess_ip(preBookingSupplyRequest.getCustomerIp());
        esLogDTO.setHotel_id(preBookingSupplyRequest.getHotelId());
        esLogDTO.setSupplier_code(preBookingSupplyRequest.getSupplyCode());
        esLogDTO.setRequest_url(BaseConstant.PRE_BOOKING_URL);
        esLogDTO.setStart_dt(resultX.getData().getRequestTime().getTime());
        esLogDTO.setEnd_dt(resultX.getData().getResponseTime().getTime());
        esLogDTO.setRequest_params(JSONUtil.toJsonStr(preBookingSupplyRequest));
        esLogDTO.setResponse_data(JSONUtil.toJsonStr(resultX));
        esLogDTO.setResponse_code(resultX.getCode());
        esLogDTO.setResponse_msg(resultX.getMsg());
        rocketMQTemplate.asyncSend(MqConstants.SUPPLY_ES_LOG_TOPIC, esLogDTO, null);
    }


    private void preBookingResponseHandle(PreBookingSupplyRequest preBookingSupplyRequest, PreBookingSupplyResponse data) {
        if (data != null) {
            if (StrUtil.isBlank(data.getSupplyRateId())) {
                data.setSupplyRateId(preBookingSupplyRequest.getSupplyRateId());
            }
            if (data.getOrderRoomDetails() != null && CollectionUtil.isNotEmpty(data.getOrderRoomDetails())) {
                for (OrderRoomDetailDTO orderRoomDetail : data.getOrderRoomDetails()) {
                    prePriceInfo(preBookingSupplyRequest, orderRoomDetail.getPriceInfoDetails(), data);
                }
            } else if (data.getPriceInfoDetails() != null && CollectionUtil.isNotEmpty(data.getPriceInfoDetails())) {
                prePriceInfo(preBookingSupplyRequest, data.getPriceInfoDetails(), data);
            }else {
                // 没有价格信息 则整体不可预订
                data.setCanBook(CanBookEnum.CAN_NOT_BOOK.value);
            }
        }
    }

    private void prePriceInfo(PreBookingSupplyRequest preBookingSupplyRequest, List<PriceInfoDetail> priceInfoDetails, PreBookingSupplyResponse data) {
        if (CollectionUtil.isNotEmpty(priceInfoDetails)) {
            for (PriceInfoDetail priceInfoDetail : priceInfoDetails) {
                if (priceInfoDetail.getQuotaNum() != null) {
                    if ((priceInfoDetail.getQuotaNum() < preBookingSupplyRequest.getRoomNum()) && (priceInfoDetail.getCanOverDraft() != null && priceInfoDetail.getCanOverDraft() != 1)) {
                        // 有房 不可定 且 不可超 不满足间数
                        priceInfoDetail.setCanbook(CanBookEnum.CAN_NOT_BOOK.value);
                        // 某日不可预定则整体不可预定
                        data.setCanBook(CanBookEnum.CAN_NOT_BOOK.value);
                    } else if (priceInfoDetail.getBasePrice() == null || priceInfoDetail.getBasePrice().compareTo(BigDecimal.ZERO) == 0) {
                        priceInfoDetail.setBasePrice(BigDecimal.ZERO);
                        // 没有每日价格 | 价格为0 则不可定
                        priceInfoDetail.setCanbook(CanBookEnum.CAN_NOT_BOOK.value);
                        // 整体不可预定
                        data.setCanBook(CanBookEnum.CAN_NOT_BOOK.value);
                    }
                } else {
                    // 空 满房
                    priceInfoDetail.setQuotaNum(0);
                    priceInfoDetail.setRoomStatus(RoomStateEnum.FULL_ROOM.key);
                    priceInfoDetail.setCanbook(CanBookEnum.CAN_NOT_BOOK.value);
                    // 整体不可预定
                    data.setCanBook(CanBookEnum.CAN_NOT_BOOK.value);
                }
            }
        }else {
            // 没有价格信息 则整体不可预订
            data.setCanBook(CanBookEnum.CAN_NOT_BOOK.value);
        }
    }

    /**
     * 创建订单
     *
     * @param createSupplyOrderRequest
     * @return
     */
    public ResultX<CreateSupplyOrderResponse> createSupplyOrder(CreateSupplyOrderRequest createSupplyOrderRequest) {
        Date startdate = new Date();
        ResultX<CreateSupplyOrderResponse> resultX = null;

        String supplyClass = createSupplyOrderRequest.getSupplyClass();

        //根据日期升序排序
        List<PriceInfoDetail> priceInfoDetails = createSupplyOrderRequest.getPriceInfoDetails().stream().sorted(Comparator.comparing(PriceInfoDetail::getSaleDate)).collect(Collectors.toList());
        createSupplyOrderRequest.setPriceInfoDetails(priceInfoDetails);

        String applicationName = LogIndexUtil.getIndex(supplyClass, LogIndexSuffixConstants.CREATE_ORDER);
        try {
            SupplyOrderService orderService = registerSupplyService.getOrderService(supplyClass);
            //返回
            if (orderService == null) {
                log.error("创建订单无法根据供应类型获取对应实时查询服务,{} ", supplyClass);
                resultX = ResultX.error(GlobalErrorCodeEnum.SUPPLY_SERVICE_NOT_FOUND);
            } else {
                resultX = orderService.createSupplierOrder(createSupplyOrderRequest);
            }
        } catch (Exception e) {
            if (e instanceof BizException) {
                resultX = ResultX.error(((BizException) e).getNumber(),((BizException) e).getCode(), ((BizException) e).getMessage());
            } else {
                resultX = ResultX.error(GlobalErrorCodeEnum.SYSTEM_ERROR);
            }
            log.error("供应商创建订单异常", e);
            slsLogger.saveErrorLog(e);
        } finally {
            //SLS日志接入
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "info");
            map.put(SlsEnum.NAME.getType(), applicationName);
            map.put(SlsEnum.MESSAGE.getType(), "创建订单");
            map.put("requestId", createSupplyOrderRequest.getRequestId());
            if (resultX.getData() != null) {
                if (resultX.getData().getResponseTime() != null && resultX.getData().getRequestTime() != null) {
                    map.put("requestTime", DateUtilX.dateToString(resultX.getData().getRequestTime(), DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    map.put("responseTime", DateUtilX.dateToString(resultX.getData().getResponseTime(), DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    map.put("supplyCostTime", String.valueOf(resultX.getData().getResponseTime().getTime() - resultX.getData().getRequestTime().getTime()));
                }
                map.put("requestContent", resultX.getData().getRequestContent());
                map.put("responseContent", resultX.getData().getResponseContent());
                map.put("status", "1");
                resultX.getData().setRequestContent(null);
                resultX.getData().setResponseContent(null);
                resultX.getData().setRequestTime(null);
                resultX.getData().setResponseTime(null);
            } else {
                map.put("status", "0");
            }
            map.put("costTime", String.valueOf(startdate.getTime() - System.currentTimeMillis()));
            map.put("request",  JSONUtil.toJsonStr(createSupplyOrderRequest));
            map.put("response",  JSONUtil.toJsonStr(resultX));
            map.put("MerchantSource", createSupplyOrderRequest.getMerchantSource());
            map.put("MerchantCode", createSupplyOrderRequest.getMerchantCode());
            String topic = "创建订单";
            slsLogger.saveLog(map, topic, applicationName);
            return resultX;
        }
    }


    /**
     * 查询订单状态
     *
     * @param querySupplyOrderRequest
     * @return
     */
    public ResultX<QuerySupplyOrderResponse> querySupplyOrder(QuerySupplyOrderRequest querySupplyOrderRequest) {
        Date startdate = new Date();
        ResultX<QuerySupplyOrderResponse> resultX = null;

        String supplyClass = querySupplyOrderRequest.getSupplyClass();
        String index = LogIndexUtil.getIndex(supplyClass, LogIndexSuffixConstants.QUERY_ORDER);
        try {
            SupplyOrderService orderService = registerSupplyService.getOrderService(supplyClass);
            if (orderService == null) {
                log.error("查询订单无法根据供应类型获取对应实时查询服务,{} ", supplyClass);
                resultX = ResultX.error(GlobalErrorCodeEnum.SUPPLY_SERVICE_NOT_FOUND);
            } else {
                resultX = orderService.querySupplierOrderStatus(querySupplyOrderRequest);
            }
        } catch (Exception e) {
            if (e instanceof BizException) {
                resultX = ResultX.error(((BizException) e).getNumber(),((BizException) e).getCode(), ((BizException) e).getMessage());
            } else {
                resultX = ResultX.error(GlobalErrorCodeEnum.SYSTEM_ERROR);
            }
            log.error("供应商查询订单异常", e);
            slsLogger.saveErrorLog(e);
            resultX = ResultX.error(GlobalErrorCodeEnum.SYSTEM_ERROR);
        } finally {
            //SLS日志接入
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "info");
            map.put(SlsEnum.NAME.getType(), index);
            map.put(SlsEnum.MESSAGE.getType(), "查询订单");
            map.put("requestId", querySupplyOrderRequest.getRequestId());
            if (resultX.getData() != null) {
                if (resultX.getData().getResponseTime() != null && resultX.getData().getRequestTime() != null) {
                    map.put("requestTime", DateUtilX.dateToString(resultX.getData().getRequestTime(), DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    map.put("responseTime", DateUtilX.dateToString(resultX.getData().getResponseTime(), DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    map.put("supplyCostTime", String.valueOf(resultX.getData().getResponseTime().getTime() - resultX.getData().getRequestTime().getTime()));
                }
                map.put("requestContent", resultX.getData().getRequestContent());
                map.put("responseContent", resultX.getData().getResponseContent());
                map.put("status", "1");
                resultX.getData().setRequestContent(null);
                resultX.getData().setResponseContent(null);
                resultX.getData().setRequestTime(null);
                resultX.getData().setResponseTime(null);
            } else {
                map.put("status", "0");
            }

            map.put("costTime", String.valueOf(startdate.getTime() - System.currentTimeMillis()));
            map.put("request",  JSONUtil.toJsonStr(querySupplyOrderRequest));
            map.put("response",  JSONUtil.toJsonStr(resultX));
            map.put("MerchantSource", querySupplyOrderRequest.getMerchantSource());
            map.put("MerchantCode", querySupplyOrderRequest.getMerchantCode());
            String topic = "查询订单";
            String source = index;
            slsLogger.saveLog(map, topic, source);

            return resultX;
        }
    }


    /**
     * 取消订单
     *
     * @param cancelSupplyOrderRequest
     * @return
     */
    public ResultX<CancelSupplyOrderResponse> cancelSupplyOrder(CancelSupplyOrderRequest cancelSupplyOrderRequest) {
        Date startdate = new Date();
        ResultX<CancelSupplyOrderResponse> resultX = null;
        String supplyClass = cancelSupplyOrderRequest.getSupplyClass();
        String index = LogIndexUtil.getIndex(supplyClass, LogIndexSuffixConstants.CANCEL_ORDER);
        try {
            SupplyOrderService orderService = registerSupplyService.getOrderService(supplyClass);
            if (orderService == null) {
                log.error("取消订单无法根据供应类型获取对应实时查询服务,{}", supplyClass);
                resultX = ResultX.error(GlobalErrorCodeEnum.SUPPLY_SERVICE_NOT_FOUND);
            } else {
                resultX = orderService.cancelSupplierOrder(cancelSupplyOrderRequest);
            }
        } catch (Exception e) {
            if (e instanceof BizException) {
                resultX = ResultX.error(((BizException) e).getNumber(),((BizException) e).getCode(), ((BizException) e).getMessage());
            } else {
                resultX = ResultX.error(GlobalErrorCodeEnum.SYSTEM_ERROR);
            }
            log.error("供应商取消订单异常", e);
            slsLogger.saveErrorLog(e);
        } finally {
            //SLS日志接入
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "info");
            map.put(SlsEnum.NAME.getType(), index);
            map.put(SlsEnum.MESSAGE.getType(), "取消订单");
            map.put("requestId", cancelSupplyOrderRequest.getRequestId());
            if (resultX.getData() != null) {
                if (resultX.getData().getResponseTime() != null && resultX.getData().getRequestTime() != null) {
                    map.put("requestTime", DateUtilX.dateToString(resultX.getData().getRequestTime(), DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    map.put("responseTime", DateUtilX.dateToString(resultX.getData().getResponseTime(), DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    map.put("supplyCostTime", String.valueOf(resultX.getData().getResponseTime().getTime() - resultX.getData().getRequestTime().getTime()));
                }
                map.put("requestContent", resultX.getData().getRequestContent());
                map.put("responseContent", resultX.getData().getResponseContent());
                map.put("status", "1");
                resultX.getData().setRequestContent(null);
                resultX.getData().setResponseContent(null);
                resultX.getData().setRequestTime(null);
                resultX.getData().setResponseTime(null);
            } else {
                map.put("status", "0");
            }
            map.put("request",  JSONUtil.toJsonStr(cancelSupplyOrderRequest));
            map.put("response",  JSONUtil.toJsonStr(resultX));
            map.put("costTime", String.valueOf(startdate.getTime() - System.currentTimeMillis()));
            map.put("MerchantSource", cancelSupplyOrderRequest.getMerchantSource());
            map.put("MerchantCode", cancelSupplyOrderRequest.getMerchantCode());
            String topic = "取消订单";
            String source = index;
            slsLogger.saveLog(map, topic, source);
            return resultX;
        }
    }

    /**
     * 删除供应商酒店详情缓存
     * 查询产品时执行缓存的删除操作
     *
     * @param preBookingSupplyRequest 试预定的请求对象，包含详情查询所需的信息
     */
    private void deleteSupplyHotelDetailCache(PreBookingSupplyRequest preBookingSupplyRequest) {
        // 查询供应方产品语言配置有效
        if (StringUtilExtend.isValidString(dataBaseConfig.getQuerySupplyProductLanguage())) {
            // 获取供应商类别
            String supplyClass = preBookingSupplyRequest.getSupplyClass();
            // 遍历每种语言，删除对应缓存
            String splitStr = ",|，";
            for (String language : dataBaseConfig.getQuerySupplyProductLanguage().split(splitStr)) {
                // 拼装cacheKey
                String roomGuestNumberStr = CommonUtil.getRoomGuestNumberStr(preBookingSupplyRequest.getRoomGuestNumbers());
                String supplyHotelDetailCacheKey = getSupplyHotelDetailCacheKey(preBookingSupplyRequest.getSupplyCode(), preBookingSupplyRequest.getHotelId(),
                        DateUtilX.dateToString(preBookingSupplyRequest.getCheckInDate()), DateUtilX.dateToString(preBookingSupplyRequest.getCheckOutDate()),
                        preBookingSupplyRequest.getNationality(), language,
                        roomGuestNumberStr, Objects.isNull(preBookingSupplyRequest.getCurrency()) ? null : preBookingSupplyRequest.getCurrency().toString());

                // 将cacheKey进行哈希计算,得到一个Long值,作为缓存的key
                byte[] dataBytes = supplyHotelDetailCacheKey.getBytes(java.nio.charset.StandardCharsets.UTF_8);
                String cacheKey = RedisKeyConstants.SUPPLY_QUERY_HOTEL_DETAIL + supplyClass + ":" + LongHashFunction.xx3().hashBytes(dataBytes);
                // 删除Redis中的缓存键
                RedisTemplateX.delete(cacheKey);
            }
        }
    }


    private String getSupplyHotelDetailCacheKey(String... str) {
        return String.join("_", str);
    }
}