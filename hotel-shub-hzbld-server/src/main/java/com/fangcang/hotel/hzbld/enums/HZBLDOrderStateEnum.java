package com.fangcang.hotel.hzbld.enums;

import com.fangcang.hotel.data.api.enums.CheckInStateEnum;

/**
 *
 *  入住状态枚举
 *
 * <AUTHOR>
 * @date 2023-12-11 14:22:35
 */
public enum HZBLDOrderStateEnum {

    O("O", "Left", CheckInStateEnum.LEFT),
    N("N","NoShow",CheckInStateEnum.NOSHOW);

    public String key;

    public String name;

    public CheckInStateEnum value;


    private HZBLDOrderStateEnum (String key, String name, CheckInStateEnum value) {
        this.key = key;
        this.name = name;
        this.value = value;
    }

    public static HZBLDOrderStateEnum getFCOrederState(String key) {
        for (HZBLDOrderStateEnum responseEnum : HZBLDOrderStateEnum.values()) {
            if (responseEnum.key.equals(key)) {
                return responseEnum;
            }
        }
        return null;
    }

}
