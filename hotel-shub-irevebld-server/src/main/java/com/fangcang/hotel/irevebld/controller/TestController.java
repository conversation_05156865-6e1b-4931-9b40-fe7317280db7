package com.fangcang.hotel.irevebld.controller;

import com.fangcang.hotel.framework.common.domain.ResultX;
import com.fangcang.hotel.irevebld.enums.SlsLevelEnum;
import com.fangcang.hotel.irevebld.service.IREVEBLDHotelService;
import com.fangcang.hotel.irevebld.service.impl.IREVEBLDLogServiceImpl;
import com.fangcang.hotel.irevebld.service.impl.IREVEBLDOrderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.fangcang.hotel.framework.common.domain.ResultX.success;

/**
 * 测试接口
 */
@Slf4j
@RestController
@RequestMapping("/hotel-shub-irevebld-server/irevebld")
public class TestController {

    @Autowired
    private IREVEBLDHotelService irevebldHotelService;

    @Autowired
    private IREVEBLDOrderServiceImpl irevebldOrderService;

    @Autowired
    private IREVEBLDLogServiceImpl irevebldLogServiceImpl;

    @PostMapping("/calculateIREVEBLDHotelLowestPriceRunner")
    public ResultX<Object> calculateIREVEBLDHotelLowestPriceRunner() {
        try {
            irevebldHotelService.calculateIREVEBLDHotelLowestPriceTask();
        } catch (Exception e) {
            log.error("执行计算ireve不落地酒店起价任务异常", e);
            irevebldLogServiceImpl.saveSlsLog("执行计算ireve不落地酒店起价任务异常", SlsLevelEnum.ERROR, e);
        }
        return success(null);
    }

    @PostMapping("/syncAllIREVEBLDHotelIdToRedisRunner")
    public ResultX<Object> syncAllIREVEBLDHotelIdToRedisRunner() {
        try {
            irevebldHotelService.syncAllIREVEBLDHotelIdToRedisTask();
        } catch (Exception e) {
            log.error("执行全量获取ireve不落地酒店ID清单存进缓存任务异常", e);
            irevebldLogServiceImpl.saveSlsLog("执行全量获取ireve不落地酒店ID清单存进缓存任务异常", SlsLevelEnum.ERROR, e);
        }
        return success(null);
    }

    @PostMapping("/syncAllIREVEBLDHotelInfoRunner")
    public ResultX<Object> syncAllIREVEBLDHotelInfoRunner() {
        try {
            irevebldHotelService.syncAllIREVEBLDHotelInfoRunnerTask();
        } catch (Exception e) {
            log.error("执行全量同步ireve不落地酒店信息任务异常", e);
            irevebldLogServiceImpl.saveSlsLog("执行全量同步ireve不落地酒店信息任务异常", SlsLevelEnum.ERROR, e);
        }
        return success(null);
    }

    @PostMapping("/syncIncrementIREVEBLDHotelIdToRedisRunner")
    public ResultX<Object> syncIncrementIREVEBLDHotelIdToRedisRunner() {
        try {
            irevebldHotelService.syncIncrementIREVEBLDHotelIdToRedisTask();
        } catch (Exception e) {
            log.error("执行增量获取ireve不落地酒店id清单存进缓存任务异常", e);
            irevebldLogServiceImpl.saveSlsLog("执行增量获取ireve不落地酒店id清单存进缓存任务异常", SlsLevelEnum.ERROR, e);
        }
        return success(null);
    }

    @PostMapping("/syncIncrementIREVEBLDHotelInfoRunner")
    public ResultX<Object> syncIncrementIREVEBLDHotelInfoRunner() {
        try {
            irevebldHotelService.syncIncrementIREVEBLDHotelInfoTask();
        } catch (Exception e) {
            log.error("执行增量同步ireve不落地酒店信息任务异常", e);
            irevebldLogServiceImpl.saveSlsLog("执行增量同步ireve不落地酒店信息任务异常", SlsLevelEnum.ERROR, e);
        }
        return success(null);
    }

    @PostMapping("/syncIREVEBLDOrderStatusRunner")
    public ResultX<Object> syncIREVEBLDOrderStatusRunner() {
        try {
            irevebldOrderService.syncIREVEBLDOrderStatusTask();
        } catch (Exception e) {
            log.error("执行同步ireve不落地订单状态任务异常", e);
            irevebldLogServiceImpl.saveSlsLog("执行同步ireve不落地订单状态任务异常", SlsLevelEnum.ERROR, e);
        }
        return success(null);
    }

    @PostMapping("/syncLowestPriceIREVEBLDHotelIdToRedisRunner")
    public ResultX<Object> syncLowestPriceIREVEBLDHotelIdToRedisRunner() {
        try {
            irevebldHotelService.syncLowestPriceIREVEBLDHotelIdToRedisTask();
        } catch (Exception e) {
            log.error("执行起价获取ireve不落地酒店ID存进缓存任务异常", e);
            irevebldLogServiceImpl.saveSlsLog("执行起价获取ireve不落地酒店ID存进缓存任务异常", SlsLevelEnum.ERROR, e);
        }
        return success(null);
    }
}
