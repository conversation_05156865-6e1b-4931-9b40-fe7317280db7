package com.fangcang.hotel.epsbld.jackson.core.databind;

import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

import static com.fangcang.hotel.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

public class LocalDateTimeJson {

    public static final LocalDateTimeSerializer SERIALIZER = new LocalDateTimeSerializer(DateTimeFormatter
            .ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
            .withZone(ZoneId.systemDefault()));

    public static final LocalDateTimeDeserializer DESERIALIZABLE = new LocalDateTimeDeserializer(DateTimeFormatter
            .ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
            .withZone(ZoneId.systemDefault()));
}
