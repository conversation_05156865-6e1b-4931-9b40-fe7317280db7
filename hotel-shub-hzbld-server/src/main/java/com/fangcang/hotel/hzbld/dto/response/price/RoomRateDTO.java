package com.fangcang.hotel.hzbld.dto.response.price;

import java.util.List;
import java.util.List;
import com.fangcang.hotel.hzbld.dto.response.price.PriceDailyDTO;
import java.util.List;
import com.fangcang.hotel.hzbld.dto.response.price.RoomCountDailyDTO;
import java.util.List;
import com.fangcang.hotel.hzbld.dto.response.price.BreakFastDTO;
import java.util.List;
import com.fangcang.hotel.hzbld.dto.response.price.BookDTO;
import com.fangcang.hotel.hzbld.dto.response.price.CheckInDTO;
import com.fangcang.hotel.hzbld.dto.response.price.CancelDTO;
import com.fangcang.hotel.hzbld.dto.response.price.OtherDTO;
import java.util.List;
import com.fangcang.hotel.hzbld.dto.response.price.ActivityControl;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  查询房价房量响应接口
 *
 * <AUTHOR>
 * @date 2023-12-11 14:22:34
 */
@Data
public class RoomRateDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 房价码ID
     */
    private String rateCode;

    /**
     * 房价码名称
     */
    private String rateCodeName;

    /**
     * 房价码描述
     */
    private String rateCodeDescription;

    /**
     * 入住类型
     */
    private List<String> checkInTypeList;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 房价码类型
     */
    private List<String> tagList;

    /**
     * 每日价格
     */
    private List<PriceDailyDTO> priceDailyList;

    /**
     * 每日房量
     */
    private List<RoomCountDailyDTO> roomCountDailyList;

    /**
     * 会员和早餐
     */
    private List<BreakFastDTO> memberAndBreakFastList;

    /**
     * 会员等级
     */
    private List<String> memberLevels;

    /**
     * 预订政策
     */
    private BookDTO bookInfo;

    /**
     * 入住政策
     */
    private CheckInDTO checkInInfo;

    /**
     * 取消政策
     */
    private CancelDTO cancelInfo;

    /**
     * 其他政策
     */
    private OtherDTO otherInfo;

    /**
     * 单店价支持的卡号列表
     */
    private List<String> rfpCardIdList;

    /**
     * 通过黑白名单控制可以使用活动价的客户
     */
    private ActivityControl activityControlInfo;

}
