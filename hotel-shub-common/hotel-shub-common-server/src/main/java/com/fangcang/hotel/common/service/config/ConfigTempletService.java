package com.fangcang.hotel.common.service.config;

import com.fangcang.hotel.common.controller.admin.config.vo.ConfigTempletAditReqVO;
import com.fangcang.hotel.common.controller.admin.config.vo.ConfigTempletPageReqVO;
import com.fangcang.hotel.common.controller.admin.config.vo.ConfigTempletQueryReqVO;
import com.fangcang.hotel.common.dal.db.config.ConfigTempletDO;
import com.fangcang.hotel.framework.common.domain.PageResult;

import javax.validation.Valid;
import java.util.List;

/**
 * 配置模板 Service 接口
 *
 * <AUTHOR>
 */
public interface ConfigTempletService {

    /**
     * 创建配置模板
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long configTempletAdd(@Valid ConfigTempletAditReqVO reqVO);

    /**
     * 更新配置模板
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long configTempletEdit(@Valid ConfigTempletAditReqVO reqVO);

    /**
     * 删除配置模板
     *
     * @param id 编号
     */
    void configTempletDel(Long id);

    /**
     * 获得配置模板信息
     *
     * @param id 编号
     * @return 配置模板信息
     */
    ConfigTempletDO configTempletDetail(Long id);

    /**
     * 获得配置模板列表
     *
     * @param reqVO 查询条件
     * @return 配置模板列表
     */
    List<ConfigTempletDO> configTempletList(@Valid ConfigTempletQueryReqVO reqVO);

    /**
     * 获得配置模板分页
     *
     * @param reqVO 查询条件
     * @return 配置模板分页
     */
    PageResult<ConfigTempletDO> configTempletPage(@Valid ConfigTempletPageReqVO reqVO);

}
