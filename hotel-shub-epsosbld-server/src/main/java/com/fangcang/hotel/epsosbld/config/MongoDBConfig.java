package com.fangcang.hotel.epsosbld.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDbFactory;

/**
 * @description: 每日售卖信息
 * @author: qiu
 * @create: 2023-11-23 11:09
 */
@Configuration
public class MongoDBConfig {

    @Value("${spring.data.mongodb.uri}")
    private String uri;

    @Value("${spring.data.mongodb.saleDateMongoUrl}")
    private String saleDateMongoUrl;


    @Bean(name = "cityMappingMongoTemplate")
    public MongoTemplate cityMappingMongoTemplate() {
        MongoTemplate mongoTemplate = new MongoTemplate(new SimpleMongoClientDbFactory(uri));
        return mongoTemplate;
    }

    @Bean(name = "shubMongoTemplate")
    @Primary
    public MongoTemplate shubMongoTemplate() {
        MongoTemplate mongoTemplate = new MongoTemplate(new SimpleMongoClientDbFactory(saleDateMongoUrl));
        return mongoTemplate;
    }
}