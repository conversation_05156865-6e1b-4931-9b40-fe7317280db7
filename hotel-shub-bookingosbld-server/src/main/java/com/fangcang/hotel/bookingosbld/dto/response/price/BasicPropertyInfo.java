package com.fangcang.hotel.bookingosbld.dto.response.price;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/18 17:22
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "BasicPropertyInfo")
public class BasicPropertyInfo {

    @XmlAttribute(name = "ChainCode")
    private String chainCode;

    @XmlAttribute(name = "HotelCode")
    private String hotelCode;

    @XmlAttribute(name = "HotelCityCode")
    private String hotelCityCode;

    @XmlAttribute(name = "HotelName")
    private String hotelName;

    @XmlAttribute(name = "HotelCodeContext")
    private String hotelCodeContext;

    @XmlAttribute(name = "ChainName")
    private String chainName;

    @XmlAttribute(name = "AreaID")
    private String areaID;

    @XmlAttribute(name = "HotelSegmentCategoryCode")
    private String hotelSegmentCategoryCode;

    @XmlAttribute(name = "SupplierIntegrationLevel")
    private String supplierIntegrationLevel;

    @XmlElementWrapper(name = "VendorMessages")
    @XmlElement(name = "VendorMessage")
    private List<VendorMessage> vendorMessageList;

    @XmlElement(name = "Address")
    private HotelAddress address;

    @XmlElement(name = "RelativePosition")
    private RelativePosition relativePosition;

    public String getChainCode() {
        return chainCode;
    }

    public void setChainCode(String chainCode) {
        this.chainCode = chainCode;
    }

    public String getHotelCode() {
        return hotelCode;
    }

    public void setHotelCode(String hotelCode) {
        this.hotelCode = hotelCode;
    }

    public String getHotelCityCode() {
        return hotelCityCode;
    }

    public void setHotelCityCode(String hotelCityCode) {
        this.hotelCityCode = hotelCityCode;
    }

    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    public String getHotelCodeContext() {
        return hotelCodeContext;
    }

    public void setHotelCodeContext(String hotelCodeContext) {
        this.hotelCodeContext = hotelCodeContext;
    }

    public String getChainName() {
        return chainName;
    }

    public void setChainName(String chainName) {
        this.chainName = chainName;
    }

    public String getAreaID() {
        return areaID;
    }

    public void setAreaID(String areaID) {
        this.areaID = areaID;
    }

    public String getHotelSegmentCategoryCode() {
        return hotelSegmentCategoryCode;
    }

    public void setHotelSegmentCategoryCode(String hotelSegmentCategoryCode) {
        this.hotelSegmentCategoryCode = hotelSegmentCategoryCode;
    }

    public String getSupplierIntegrationLevel() {
        return supplierIntegrationLevel;
    }

    public void setSupplierIntegrationLevel(String supplierIntegrationLevel) {
        this.supplierIntegrationLevel = supplierIntegrationLevel;
    }

    public List<VendorMessage> getVendorMessageList() {
        return vendorMessageList;
    }

    public void setVendorMessageList(List<VendorMessage> vendorMessageList) {
        this.vendorMessageList = vendorMessageList;
    }

    public HotelAddress getAddress() {
        return address;
    }

    public void setAddress(HotelAddress address) {
        this.address = address;
    }

    public RelativePosition getRelativePosition() {
        return relativePosition;
    }

    public void setRelativePosition(RelativePosition relativePosition) {
        this.relativePosition = relativePosition;
    }
}
