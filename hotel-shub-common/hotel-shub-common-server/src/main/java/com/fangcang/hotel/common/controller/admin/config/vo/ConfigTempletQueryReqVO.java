package com.fangcang.hotel.common.controller.admin.config.vo;

import lombok.*;

import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.fangcang.hotel.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 配置模板 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ConfigTempletQueryReqVO {

    /** 供应类型 */
    private String supplyClass;

    /** 配置项参数 */
    private String templetpara;

    /** 配置项描述 */
    private String description;

    /** 配置类型 [公共,商家] */
    private Integer configType;

    /** 示例 */
    private String example;

    /** 状态 [有效,无效] */
    private Integer status;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

}
