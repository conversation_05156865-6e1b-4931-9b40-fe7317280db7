package com.fangcang.hotel.mtosbld.service.impl;

import com.alibaba.fastjson.JSON;
import com.fangcang.hotel.common.api.common.order.SupplyOrderApi;
import com.fangcang.hotel.core.enums.GlobalErrorCodeEnum;
import com.fangcang.hotel.core.util.StringUtilExtend;
import com.fangcang.hotel.data.api.dto.*;
import com.fangcang.hotel.data.api.dto.order.OrderCheckDetailsResponse;
import com.fangcang.hotel.data.api.dto.order.OrderRoomDetailDTO;
import com.fangcang.hotel.data.api.dto.order.PriceInfoDetail;
import com.fangcang.hotel.data.api.dto.order.QueryCheckDetailRequest;
import com.fangcang.hotel.data.api.dto.order.cancel.CancelSupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.cancel.CancelSupplyOrderResponse;
import com.fangcang.hotel.data.api.dto.order.create.CreateSupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.create.CreateSupplyOrderResponse;
import com.fangcang.hotel.data.api.dto.order.prebooking.PreBookingSupplyRequest;
import com.fangcang.hotel.data.api.dto.order.prebooking.PreBookingSupplyResponse;
import com.fangcang.hotel.data.api.dto.order.query.QuerySupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.query.QuerySupplyOrderResponse;
import com.fangcang.hotel.data.api.enums.*;
import com.fangcang.hotel.data.api.service.SupplyOrderService;
import com.fangcang.hotel.data.base.services.CacheService;
import com.fangcang.hotel.framework.common.domain.ResultX;
import com.fangcang.hotel.framework.common.exception.BizException;
import com.fangcang.hotel.framework.common.util.DateUtilX;
import com.fangcang.hotel.framework.operatelog.core.sls.SlsEnum;
import com.fangcang.hotel.mtosbld.config.MTOSBLDExtendConfig;
import com.fangcang.hotel.mtosbld.dto.request.*;
import com.fangcang.hotel.mtosbld.dto.response.*;
import com.fangcang.hotel.mtosbld.enums.MTBreakfastEnum;
import com.fangcang.hotel.mtosbld.enums.MTOSBLDOrderStatusEnum;
import com.fangcang.hotel.mtosbld.enums.ResponseStatusEnum;
import com.fangcang.hotel.mtosbld.manger.MTOSBLDManager;
import com.fangcang.hotel.mtosbld.util.LogPrintUtil;
import com.fangcang.hotel.mtosbld.util.PricePlanIdUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;


/**
 * 订单服务实现类
 */
//@Slf4j
@Service("mtosbldOrderService")
public class MTOSBLDOrderServiceImpl implements SupplyOrderService {

    @Autowired
    private CacheService cacheService;

    @Autowired
    private MTOSBLDManager mtosbldManager;

    @Autowired
    private LogPrintUtil logPrintUtil;

    /**
     * 试预定
     *
     * @param preBookingSupplyRequest
     * @return
     */
    @Override
    public ResultX<PreBookingSupplyResponse> preBooking(PreBookingSupplyRequest preBookingSupplyRequest) throws Exception {
        PreBookingSupplyResponse preBookingSupplyResponse = new PreBookingSupplyResponse();
        try {
            //获取供应商配置
            MTOSBLDExtendConfig extendConfig = (MTOSBLDExtendConfig) cacheService.getCacheConfigExtend(preBookingSupplyRequest.getMerchantSource(),
                    preBookingSupplyRequest.getMerchantCode(), SupplyClassEnum.MTOSBLD, preBookingSupplyRequest.getSupplyCode(), MTOSBLDExtendConfig.class);

            String supplyRateId = preBookingSupplyRequest.getSupplyRateId();
            String spHotelId = PricePlanIdUtils.getSpHotelId(supplyRateId);
            String spRoomId = PricePlanIdUtils.getSpRoomId(supplyRateId);
            String ratePlanCode = PricePlanIdUtils.getPricePlanId(supplyRateId);
            if (!StringUtilExtend.isValidString(spHotelId)
                    || !StringUtilExtend.isValidString(spRoomId)
                    || !StringUtilExtend.isValidString(ratePlanCode)) {
                logPrintUtil.simplePrintErrorLog("preBooking", "preBooking:{}--supplyRateId is error", JSON.toJSONString(preBookingSupplyRequest));
                return ResultX.error(GlobalErrorCodeEnum.PRODUCT_NOT_FOUND);
            }

            Date checkInDate = preBookingSupplyRequest.getCheckInDate();
            Date checkOutDate = preBookingSupplyRequest.getCheckOutDate();
            String currency = extendConfig.getCurrency();

            //组装试预订返回参数
            preBookingSupplyResponse.setCanBook(CanBookEnum.CAN_BOOK.value);
            preBookingSupplyResponse.setBaseCurrency(CurrencyEnum.getKeyByDesc(currency));
            preBookingSupplyResponse.setSupplyRateId(supplyRateId);
            preBookingSupplyResponse.setSupplyCode(extendConfig.getSupplyCode());
            preBookingSupplyResponse.setCheckInDate(DateUtilX.dateToString(checkInDate));
            preBookingSupplyResponse.setCheckOutDate(DateUtilX.dateToString(checkOutDate));

            preBookingSupplyResponse.setRoomId(preBookingSupplyRequest.getRoomId());
            preBookingSupplyResponse.setHotelId(preBookingSupplyRequest.getHotelId());
            preBookingSupplyResponse.setCanImmediate(1);

            Occupancy occupancy = new Occupancy();
            HotelSearchRequest hotelSearchRequest = getHotelSearchRequest(preBookingSupplyRequest, spHotelId, occupancy);
            HotelSearchResponse hotelSearchResponse = mtosbldManager.getHotelSearch(hotelSearchRequest, extendConfig);
            if (hotelSearchResponse == null || CollectionUtils.isEmpty(hotelSearchResponse.getHotels())) {
                logPrintUtil.simplePrintErrorLog("preBooking", "preBooking:{}--hotelSearch is null", supplyRateId);
                return ResultX.error(GlobalErrorCodeEnum.PRODUCT_NOT_FOUND);
            }

            RatesDTO rate = getRate(hotelSearchResponse, spHotelId, spRoomId, ratePlanCode);
            if (rate == null) {
                logPrintUtil.simplePrintErrorLog("preBooking", "preBooking:{}--rate is null", JSON.toJSONString(preBookingSupplyRequest));
                return ResultX.error(GlobalErrorCodeEnum.PRODUCT_NOT_FOUND);
            }

            //试预定订单
            CheckRequest checkRequest = new CheckRequest();
            checkRequest.setHotel(spHotelId);
            checkRequest.setRatePlanCode(ratePlanCode);
            checkRequest.setCurrency(currency);
            checkRequest.setTotalPrice(rate.getPrice());
            checkRequest.setCheckIn(DateUtilX.dateToString(checkInDate));
            checkRequest.setCheckOut(DateUtilX.dateToString(checkOutDate));
            checkRequest.setOccupancy(occupancy);
            checkRequest.setCountryCodes(new ArrayList<String>() {{
                add(preBookingSupplyRequest.getNationality());
            }});
            preBookingSupplyResponse.setRequestContent(JSON.toJSONString(checkRequest));
            CheckResponse checkResponse = mtosbldManager.check(checkRequest, extendConfig);
            preBookingSupplyResponse.setResponseContent(JSON.toJSONString(checkResponse));

            if (checkResponse == null || checkResponse.getHotel() == null
                    || !checkResponse.getHotel().getCode().equals(spHotelId)
                    || CollectionUtils.isEmpty(checkResponse.getHotel().getRooms())) {
                logPrintUtil.simplePrintErrorLog("preBooking", "preBooking:{}--response is error", JSON.toJSONString(preBookingSupplyRequest));
                String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getMsg(), "试预定");
                return ResultX.failure(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, msg, preBookingSupplyResponse);
            }

            RatesDTO checkRate = getCheckRate(checkResponse, spRoomId, ratePlanCode);
            if (checkRate == null) {
                logPrintUtil.simplePrintErrorLog("preBooking", "preBooking:{}-->response rate is null", supplyRateId);
                String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getMsg(), "试预定");
                return ResultX.failure(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, msg, preBookingSupplyResponse);
            }

            List<OrderRoomDetailDTO> orderRoomDetails = new ArrayList<>();
            try {

                List<PriceInfoDetail> priceInfoDetails = new ArrayList<>();

                List<RoomGuestNumberDTO> roomGuestNumbers = preBookingSupplyRequest.getRoomGuestNumbers();

                // 当前时间
                Date rateCheckInDate = preBookingSupplyRequest.getCheckInDate();
                Date rateCheckOutDate = DateUtilX.getDate(preBookingSupplyRequest.getCheckOutDate(), -1);
                List<String> dateStrList = DateUtilX.getDateStrList(rateCheckInDate, rateCheckOutDate);
                int roomSize = roomGuestNumbers.size();
                int datesSize = dateStrList.size();
                BigDecimal basePrice = rate.getPrice().divide(BigDecimal.valueOf(roomSize).multiply(BigDecimal.valueOf(datesSize)), 2, RoundingMode.DOWN);
                BigDecimal remainder = rate.getPrice().subtract(basePrice.multiply(BigDecimal.valueOf(roomSize).multiply(BigDecimal.valueOf(datesSize))));
                for (String dateStr : dateStrList) {
                    TaxDetailsDTO dailyTax = new TaxDetailsDTO();
                    dailyTax.setRoomPrice(basePrice);
                    dailyTax.setOtherTax(BigDecimal.ZERO);

                    PriceInfoDetail priceInfoDetail = new PriceInfoDetail();
                    priceInfoDetail.setSaleDate(DateUtilX.stringToDate(dateStr));
                    priceInfoDetail.setBasePrice(basePrice);
                    priceInfoDetail.setQuotaNum(rate.getAllotment());
                    //非待查
                    priceInfoDetail.setNeedQuery(0);
                    //有房
                    priceInfoDetail.setRoomStatus(RoomStateEnum.HAVA_ROOM.key);
                    priceInfoDetail.setCanbook(CanBookEnum.CAN_BOOK.value);

                    priceInfoDetail.setTaxDetail(dailyTax);
                    priceInfoDetails.add(priceInfoDetail);
                }

                OrderRoomDetailDTO orderRoomDetailDTO = new OrderRoomDetailDTO();
                orderRoomDetailDTO.setPriceInfoDetails(priceInfoDetails);

                //设置房间号
                Map<Integer, OrderRoomDetailDTO> map = new HashMap<>();
                int count = preBookingSupplyRequest.getRoomGuestNumbers().size();
                Integer lastRoomIndex = 0;
                for (int i = 0; i < count; i++) {
                    OrderRoomDetailDTO roomDetailDTO = new OrderRoomDetailDTO();
                    BeanUtils.copyProperties(orderRoomDetailDTO, roomDetailDTO);
                    RoomGuestNumberDTO roomGuestNumberDTO = preBookingSupplyRequest.getRoomGuestNumbers().get(i);
                    Integer roomIndex = roomGuestNumberDTO.getRoomIndex();
                    roomDetailDTO.setRoomIndex(roomIndex);
                    roomDetailDTO.setAdultNum(roomGuestNumberDTO.getAdultNum());

                    //早餐数据//根据房间入住成人数转换早餐
                    Integer breakfastNum;
                    String boardCode = rate.getBoardCode();
                    if (StringUtilExtend.isValidString(boardCode) && !boardCode.equals(MTBreakfastEnum._RO.key)) {
                        if (roomGuestNumberDTO.getAdultNum() == 1) {
                            breakfastNum = 1;
                        } else if (roomGuestNumberDTO.getAdultNum() == 2) {
                            breakfastNum = 2;
                        } else if (roomGuestNumberDTO.getAdultNum() > 2) {
                            breakfastNum = -1;
                        } else {
                            breakfastNum = 0;
                        }
                    } else {
                        breakfastNum = 0;
                    }
                    roomDetailDTO.getPriceInfoDetails().forEach(item -> {
                        item.setBreakfastNum(breakfastNum);
                    });

                    if (roomIndex > lastRoomIndex) {
                        lastRoomIndex = roomIndex;
                    }
                    map.put(roomIndex, roomDetailDTO);
                }

                //最后一间房的最后一天的房价要加上余额
//                Integer lastDateIndex = null;
                if (!map.isEmpty()) {
                    OrderRoomDetailDTO lastOrderRoomDetailDTO = map.get(lastRoomIndex);
                    List<PriceInfoDetail> lastPriceInfoDetails = lastOrderRoomDetailDTO.getPriceInfoDetails();
                    if (!CollectionUtils.isEmpty(lastPriceInfoDetails)) {
                        PriceInfoDetail priceInfoDetailTmp = new PriceInfoDetail();
                        TaxDetailsDTO taxDetailTmp = new TaxDetailsDTO();
                        for (int i = 0; i < lastPriceInfoDetails.size(); i++) {
                            PriceInfoDetail lastPriceInfoDetail = lastPriceInfoDetails.get(i);
                            if (!DateUtilX.dateToString(lastPriceInfoDetail.getSaleDate())
                                    .equals(DateUtilX.dateToString(rateCheckOutDate))) {
                                continue;
                            }
                            BeanUtils.copyProperties(lastPriceInfoDetail, priceInfoDetailTmp);
                            BeanUtils.copyProperties(lastPriceInfoDetail.getTaxDetail(), taxDetailTmp);
                            priceInfoDetailTmp.setBasePrice(basePrice.add(remainder));
                            taxDetailTmp.setRoomPrice(basePrice.add(remainder));
                            priceInfoDetailTmp.setTaxDetail(taxDetailTmp);
                            lastPriceInfoDetails.set(i, priceInfoDetailTmp);
                            break;
                        }
                    }
                }
                orderRoomDetails.addAll(map.values());
            } catch (Exception e) {
                logPrintUtil.simplePrintErrorLog("preBooking", "试预定转换产品价格数据异常:{}", e.getMessage());
                logPrintUtil.saveErrorLog(e);
            }
            preBookingSupplyResponse.setOrderRoomDetails(orderRoomDetails);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            logPrintUtil.simplePrintErrorLog("preBooking", "萌兔全球不落地试预定失败,系统异常--request:{} error:{}", JSON.toJSONString(preBookingSupplyRequest), e.getMessage());
            logPrintUtil.saveErrorLog(e);
            throw new Exception("萌兔全球不落地试预定失败,系统异常--request" + JSON.toJSONString(preBookingSupplyRequest), e);
        }
        return ResultX.success(preBookingSupplyResponse);
    }

    /**
     * 创建订单服务
     *
     * @param createSupplyOrderRequest
     * @return
     */
    @Override
    public ResultX<CreateSupplyOrderResponse> createSupplierOrder(CreateSupplyOrderRequest createSupplyOrderRequest) throws Exception {
        CreateSupplyOrderResponse createSupplyOrderResponse = new CreateSupplyOrderResponse();
        try {
            //获取供应商配置
            MTOSBLDExtendConfig extendConfig = (MTOSBLDExtendConfig) cacheService.getCacheConfigExtend(createSupplyOrderRequest.getMerchantSource(),
                    createSupplyOrderRequest.getMerchantCode(), SupplyClassEnum.MTOSBLD, createSupplyOrderRequest.getSupplyCode(), MTOSBLDExtendConfig.class);

            //校验币种
            if (!(StringUtilExtend.isValidString(extendConfig.getCurrency())
                    && createSupplyOrderRequest.getBaseCurrency() != null
                    && extendConfig.getCurrency().equals(CurrencyEnum.getDescByKey(createSupplyOrderRequest.getBaseCurrency())))) {
                logPrintUtil.simplePrintErrorLog("createSupplierOrder", "createSupplierOrder--下单前校验币种不通过:{}", JSON.toJSONString(createSupplyOrderRequest));
                return ResultX.error(GlobalErrorCodeEnum.CURRENCY_NOT_EQUAL);
            }

            //验证订单是否存在
            QueryOrderRequest queryOrderRequest = new QueryOrderRequest();
            queryOrderRequest.setBookingReferenceId(createSupplyOrderRequest.getMerchantOrderCode());
            QueryOrderResponse queryOrderResponse = mtosbldManager.queryOrder(queryOrderRequest, extendConfig);
            if (queryOrderResponse != null && StringUtilExtend.isValidString(queryOrderResponse.getReferenceId())) {
                //订单已存在
                logPrintUtil.simplePrintErrorLog("createSupplierOrder", "createSupplierOrder--订单已存在:{}", JSON.toJSONString(createSupplyOrderRequest));
                throw new BizException(GlobalErrorCodeEnum.SUPPLY_ORDER_CODE_EXISTED);
            }

            //当前下单的channel可以固定写死,所以暂时不走下单前试预定的逻辑
//            String supplyRateId = createSupplyOrderRequest.getSupplyRateId();
//            String spHotelId = PricePlanIdUtils.getSpHotelId(supplyRateId);
//            String spRoomId = PricePlanIdUtils.getSpRoomId(supplyRateId);
//            String ratePlanCode = PricePlanIdUtils.getPricePlanId(supplyRateId);
//
//            Date checkInDate = createSupplyOrderRequest.getCheckInDate();
//            Date checkOutDate = createSupplyOrderRequest.getCheckOutDate();

//            Integer adults = 0;
//            List<String> childrenAges = new ArrayList<>();
//            for (RoomGuestNumberDTO roomGuestNumber : createSupplyOrderRequest.getRoomGuestNumbers()) {
//                adults += roomGuestNumber.getAdultNum();
//                if (!CollectionUtils.isEmpty(roomGuestNumber.getChildrenInfos())) {
//                    for (ChildrenInfoDTO childrenInfo : roomGuestNumber.getChildrenInfos()) {
//                        childrenAges.add(String.valueOf(childrenInfo.getChildrenAge()));
//                    }
//                }
//            }

//            CheckRequest checkRequest = new CheckRequest();
//            checkRequest.setHotel(spHotelId);
//            checkRequest.setRatePlanCode(ratePlanCode);
//            checkRequest.setCurrency(extendConfig.getCurrency());
//            checkRequest.setTotalPrice(createSupplyOrderRequest.getTotalAmount());
//            checkRequest.setCheckIn(DateUtilX.dateToString(checkInDate));
//            checkRequest.setCheckOut(DateUtilX.dateToString(checkOutDate));
//            Occupancy occupancy = new Occupancy();
//            occupancy.setRooms(createSupplyOrderRequest.getRoomNum());
//            occupancy.setAdults(adults);
//            occupancy.setChildrenAges(childrenAges);
//            checkRequest.setOccupancy(occupancy);
//            checkRequest.setCountryCodes(new ArrayList<String>() {{
//                add(createSupplyOrderRequest.getNationality());
//            }});
//            CheckResponse checkResponse = mtosbldManager.check(checkRequest, extendConfig);
//
//            String resource = "hotel-shub-mtosbld-server";
//            Map<String, String> map = new HashMap<>();
//            map.put(SlsEnum.LEVEL.getType(), "info");
//            map.put(SlsEnum.MESSAGE.getType(), "试预定");
//            map.put(SlsEnum.NAME.getType(), resource);
//            map.put("request", JSON.toJSONString(checkRequest));
//            map.put("response", JSON.toJSONString(checkResponse));
//            String topic = "创建订单前试预定";
//            logPrintUtil.saveLog(map, topic, resource);
//
//            RatesDTO createOrderRate = getCreateOrderRate(checkResponse, spHotelId, spRoomId, ratePlanCode);
//            if (createOrderRate == null) {
//                logPrintUtil.simplePrintErrorLog("createSupplierOrder", "createSupplierOrder--下单前试预定返回数据为空:{}", JSON.toJSONString(createSupplyOrderRequest));
//                return ResultX.error(GlobalErrorCodeEnum.SUPPLY_PRE_BOOKING_FAIL);
//            }

            //开始预定
            BookingRequest bookingRequest = getBookingRequest(createSupplyOrderRequest, extendConfig);
            createSupplyOrderResponse.setRequestContent(JSON.toJSONString(bookingRequest));
            BookingResponse bookingResponse = mtosbldManager.booking(bookingRequest, extendConfig);
            createSupplyOrderResponse.setResponseContent(JSON.toJSONString(bookingResponse));

            String responseStatus = bookingResponse.getResponseStatus();
            String referenceId = bookingResponse.getReferenceId();

            if (StringUtilExtend.isValidString(responseStatus)
                    && responseStatus.equalsIgnoreCase(ResponseStatusEnum.SUCCESS.toString())
                    && StringUtilExtend.isValidString(referenceId)) {
                createSupplyOrderResponse.setMerchantOrderCode(createSupplyOrderRequest.getMerchantOrderCode());
                createSupplyOrderResponse.setSupplyOrderCode(referenceId);
                createSupplyOrderResponse.setSupplyOrderStatus(bookingResponse.getResponseStatus());
                createSupplyOrderResponse.setOrderStatus(OrderStatusEnum.CONFIRM.getResult());
            } else if (StringUtilExtend.isValidString(responseStatus)
                    && responseStatus.equalsIgnoreCase(ResponseStatusEnum.BOOKING_PENDING.toString())
                    && StringUtilExtend.isValidString(referenceId)) {
                createSupplyOrderResponse.setMerchantOrderCode(createSupplyOrderRequest.getMerchantOrderCode());
                createSupplyOrderResponse.setSupplyOrderCode(referenceId);
                createSupplyOrderResponse.setSupplyOrderStatus(bookingResponse.getResponseStatus());
                createSupplyOrderResponse.setOrderStatus(OrderStatusEnum.NEED_CONFIRM.getResult());
            } else {
                logPrintUtil.simplePrintErrorLog("createSupplierOrder", "createSupplierOrder--创建订单失败,返回格式不正确--createSupplyOrderRequest{},bookingResponse:{}", JSON.toJSONString(createSupplyOrderRequest), JSON.toJSONString(bookingResponse));
                return ResultX.failure(GlobalErrorCodeEnum.SUPPLY_RETURN_RESULT_FAIL, createSupplyOrderResponse);
            }
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            logPrintUtil.simplePrintErrorLog("createSupplierOrder", "萌兔不落地创建订单失败,系统异常--request:{},error:{}", JSON.toJSONString(createSupplyOrderRequest), e.getMessage());
            logPrintUtil.saveErrorLog(e);
            throw new Exception("萌兔不落地创建订单失败,系统异常--request:" + JSON.toJSONString(createSupplyOrderRequest), e);
        }
        return ResultX.success(createSupplyOrderResponse);
    }


    /**
     * 查询供应商订单状态服务 并回写saas订单装填
     *
     * @param querySupplyOrderRequest
     * @return
     */
    @Override
    public ResultX<QuerySupplyOrderResponse> querySupplierOrderStatus(QuerySupplyOrderRequest querySupplyOrderRequest) throws Exception {
        QuerySupplyOrderResponse querySupplyOrderResponse = new QuerySupplyOrderResponse();
        try {
            MTOSBLDExtendConfig extendConfig = (MTOSBLDExtendConfig) cacheService.getCacheConfigExtend(querySupplyOrderRequest.getMerchantSource(),
                    querySupplyOrderRequest.getMerchantCode(), SupplyClassEnum.MTOSBLD, querySupplyOrderRequest.getSupplyCode(), MTOSBLDExtendConfig.class);


            QueryOrderRequest queryOrderRequest = new QueryOrderRequest();
            queryOrderRequest.setBookingReferenceId(querySupplyOrderRequest.getMerchantOrderCode());
            querySupplyOrderResponse.setRequestContent(JSON.toJSONString(queryOrderRequest));
            QueryOrderResponse queryOrderResponse = mtosbldManager.queryOrder(queryOrderRequest, extendConfig);
            querySupplyOrderResponse.setResponseContent(JSON.toJSONString(queryOrderResponse));

            if (queryOrderResponse == null
                    || !StringUtilExtend.isValidString(queryOrderResponse.getResponseStatus())
                    || !queryOrderResponse.getResponseStatus().equalsIgnoreCase(ResponseStatusEnum.SUCCESS.name())
                    || queryOrderResponse.getOrderStatus() == null) {
                logPrintUtil.simplePrintErrorLog("querySupplierOrderStatus", "querySupplierOrderStatus-查询订单详情,返回数据格式有误-querySupplyOrderRequest{},queryOrderResponse:{}", JSON.toJSONString(querySupplyOrderRequest), JSON.toJSONString(queryOrderResponse));
                return ResultX.error(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, "查询订单详情", JSON.toJSONString(queryOrderResponse));
            }

            Integer orderStatus = queryOrderResponse.getOrderStatus();
            querySupplyOrderResponse.setSupplyOrderCode(queryOrderResponse.getReferenceId());
            querySupplyOrderResponse.setSupplyOrderStatus(orderStatus.toString());
            querySupplyOrderResponse.setSupplyConfirmNo(queryOrderResponse.getHotelConfirmNO());

            if (orderStatus.equals(MTOSBLDOrderStatusEnum._0.getKey())
                    || orderStatus.equals(MTOSBLDOrderStatusEnum._10.getKey())) {
                querySupplyOrderResponse.setOrderStatus(OrderStatusEnum.NEED_CONFIRM.getResult());
            } else if (orderStatus.equals(MTOSBLDOrderStatusEnum._1.getKey())
                    || orderStatus.equals(MTOSBLDOrderStatusEnum._202.getKey())) {
                querySupplyOrderResponse.setOrderStatus(OrderStatusEnum.CONFIRM.getResult());
            } else if (orderStatus.equals(MTOSBLDOrderStatusEnum._201.getKey())) {
                querySupplyOrderResponse.setOrderStatus(OrderStatusEnum.CANCEL.getResult());
            } else if (orderStatus.equals(MTOSBLDOrderStatusEnum._20.getKey())) {
                querySupplyOrderResponse.setOrderStatus(OrderStatusEnum.CANCEL_PENDING.getResult());
            } else {
                logPrintUtil.simplePrintErrorLog("querySupplierOrderStatus", "querySupplierOrderStatus-订单状态有误-querySupplyOrderRequest{},queryOrderResponse:{}", JSON.toJSONString(querySupplyOrderRequest), JSON.toJSONString(queryOrderResponse));
                return ResultX.failure(GlobalErrorCodeEnum.ORDER_STATUS_ERROR, querySupplyOrderResponse);
            }

        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            logPrintUtil.simplePrintErrorLog("querySupplierOrderStatus", "萌兔不落地查询订单失败,系统异常--request:{},error:{}", JSON.toJSONString(querySupplyOrderRequest), e.getMessage());
            logPrintUtil.saveErrorLog(e);
            throw new Exception("萌兔不落地查询订单失败,系统异常--request:" + JSON.toJSONString(querySupplyOrderRequest), e);
        }
        return ResultX.success(querySupplyOrderResponse);
    }


    /**
     * 取消订单服务
     *
     * @param cancelSupplyOrderRequest
     * @return
     */
    @Override
    public ResultX<CancelSupplyOrderResponse> cancelSupplierOrder(CancelSupplyOrderRequest cancelSupplyOrderRequest) throws Exception {
        CancelSupplyOrderResponse cancelSupplyOrderResponse = new CancelSupplyOrderResponse();
        try {
            MTOSBLDExtendConfig extendConfig = (MTOSBLDExtendConfig) cacheService.getCacheConfigExtend(cancelSupplyOrderRequest.getMerchantSource(),
                    cancelSupplyOrderRequest.getMerchantCode(), SupplyClassEnum.MTOSBLD, cancelSupplyOrderRequest.getSupplyCode(), MTOSBLDExtendConfig.class);

            CancelRequest cancelRequest = new CancelRequest();
            cancelRequest.setBookingReferenceId(cancelSupplyOrderRequest.getMerchantOrderCode());
            cancelSupplyOrderResponse.setRequestContent(JSON.toJSONString(cancelRequest));
            CancelResponse cancelResponse = mtosbldManager.cancel(cancelRequest, extendConfig);
            cancelSupplyOrderResponse.setResponseContent(JSON.toJSONString(cancelResponse));

            if (cancelResponse == null
                    || !StringUtilExtend.isValidString(cancelResponse.getResponseStatus())) {
                logPrintUtil.simplePrintErrorLog("cancelSupplierOrder", "cancelSupplierOrder-取消订单,返回数据格式有误-cancelSupplyOrderRequest{},cancelResponse:{}", JSON.toJSONString(cancelSupplyOrderRequest), JSON.toJSONString(cancelResponse));
                return ResultX.error(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, "取消订单", JSON.toJSONString(cancelResponse));
            }

            if (!cancelResponse.getResponseStatus().equalsIgnoreCase(ResponseStatusEnum.SUCCESS.name())) {
                logPrintUtil.simplePrintErrorLog("cancelSupplierOrder", "cancelSupplierOrder-取消订单失败-cancelSupplyOrderRequest:{},cancelResponse:{}", JSON.toJSONString(cancelSupplyOrderRequest), JSON.toJSONString(cancelResponse));
                return ResultX.failure(GlobalErrorCodeEnum.SUPPLY_CANCEL_ORDER_FIAIL, cancelSupplyOrderResponse);
            }

            BigDecimal cancellationFees = cancelResponse.getCancellationFees();
            cancelSupplyOrderResponse.setCancelOrderStatus(true);
            cancelSupplyOrderResponse.setSupplyOrderStatus(MTOSBLDOrderStatusEnum._201.getKey().toString());
            cancelSupplyOrderResponse.setOrderStatus(OrderStatusEnum.CANCEL.getResult());
            cancelSupplyOrderResponse.setSupplyOrderCode(cancelSupplyOrderResponse.getSupplyOrderCode());
            cancelSupplyOrderResponse.setRefundCurrency(CurrencyEnum.getKeyByDesc(extendConfig.getCurrency()));
            cancelSupplyOrderResponse.setRefundPrice(cancellationFees);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            logPrintUtil.saveErrorLog(e);
            logPrintUtil.simplePrintErrorLog("cancelSupplierOrder", "萌兔不落地取消订单失败,系统异常--request:{},error:{}", JSON.toJSONString(cancelSupplyOrderRequest), e.getMessage());
            throw new Exception("萌兔不落地取消订单失败,系统异常--request:" + JSON.toJSONString(cancelSupplyOrderRequest), e);
        }
        return ResultX.success(cancelSupplyOrderResponse);
    }

    /**
     * 订单入住详情
     *
     * @param queryCheckDetailRequest 查询入住明细请求
     * @return 入住明细
     * <AUTHOR>
     * @version
     */
    @Override
    public ResultX<OrderCheckDetailsResponse> querySupplyOrderCheckDetail(QueryCheckDetailRequest queryCheckDetailRequest) throws Exception {
        try {

        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            logPrintUtil.saveErrorLog(e);
            logPrintUtil.simplePrintErrorLog("querySupplyOrderCheckDetail", "萌兔不落地查询供货单入住明细,系统异常--request:{},error:{}", JSON.toJSONString(queryCheckDetailRequest), e.getMessage());
            throw new Exception("萌兔不落地查询供货单入住明细,系统异常--request:" + JSON.toJSONString(queryCheckDetailRequest), e);
        }

        return null;
    }

    private BookingRequest getBookingRequest(CreateSupplyOrderRequest createSupplyOrderRequest,
                                             MTOSBLDExtendConfig extendConfig) {
        String supplyRateId = createSupplyOrderRequest.getSupplyRateId();
        String spHotelId = PricePlanIdUtils.getSpHotelId(supplyRateId);
        String spRoomId = PricePlanIdUtils.getSpRoomId(supplyRateId);
        String ratePlanCode = PricePlanIdUtils.getPricePlanId(supplyRateId);

        Date checkInDate = createSupplyOrderRequest.getCheckInDate();
        Date checkOutDate = createSupplyOrderRequest.getCheckOutDate();

        String breakfastNum = MTBreakfastEnum._RO.key;
        for (PriceInfoDetail priceInfoDetail : createSupplyOrderRequest.getPriceInfoDetails()) {
            if (priceInfoDetail.getBreakfastNum() != null
                    && priceInfoDetail.getBreakfastNum() > 0) {
                breakfastNum = MTBreakfastEnum._BB.key;
                break;
            }
        }

        String firstName = null;
        String lastName = null;
        String email = null;
        if (extendConfig.getIsUseFixedName() != null
                && extendConfig.getIsUseFixedName() == 1) {
            firstName = extendConfig.getFixedFirstName();
            lastName = extendConfig.getFixedLastName();
        } else {
            if (!CollectionUtils.isEmpty(createSupplyOrderRequest.getGuests())) {
                firstName = createSupplyOrderRequest.getGuests().get(0).getFirstName();
                lastName = createSupplyOrderRequest.getGuests().get(0).getLastName();
            }
        }
        if (extendConfig.getIsUseFixedEmail() != null
                && extendConfig.getIsUseFixedEmail() == 1) {
            email = extendConfig.getFixedEmail();
        } else {
            email = createSupplyOrderRequest.getLinkEmail();
        }

        Integer adults = 0;
        List<String> childrenAges = new ArrayList<>();
        for (RoomGuestNumberDTO roomGuestNumber : createSupplyOrderRequest.getRoomGuestNumbers()) {
            adults += roomGuestNumber.getAdultNum();
            if (!CollectionUtils.isEmpty(roomGuestNumber.getChildrenInfos())) {
                for (ChildrenInfoDTO childrenInfo : roomGuestNumber.getChildrenInfos()) {
                    childrenAges.add(String.valueOf(childrenInfo.getChildrenAge()));
                }
            }
        }

        createSupplyOrderRequest.getGuests();
        BookingRequest bookingRequest = new BookingRequest();
        bookingRequest.setBookingReferenceId(createSupplyOrderRequest.getMerchantOrderCode());
        bookingRequest.setCheckIn(DateUtilX.dateToString(checkInDate));
        bookingRequest.setCheckOut(DateUtilX.dateToString(checkOutDate));
        bookingRequest.setHotel(spHotelId);
        bookingRequest.setRoomCode(spRoomId);
        bookingRequest.setBoardCode(breakfastNum);
        bookingRequest.setRatePlanCode(ratePlanCode);
        bookingRequest.setChannel(1);
        bookingRequest.setCurrency(extendConfig.getCurrency());
        bookingRequest.setQuantity(createSupplyOrderRequest.getRoomNum());
        bookingRequest.setTotalPrice(createSupplyOrderRequest.getTotalAmount());
        Guest guest = new Guest();
        guest.setFirstName(firstName);
        guest.setLastName(lastName);
        guest.setEmail(email);
        bookingRequest.setGuest(guest);
        bookingRequest.setSpecialRequests(createSupplyOrderRequest.getRemark());
        GuestCount guestCount = new GuestCount();
        guestCount.setAdults(adults);
        guestCount.setChildrenAges(childrenAges);
        bookingRequest.setGuestCount(guestCount);
        return bookingRequest;
    }

    private RatesDTO getCreateOrderRate(CheckResponse checkResponse, String spHotelId, String spRoomId, String ratePlanCode) {
        if (checkResponse == null
                || checkResponse.getHotel() == null
                || !StringUtilExtend.isValidString(checkResponse.getHotel().getCode())
                || !checkResponse.getHotel().getCode().equals(spHotelId)
                || CollectionUtils.isEmpty(checkResponse.getHotel().getRooms())) {
            return null;
        }

        for (RoomsDTO room : checkResponse.getHotel().getRooms()) {
            if (!StringUtilExtend.isValidString(room.getCode())
                    || !room.getCode().equals(spRoomId)
                    || CollectionUtils.isEmpty(room.getRates())) {
                continue;
            }
            for (RatesDTO rate : room.getRates()) {
                if (rate.getRatePlanCode().equals(ratePlanCode)) {
                    return rate;
                }
            }
        }
        return null;
    }

    private HotelSearchRequest getHotelSearchRequest(PreBookingSupplyRequest preBookingSupplyRequest, String spHotelId, Occupancy occupancy) {

        List<String> spHotelIds = Arrays.asList(spHotelId);
        Integer adults = 0;
        List<String> childrenAges = new ArrayList<>();
        for (RoomGuestNumberDTO roomGuestNumber : preBookingSupplyRequest.getRoomGuestNumbers()) {
            adults += roomGuestNumber.getAdultNum();
            if (!CollectionUtils.isEmpty(roomGuestNumber.getChildrenInfos())) {
                for (ChildrenInfoDTO childrenInfo : roomGuestNumber.getChildrenInfos()) {
                    childrenAges.add(String.valueOf(childrenInfo.getChildrenAge()));
                }
            }
        }
        String checkInDateStr = DateUtilX.dateToString(preBookingSupplyRequest.getCheckInDate());
        String checkOutDateStr = DateUtilX.dateToString(preBookingSupplyRequest.getCheckOutDate());
        //调用查价酒店价格接口,获取总价
        HotelSearchRequest hotelSearchRequest = new HotelSearchRequest();
        hotelSearchRequest.setHotels(spHotelIds);
        hotelSearchRequest.setCheckIn(checkInDateStr);
        hotelSearchRequest.setCheckOut(checkOutDateStr);
        occupancy.setRooms(preBookingSupplyRequest.getRoomNum());
        occupancy.setAdults(adults);
        occupancy.setChildrenAges(childrenAges);
        hotelSearchRequest.setOccupancy(occupancy);
        hotelSearchRequest.setCountryCodes(new ArrayList<String>() {{
            add(preBookingSupplyRequest.getNationality());
        }});

        return hotelSearchRequest;

    }

    /**
     * 得到价格计划
     *
     * @param hotelSearchResponse
     * @param spHotelId
     * @param spRoomId
     * @param ratePlanCode
     * @return
     */
    private RatesDTO getRate(HotelSearchResponse hotelSearchResponse, String spHotelId, String spRoomId, String ratePlanCode) {
        for (HotelSearchHotelsDTO hotel : hotelSearchResponse.getHotels()) {
            if (!StringUtilExtend.isValidString(hotel.getCode())
                    || !hotel.getCode().equals(spHotelId)
                    || CollectionUtils.isEmpty(hotel.getRooms())) {
                continue;
            }
            for (RoomsDTO room : hotel.getRooms()) {
                if (!StringUtilExtend.isValidString(room.getCode())
                        || !room.getCode().equals(spRoomId)
                        || CollectionUtils.isEmpty(room.getRates())) {
                    continue;
                }
                for (RatesDTO rate : room.getRates()) {
                    if (StringUtilExtend.isValidString(rate.getRatePlanCode())
                            && rate.getRatePlanCode().equals(ratePlanCode)) {
                        return rate;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 试预定的时候,获取对应价格计划的信息
     *
     * @param checkResponse
     * @param spRoomId
     * @param ratePlanCode
     * @return
     */
    private RatesDTO getCheckRate(CheckResponse checkResponse, String spRoomId, String ratePlanCode) {
        for (RoomsDTO room : checkResponse.getHotel().getRooms()) {
            if (!StringUtilExtend.isValidString(room.getCode())
                    || !room.getCode().equals(spRoomId)
                    || CollectionUtils.isEmpty(room.getRates())) {
                continue;
            }
            for (RatesDTO roomRate : room.getRates()) {
                if (StringUtilExtend.isValidString(roomRate.getRatePlanCode())
                        && roomRate.getRatePlanCode().equals(ratePlanCode)) {
                    return roomRate;
                }
            }

        }
        return null;
    }

}
