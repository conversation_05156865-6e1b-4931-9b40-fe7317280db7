package com.fangcang.hotel.common.utils;

import lombok.Data;

import java.util.Collection;

@Data
public class Jwt {

    private static final long serialVersionUID = 1;
    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 运营商编码
     */
    private String companyCode;
    /**
     * 运营商域名
     */
    private String companyDomain;
    /**
     * 全名 用户账号+用户名
     */
    private String fullUserName;
    /**
     * 角色名称s
     */
    private String roleNames;
    /**
     * 重置密码
     */
    private Integer restPwd;
    /**
     * 是否管理员 0-否 1-是
     */
    private Integer isSuperAdmin;
    /**
     * 是否有教程编辑权限 0-否 1-是
     */
    private Integer isNotePermission;
    /**
     * 权限列表
     */
    private Collection<String> roles;

    /**
     * 用户类型 1商户登录 2管理员 3普通员工
     */
    private Integer userType;

}
