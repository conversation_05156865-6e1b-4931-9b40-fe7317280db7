package com.fangcang.hotel.data.api.enums;

/**
 * @program: supply-hub
 * @ClassName HotelMappingCacheEnum
 * @description:
 * @author: 湫
 * @create: 2024/12/07/ 9:25
 * @Version 1.0
 **/

import cn.hutool.core.util.StrUtil;

/**
 * 需要供应商房型映射缓存的supplyClass
 */

public enum RoomMappingCacheEnum {

    ;

    private String supplierClass;

    public String getSupplierClass() {
        return supplierClass;
    }

    private RoomMappingCacheEnum(String supplierClass) {
        this.supplierClass = supplierClass;
    }

    public static RoomMappingCacheEnum getSupplierClass(String supplyClass) {
        for (RoomMappingCacheEnum roomMappingCacheEnum : RoomMappingCacheEnum.values()) {
            if (StrUtil.isNotBlank(supplyClass) && roomMappingCacheEnum.supplierClass.equals(supplyClass)) {
                return roomMappingCacheEnum;
            }
        }
        return null;
    }
}
