package com.fangcang.hotel.amadeusosbld.dto.response.price;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/18 18:52
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Commission")
public class Commission {

    @XmlAttribute(name = "StatusType")
    private String statusType;

    @XmlAttribute(name = "Percent")
    private String percent;

    public String getStatusType() {
        return statusType;
    }

    public void setStatusType(String statusType) {
        this.statusType = statusType;
    }

    public String getPercent() {
        return percent;
    }

    public void setPercent(String percent) {
        this.percent = percent;
    }
}
