package com.fangcang.hotel.irevebld.xxljob;

import com.fangcang.hotel.irevebld.enums.SlsLevelEnum;
import com.fangcang.hotel.irevebld.service.IREVEBLDHotelService;
import com.fangcang.hotel.irevebld.service.impl.IREVEBLDLogServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 全量同步ireve不落地酒店信息任务
 */
@Slf4j
@Component
public class SyncAllIREVEBLDHotelInfoRunner {

    @Autowired
    private IREVEBLDHotelService irevebldHotelService;

    @Autowired
    private IREVEBLDLogServiceImpl irevebldLogServiceImpl;

    @XxlJob("syncAllIREVEBLDHotelInfoRunner")
    public void syncAllIREVEBLDHotelInfoRunner() {
        try {
            XxlJobHelper.log("执行全量同步ireve不落地酒店信息任务开始");
            irevebldHotelService.syncAllIREVEBLDHotelInfoRunnerTask();
            XxlJobHelper.log("执行全量同步ireve不落地酒店信息任务结束");
        } catch (Exception e) {
            log.error("执行全量同步ireve不落地酒店信息任务异常", e);
            irevebldLogServiceImpl.saveSlsLog("执行全量同步ireve不落地酒店信息任务异常", SlsLevelEnum.ERROR, e);
            XxlJobHelper.log("执行全量同步ireve不落地酒店信息任务异常", e);
        }
    }
}
