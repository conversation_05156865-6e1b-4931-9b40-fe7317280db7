package com.fangcang.hotel.common.service.supply;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyPayConfigAditReqVO;
import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyPayConfigPageReqVO;
import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyPayConfigQueryReqVO;
import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyPayConfigRespVO;
import com.fangcang.hotel.common.convert.supply.SupplyPayConfigConvert;
import com.fangcang.hotel.common.dal.db.supply.SupplyPayConfigDO;
import com.fangcang.hotel.common.dal.db.supply.SupplyVccPayAccountDO;
import com.fangcang.hotel.common.dal.mysql.supply.SupplyPayConfigMapper;
import com.fangcang.hotel.core.enums.GlobalErrorCodeEnum;
import com.fangcang.hotel.data.api.enums.ModeOfPayEnum;
import com.fangcang.hotel.data.api.enums.PayDimensionEnum;
import com.fangcang.hotel.framework.common.domain.PageResult;
import com.fangcang.hotel.core.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static com.fangcang.hotel.framework.common.exception.util.BizExceptionUtilX.exception;


/**
 * 供应商付款配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SupplyPayConfigServiceImpl implements SupplyPayConfigService {

    @Resource
    private SupplyPayConfigMapper supplyPayConfigMapper;

    @Autowired
    private SupplyVccPayAccountService supplyVccPayAccountService;

    @Override
    public Long supplyPayConfigAdd(SupplyPayConfigAditReqVO reqVO) {

        if (!reqVO.getStatus().equals(StatusEnum.ACTIVE.getValue())) {
            throw exception(GlobalErrorCodeEnum.SUPPLY_UNABLE_TO_ADD_INVALID_DATA);
        }

        if (reqVO.getModeOfPay().equals(ModeOfPayEnum.FIXEDVCC.getCode())) {
            if (reqVO.getPayVccId() == null) {
                // 固定VCC 必须传入固定VCCId
                throw exception(GlobalErrorCodeEnum.SUPPLY_VCC_PAY_ACCOUNT_NOT_NULL);
            }
            // 校验固定VCCId 是否有效
            SupplyVccPayAccountDO supplyVccPayAccountDO = supplyVccPayAccountService.supplyVccPayAccountDetail(reqVO.getPayVccId());
            if (supplyVccPayAccountDO == null) {
                throw exception(GlobalErrorCodeEnum.SUPPLY_VCC_PAY_ACCOUNT_NOT_EXISTS);
            }
            reqVO.setPaymentSubjectCode(null);
            reqVO.setPaymentSubjectName(null);
        } else  {
            //分类为动态VCC和Billback才有该选择付款主体
            reqVO.setPayVccId(null);
        }


        //非供应类型 则都需要写入供应商编码
        if (reqVO.getPayDimension().equals(PayDimensionEnum.SUPPLYTYPE.getCode())) {
            reqVO.setMerchantCode(null);
            reqVO.setMerchantSource(null);
            reqVO.setMerchantCode(null);
            reqVO.setSupplyCode(null);
            reqVO.setSupplyName(null);
        }else if(StrUtil.isBlank(reqVO.getMerchantSource())
                || StrUtil.isBlank(reqVO.getMerchantCode())
                || StrUtil.isBlank(reqVO.getMerchantName())){
            throw exception(GlobalErrorCodeEnum.BAD_REQUEST);
        }


        SupplyPayConfigQueryReqVO supplyPayConfigQueryReqVO = new SupplyPayConfigQueryReqVO();
        supplyPayConfigQueryReqVO.setMerchantSource(reqVO.getMerchantSource());
        supplyPayConfigQueryReqVO.setMerchantCode(reqVO.getMerchantCode());
        supplyPayConfigQueryReqVO.setSupplyClass(reqVO.getSupplyClass());
        supplyPayConfigQueryReqVO.setSupplyCode(reqVO.getSupplyCode());
        supplyPayConfigQueryReqVO.setPayMethod(reqVO.getPayMethod());
        supplyPayConfigQueryReqVO.setPayDimension(reqVO.getPayDimension());
        supplyPayConfigQueryReqVO.setAttributeValue(reqVO.getAttributeValue());
        supplyPayConfigQueryReqVO.setStatus(reqVO.getStatus());
        //相同条件的数据只能有一条
        SupplyPayConfigDO supplyPayConfigDOS = supplyPayConfigMapper.selectOne(supplyPayConfigQueryReqVO);



        if (supplyPayConfigDOS != null) {
           // 已存在该付款类型配置
            throw exception(GlobalErrorCodeEnum.SUPPLY_PAY_CONFIG_REPEAT);
        }else {
            SupplyPayConfigDO supplyPayConfig = SupplyPayConfigConvert.INSTANCE.convert(reqVO);
            //新增操作
            supplyPayConfigMapper.insert(supplyPayConfig);
            // 返回
            return supplyPayConfig.getId();
        }
    }

    @Override
    public Long supplyPayConfigEdit(SupplyPayConfigAditReqVO reqVO) {
        // 校验存在
        this.supplyPayConfigValidateExists(reqVO.getId());

        SupplyPayConfigQueryReqVO supplyPayConfigQueryReqVO = new SupplyPayConfigQueryReqVO();
        supplyPayConfigQueryReqVO.setMerchantSource(reqVO.getMerchantSource());
        supplyPayConfigQueryReqVO.setMerchantCode(reqVO.getMerchantCode());
        supplyPayConfigQueryReqVO.setSupplyClass(reqVO.getSupplyClass());
        supplyPayConfigQueryReqVO.setSupplyCode(reqVO.getSupplyCode());
        supplyPayConfigQueryReqVO.setPayMethod(reqVO.getPayMethod());
        supplyPayConfigQueryReqVO.setPayDimension(reqVO.getPayDimension());
        supplyPayConfigQueryReqVO.setAttributeValue(reqVO.getAttributeValue());
        supplyPayConfigQueryReqVO.setStatus(reqVO.getStatus());
        //相同条件的数据只能有一条
        SupplyPayConfigDO supplyPayConfigDOS = supplyPayConfigMapper.selectOne(supplyPayConfigQueryReqVO);
        if (supplyPayConfigDOS != null && !supplyPayConfigDOS.getId().equals(reqVO.getId())) {
            // 这里存在两种修改
            // 1.其他支付方式已存在 不允许修改
            // 2.当前主体方式修改 支付主体|vcc卡号 其他不变更则 可以进行修改
            throw exception(GlobalErrorCodeEnum.SUPPLY_PAY_CONFIG_REPEAT);
        } else {
            // 更新
            SupplyPayConfigDO supplyPayConfig = SupplyPayConfigConvert.INSTANCE.convert(reqVO);
            supplyPayConfigMapper.updateById(supplyPayConfig);
            // 返回
            return supplyPayConfig.getId();
        }
    }

    @Override
    public void supplyPayConfigDel(Long id) {
        // 校验存在
        this.supplyPayConfigValidateExists(id);
        // 删除
        supplyPayConfigMapper.deleteById(id);
    }

    private SupplyPayConfigDO supplyPayConfigValidateExists(Long id) {
        SupplyPayConfigDO supplyPayConfig = supplyPayConfigMapper.selectById(id);
        if (supplyPayConfig == null) {
            throw exception(GlobalErrorCodeEnum.VCC_PAY_ACCOUNT_INFO_NOT_EXIST);
        }
        return supplyPayConfig;
    }

    @Override
    public SupplyPayConfigDO supplyPayConfigDetail(Long id) {

        return supplyPayConfigMapper.selectById(id);
    }

    @Override
    public SupplyPayConfigRespVO selectSupplyPayConfigById(Long id) {
        return supplyPayConfigMapper.selectSupplyPayConfigById(id);
    }

    @Override
    public SupplyPayConfigRespVO selectSupplyPayConfig(SupplyPayConfigQueryReqVO reqVO) {
        return supplyPayConfigMapper.selectSupplyPayConfig(reqVO);
    }

    @Override
    public List<SupplyPayConfigDO> supplyPayConfigList(SupplyPayConfigQueryReqVO reqVO) {
        return supplyPayConfigMapper.selectList(reqVO);
    }

    @Override
    public PageResult<SupplyPayConfigRespVO> supplyPayConfigPage(SupplyPayConfigPageReqVO reqVO) {

        //分页查询
        IPage<SupplyPayConfigRespVO> supplyPayConfigPage = supplyPayConfigMapper.selectSupplyPayConfigPage(new Page<SupplyPayConfigRespVO>(reqVO.getCurrentPage(), reqVO.getPageSize()), reqVO);

        //转换
        PageResult<SupplyPayConfigRespVO> pageResult = PageResult.init(supplyPayConfigPage);

        return pageResult;
    }

}
