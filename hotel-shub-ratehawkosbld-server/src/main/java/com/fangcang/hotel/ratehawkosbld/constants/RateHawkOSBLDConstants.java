package com.fangcang.hotel.ratehawkosbld.constants;

/**
 * 公共常量类
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:15
 */
public class RateHawkOSBLDConstants {

    /**
     * 请求成功响应码
     */
    public static final String SUCCESS = "ok";

    public static final String ALL_HOTEL_FILE_NAME = "all_hotel_partner_feed_en_v3.json.zst";

    public static final String INCREMENT_HOTEL_FILE_NAME = "increment_hotel_partner_feed_en_v3.json.zst";

    /**
     * 全量包--首次落库异常的文件数据
     */
    public static final String EXCEPTION_ALL_HOTEL_FILE_NAME = "exception_all_hotel_info.text";

    /**
     * 增量包--首次落库异常的文件数据
     */
    public static final String EXCEPTION_INCREMENT_HOTEL_FILE_NAME = "exception_increment_hotel_info.text";

    /**
     * 全量包--首次落库异常后再次落库再次异常的文件数据
     */
    public static final String EXCEPTION_SECOND_ALL_HOTEL_FILE_NAME = "exception_second_all_hotel_info.text";

    /**
     * 增量包--首次落库异常后再次落库再次异常的文件数据
     */
    public static final String EXCEPTION_SECOND_INCREMENT_HOTEL_FILE_NAME = "exception_second_increment_hotel_info.text";

    public static final String CITY_INFO_HOTEL_FILE_NAME = "region.json.zst";

    /**
     * 请求类型
     */
    public static final String CONTENT_TYPE = "application/json;charset=UTF-8";


    /**
     * 全量查询酒店静态信息，返回的是一个文件地址
     */
    public static final String QUERY_HOTEL_AND_ROOM_LIST = "/api/b2b/v3/hotel/info/dump/";

    /**
     * 增量查询酒店静态信息，返回的是一个文件地址
     */
    public static final String QUERY_HOTEL_AND_ROOM_LIST_INCREMENT = "/api/b2b/v3/hotel/info/incremental_dump/";

    /**
     * 查询酒店详情
     */
    public static final String QUERY_HOTEL_DETAIL = "/api/b2b/v3/hotel/info/";

    /**
     * 查询起价
     */
    public static final String QUERY_LOWEST_PRICE = "/api/b2b/v3/search/serp/hotels/";

    /**
     * 查询房型每日价格
     */
    public static final String QUERY_ROOM_PRICE_LIST = "/api/b2b/v3/search/hp/";

    /**
     * 试预定
     */
    public static final String PREBOOKING = "/api/b2b/v3/hotel/prebook";

    /**
     * 订单预订表格
     */
    public static String ORDER_BOOKING_FORM = "/api/b2b/v3/hotel/order/booking/form/";

    /**
     * 发送信用卡信息
     */
    public static String CREDIT_CARD_DATA_TOKENIZATION = "/api/public/v1/manage/init_partners";

    /**
     * 订单预定完成
     */
    public static String ORDER_BOOKING_FINISH = "/api/b2b/v3/hotel/order/booking/finish/";

//    /**
//     * 创建订单
//     */
//    public static final String CREATE_ORDER = "/HomeinnsAgentApi/api/Resv/CreateOrder";

    /**
     * 取消订单
     */
    public static final String CANCEL_ORDER = "/api/b2b/v3/hotel/order/cancel/";

    /**
     * 查询订单状态
     */
    public static final String QUERY_ORDER_STATUS = "/api/b2b/v3/hotel/order/booking/finish/status/";

    /**
     * 查询订单详情
     */
    public static final String QUERY_ORDER = "/api/b2b/v3/hotel/order/info/";

    /**
     * 查询城市列表
     */
    public static final String QUERY_CITY_INFO_LIST = "/api/b2b/v3/hotel/region/dump/";


}
