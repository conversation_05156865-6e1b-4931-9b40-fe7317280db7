package com.fangcang.hotel.common.api.common.config.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @description: 自动注册供应商 信息以及扩展字段
 * @author: qiu
 * @create: 2024-03-14 14:45
 */
@Data
public class AutomaticRegisterSupplyDTO {

    //商户来源
    @NotEmpty(message = "商户来源不能为空")
    private String merchantSource;
    //商户编码
    @NotEmpty(message = "商户编码不能为空")
    private String merchantCode;
    //商户名称
    @NotEmpty(message = "商户名称不能为空")
    private String merchantName;
    //供应商类型
    @NotEmpty(message = "供应商类型不能为空")
    private String supplyClass;
    //供应商名称
    @NotEmpty(message = "供应商名称不能为空")
    private String supplyName;
    //供应商编码
    @NotEmpty(message = "供应商编码不能为空")
    private String supplyCode;
    //合作商编码
    @NotEmpty(message = "合作商编码不能为空")
    private String partnerCode;
    //密钥
    @NotEmpty(message = "密钥不能为空")
    private String signature;
    //供应商酒店id
    @NotEmpty(message = "酒店id不能为空")
    private String hotelId;
}