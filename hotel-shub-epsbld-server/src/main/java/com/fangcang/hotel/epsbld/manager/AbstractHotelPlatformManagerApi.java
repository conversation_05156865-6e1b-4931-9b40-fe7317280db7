package com.fangcang.hotel.epsbld.manager;

import com.fangcang.hotel.data.api.dto.BaseExtendConfig;
import com.fangcang.hotel.data.api.dto.SupplyProductRequest;
import com.fangcang.hotel.data.api.dto.order.cancel.CancelSupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.create.CreateSupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.prebooking.PreBookingSupplyRequest;
import com.fangcang.hotel.data.api.dto.order.query.QuerySupplyOrderRequest;
import com.fangcang.hotel.data.base.exception.RetryException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;

@Slf4j
public abstract class AbstractHotelPlatformManagerApi implements HotelPlatformManagerApi {

    /**
     * 调用第三方接口
     */
    @Override
    public <T> T send(Supplier<T> supplier) {
        //不成功那么就重试，重试次数 1+3
        int count = 0;
        int retryCount = 3;
        T result = null;
        while (count++ <= retryCount) {
            boolean error = true;
            try {
                result = supplier.get();
            } catch (RetryException e) {
                log.error("调用第三方接口异常，当前调用次数：{}", count);
                error = false;
            }

            // 该判断两个作用：
            // 1. 结果不是null 证明调用成功 就直接退出
            // 2. 结果为null并且没有出现重试异常。那这种情况可能就是返回的数据是null 或者异常信息已经被处理了处理的过程中觉得不用去重试发送了 这种情况也直接退出。
            if (result != null || error) {
                break;
            }
        }
        return result;
    }


    /**
     * 查询供应商的产品信息
     */
    public abstract <T> T doQueryHotelDetail(List<String> requestList, SupplyProductRequest supplyProductRequest, BaseExtendConfig baseExtendConfigProperties, Class<T> acquireType);

    /**
     * 试预定
     */
    public abstract <T> T doPreBooking(List<String> requestList, PreBookingSupplyRequest preBookingSupplyRequest, BaseExtendConfig baseExtendConfigProperties, Map<String, String> extendMap, Class<T> acquireType);

    /**
     * 创建订单
     */
    public abstract <T> T doCreateOrder(List<String> requestList, CreateSupplyOrderRequest createSupplyOrderRequest, BaseExtendConfig baseExtendConfigProperties, Map<String, String> extendMap, Class<T> acquireType);

    /**
     * 查询订单
     */
    public abstract <T> T doQueryOrder(List<String> requestList, QuerySupplyOrderRequest supplyOrderQueryReqDTO, BaseExtendConfig baseExtendConfigProperties, Map<String, String> extendMap, Class<T> acquireType);

    /**
     * 取消订单
     */
    public abstract <T> T doCancelOrder(List<String> requestList, CancelSupplyOrderRequest cancelSupplyOrderRequest, BaseExtendConfig baseExtendConfigProperties, Map<String, String> extendMap, Class<T> acquireType);

    /**
     * 根据城市编码获取酒店ID
     */
    public Set<String> doQueryHotelIdsByCityCode(String cityId) {
        return null;
    }

    /**
     * 根据酒店ID查询酒店基础信息
     */
    public <T> T doQueryHotelInfoByHotelId(List<String> hotelIds, String language, Class<T> acquireType) {
        return null;
    }
}
