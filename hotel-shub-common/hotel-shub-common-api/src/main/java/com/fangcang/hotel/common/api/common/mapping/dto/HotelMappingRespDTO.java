package com.fangcang.hotel.common.api.common.mapping.dto;

import com.fangcang.hotel.common.api.common.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description: 查询酒店映射返回数据
 * @author: qiu
 * @create: 2023-08-14 09:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HotelMappingRespDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 供应商酒店ID */
    private String spHotelId;

    /** 酒店id */
    private String hotelId;

    /** 供应商酒店名称 */
    private String spHotelName;

    private String spHotelNameEn;

    /** 酒店名称 */
    private String hotelName;

    private String hotelNameEn;

    /** 酒店地址 */
    private String hotelAddress;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 省份编码
     */
    private String provinceCode;

    /** 城市编码 */
    private String cityCode;

    /** 供应类型 */
    private String supplyClass;

    /** 状态 [有效,无效] */
    private Integer status;
}