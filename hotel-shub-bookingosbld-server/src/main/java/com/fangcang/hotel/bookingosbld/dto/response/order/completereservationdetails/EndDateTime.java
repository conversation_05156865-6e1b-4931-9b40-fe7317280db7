package com.fangcang.hotel.bookingosbld.dto.response.order.completereservationdetails;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/29 20:55
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "endDateTime")
public class EndDateTime {

    @XmlElement(name = "year")
    private String year;

    @XmlElement(name = "month")
    private String month;

    @XmlElement(name = "day")
    private String day;

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }
}
