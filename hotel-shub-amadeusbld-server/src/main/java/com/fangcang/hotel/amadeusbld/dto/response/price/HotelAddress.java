package com.fangcang.hotel.amadeusbld.dto.response.price;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/18 17:35
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Address")
public class HotelAddress {

    @XmlElement(name = "AddressLine")
    private String addressLine;

    @XmlElement(name = "CityName")
    private String cityName;

    @XmlElement(name = "PostalCode")
    private String postalCode;

    @XmlElement(name = "CountryName")
    private String countryName;

    public String getAddressLine() {
        return addressLine;
    }

    public void setAddressLine(String addressLine) {
        this.addressLine = addressLine;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }
}
