package com.fangcang.hotel.didaosbld.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/4 15:09
 * @Description:
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HotelBookingConfirmRequest extends BaseRequest {

    /**
     * 要填写 Price Confirm 的 Response 里的 ReferenceNo
     */
    @JSONField(name = "ReferenceNo")
    private String ReferenceNo;

    @JSONField(name = "CheckInDate")
    private String checkInDate;

    @JSONField(name = "CheckOutDate")
    private String checkOutDate;

    @JSONField(name = "NumOfRooms")
    private Integer numOfRooms;

    @JSONField(name = "GuestList")
    private List<GuestRequest> guestList;

    /**
     * 联系人信息。可以填客人的联系信息 或 贵司的订单客服联系信息
     */
    @JSONField(name = "Contact")
    private ContactRequest contact;

    /**
     * 特殊需求。我们会尽力满足客人的需求，但无法保证能完全满足
     */
    @JSONField(name = "CustomerRequest")
    private String customerRequest;

    /**
     * 贵方的订单号。贵方的订单号跟我们的订单号存在一对一的绑定关系，贵方不能使用同一个订单号来创建两个或以上的道旅订单。请注意，此字段为选填，假如不提供此字段的值，那么您无须担心一对一关系的问题，不提供就不构成这个关系。
     */
    @JSONField(name = "ClientReference")
    private String clientReference;

}
