package com.fangcang.hotel.ratehawkosbld.util;

import com.fangcang.hotel.core.util.StringUtilExtend;
import com.fangcang.hotel.data.api.dto.ChildrenInfoDTO;
import com.fangcang.hotel.data.api.dto.GuestDTO;
import com.fangcang.hotel.data.api.dto.RoomGuestNumberDTO;
import com.fangcang.hotel.data.api.dto.order.create.CreateSupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.query.QuerySupplyOrderRequest;
import com.fangcang.hotel.framework.common.util.CollUtilX;
import com.fangcang.hotel.framework.common.util.DateUtilX;
import com.fangcang.hotel.framework.common.util.StrUtilX;
import com.fangcang.hotel.ratehawkosbld.config.RateHawkOSBLDConfig;
import com.fangcang.hotel.ratehawkosbld.config.RateHawkOSBLDExtendConfig;
import com.fangcang.hotel.ratehawkosbld.dto.request.*;
import com.fangcang.hotel.ratehawkosbld.dto.response.*;
import com.fangcang.hotel.ratehawkosbld.enums.LanguageEnum;

import java.math.BigDecimal;
import java.util.*;

public class RateHawkOSBLDUtil {

    public static OrderBookingRequest createOrderBookingRequest(String merchantOrderCode, String bookHash, RateHawkOSBLDExtendConfig extendConfig) {
        OrderBookingRequest orderBookingRequest = new OrderBookingRequest();
        orderBookingRequest.setPartnerOrderId(merchantOrderCode);
        orderBookingRequest.setBookHash(bookHash);
        orderBookingRequest.setLanguage(LanguageEnum.EN.getEn());
        orderBookingRequest.setUserIp(extendConfig.getUserIp());
        return orderBookingRequest;
    }


    public static OrderBookingFinishRequest createOrderFinishRequest(CreateSupplyOrderRequest createSupplyOrderRequest, RateHawkOSBLDExtendConfig extendConfig,
                                                                     RateHawkOSBLDConfig rateHawkOSBLDConfig,String paymentType) {
        OrderBookingFinishRequest request = new OrderBookingFinishRequest();
        //合作供货单信息
        HotelOrderBookingFinishPartner finishPartner = new HotelOrderBookingFinishPartner();
        finishPartner.setPartnerOrderId(createSupplyOrderRequest.getMerchantOrderCode());
        if (StringUtilExtend.isValidString(createSupplyOrderRequest.getRemark())){
            finishPartner.setComment(createSupplyOrderRequest.getRemark());
        }

        //订单付款信息
        HotelOrderBookingFinishPaymentType finishPaymentType = new HotelOrderBookingFinishPaymentType();
        finishPaymentType.setType(paymentType);
        finishPaymentType.setAmount(createSupplyOrderRequest.getTotalAmount());
        finishPaymentType.setCurrencyCode(extendConfig.getCurrency());


        //房间客人、人数
        List<HotelOrderBookingFinishRoom> roomList = new ArrayList<>();

        List<RoomGuestNumberDTO> roomGuestNumbers = createSupplyOrderRequest.getRoomGuestNumbers();
        List<GuestDTO> guests = createSupplyOrderRequest.getGuests();

        for (RoomGuestNumberDTO roomGuestNumber : roomGuestNumbers) {
            List<HotelOrderBookingFinishGuest> finishGuestList = new ArrayList<>();
            for (GuestDTO guest : guests) {
                if (roomGuestNumber.getRoomIndex().equals(guest.getRoomIndex())) {
                    HotelOrderBookingFinishGuest finishGuest = new HotelOrderBookingFinishGuest();
                    finishGuest.setFirstName(guest.getFirstName());
                    finishGuest.setLastName(guest.getLastName());
                    finishGuestList.add(finishGuest);
                    if (null != roomGuestNumber.getChildrenInfos() && CollUtilX.isNotEmpty(roomGuestNumber.getChildrenInfos())){
                        for (ChildrenInfoDTO childrenInfo : roomGuestNumber.getChildrenInfos()) {
                            HotelOrderBookingFinishGuest childGuest = new HotelOrderBookingFinishGuest();
                            childGuest.setAge(childrenInfo.getChildrenAge());
                            childGuest.setIsChild(true);
                            finishGuestList.add(childGuest);
                        }

                    }

                }
            }

            HotelOrderBookingFinishRoom finishRoom = new HotelOrderBookingFinishRoom();
            finishRoom.setGuests(finishGuestList);
            roomList.add(finishRoom);
        }


        //合作伙伴信息
        HotelOrderBookingFinishUser finishUser = new HotelOrderBookingFinishUser();
        String userPhone = "";
        String userEmail = "";
        if (StrUtilX.isEmpty(extendConfig.getUserPhone()) || StrUtilX.isEmpty(extendConfig.getUserEmail())){
            userPhone = rateHawkOSBLDConfig.getUserPhone();
            userEmail = rateHawkOSBLDConfig.getUserEmail();
        }else {
            userPhone = extendConfig.getUserPhone();
            userEmail = extendConfig.getUserEmail();
        }
        finishUser.setPhone(userPhone);
        finishUser.setEmail(userEmail);

        //供应商数据
        HotelOrderBookingFinishSupplierData finishSupplierData = new HotelOrderBookingFinishSupplierData();

        String supplierLastName = "";
        String supplierFirstName = "";
        String supplierEmail = "";
        String supplierPhone = "";
        if (StrUtilX.isEmpty(extendConfig.getSupplierPhone()) || StrUtilX.isEmpty(extendConfig.getSupplierEmail()) || StrUtilX.isEmpty(extendConfig.getSupplierFirstName())
                || StrUtilX.isEmpty(extendConfig.getSupplierLastName())){
            supplierPhone = rateHawkOSBLDConfig.getSupplierPhone();
            supplierEmail = rateHawkOSBLDConfig.getSupplierEmail();
            supplierFirstName = rateHawkOSBLDConfig.getSupplierFirstName();
            supplierLastName = rateHawkOSBLDConfig.getSupplierLastName();
        }else {
            supplierPhone = extendConfig.getSupplierPhone();
            supplierEmail = extendConfig.getSupplierEmail();
            supplierFirstName = extendConfig.getSupplierLastName();
            supplierLastName = extendConfig.getSupplierFirstName();
        }
        finishSupplierData.setEmail(supplierEmail);
        finishSupplierData.setPhone(supplierPhone);
        finishSupplierData.setFirstNameOriginal(supplierFirstName);
        finishSupplierData.setLastNameOriginal(supplierLastName);

        request.setRooms(roomList);
        request.setLanguage(LanguageEnum.EN.getEn());
        request.setPartner(finishPartner);
        request.setPaymentType(finishPaymentType);
        request.setUser(finishUser);
        request.setSupplierData(finishSupplierData);

        return request;
    }


    public static OrderBookingFinishStatusRequest createOrderBookingFinishStatusRequest(String merchantOrderCode) {
        OrderBookingFinishStatusRequest finishStatusRequest = new OrderBookingFinishStatusRequest();
        finishStatusRequest.setPartnerOrderId(merchantOrderCode);
        return finishStatusRequest;
    }

    public static OrderInformationRequest createOrderInformationRequest(QuerySupplyOrderRequest querySupplyOrderRequest){
        B2bHotelSearchIn searchIn = new B2bHotelSearchIn();
        List<Integer> supplyOrderIds = new ArrayList<>();
        if (Objects.nonNull(querySupplyOrderRequest.getSupplyOrderCode())){
            supplyOrderIds.add(Integer.valueOf(querySupplyOrderRequest.getSupplyOrderCode()));
        }

        PAPIDateRange checkIn = new PAPIDateRange();
        checkIn.setFromDate(querySupplyOrderRequest.getCheckInDate());
        checkIn.setToDate(querySupplyOrderRequest.getCheckOutDate());

        PAPIDateRange checkOut = new PAPIDateRange();
        checkOut.setFromDate(querySupplyOrderRequest.getCheckInDate());
        checkOut.setToDate(querySupplyOrderRequest.getCheckOutDate());

        List<String> merchantOrderIds = new ArrayList<>();
        if (Objects.nonNull(querySupplyOrderRequest.getMerchantOrderCode())){
            merchantOrderIds.add(querySupplyOrderRequest.getMerchantOrderCode());
        }

        searchIn.setOrderIds(supplyOrderIds);
        searchIn.setPartnerOrderIds(merchantOrderIds);
        searchIn.setCheckinAt(checkIn);
        searchIn.setCheckoutAt(checkOut);

        HotelOrderingIn orderingIn = new HotelOrderingIn();
        //升序
        orderingIn.setOrderingType("asc");
        orderingIn.setOrderingBy("created_at");

        PAPIPaginationIn paginationIn = new PAPIPaginationIn();
        paginationIn.setPageSize(50);
        paginationIn.setPageNumber(1);

        OrderInformationRequest request = new OrderInformationRequest();
        request.setSearch(searchIn);
        request.setOrdering(orderingIn);
        request.setLanguage(LanguageEnum.EN.getEn());
        request.setPagination(paginationIn);
        return request;
    }
}
