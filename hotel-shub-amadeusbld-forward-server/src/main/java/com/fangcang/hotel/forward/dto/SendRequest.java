package com.fangcang.hotel.forward.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class SendRequest implements Serializable {


    //报文
    private String requestInfo;

    //请求路径
    private String requestUrl;

    //请求报文类型
    private String contentType;

    private Integer socketTimeOut;

    private Integer connectTime;

    //请求头
    private Map<String,String> headers;

    //请求方法(默认为POST请求)
    private String requestMethod;
}
