package com.fangcang.hotel.common.controller.admin.supply.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.fangcang.hotel.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 供应商酒店信息 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class SupplyHotelBaseVO {

    /** id */
    private Long id;

    /** 供应商酒店ID */
    @NotEmpty(message = "供应商酒店ID不能为空")
    private String spHotelId;

    /** 供应商酒店名称 */
    @NotEmpty(message = "供应商酒店名称不能为空")
    private String spHotelName;

    /** 供应商酒店名称英文 */
    private String spHotelNameEn;

    /** 供应商酒店地址 */
    private String spHotelAddress;

    /** 供应商酒店地址英文 */
    private String spHotelAddressEn;

    /** 供应商酒店星级 */
    private String spHotelStar;

    /** 供应商国家编码 */
    private String spCountryCode;

    /** 供应商省份编码 */
    private String spProvinceCode;

    /** 供应商城市编码 */
    private String spCityCode;

    /** 供应商宾客类型 */
    private String spGuestType;

    /** 宾客类型 [任意,内宾,港澳台,外宾,其他] */
    private Integer guestType;

    /** 酒店星级 */
    private Integer hotelStar;

    /** 集团编码 */
    private String groupCode;

    /** 集团名称 */
    private String groupName;

    /** 品牌编码 */
    private String brandCode;

    /** 品牌名称 */
    private String brandName;

    /** 联系电话 */
    private String phone;

    /** 百度经度 */
    private String lngBaidu;

    /** 百度纬度 */
    private String latBaidu;

    /** 谷歌经度 */
    private String lngGoogle;

    /** 谷歌纬度 */
    private String latGoogle;

    /** 高德经度 */
    private String lngGaode;

    /** 高德纬度 */
    private String latGaode;

    /** 扩展 */
    private String ext;

    /** 供应类型 */
    @NotEmpty(message = "供应类型不能为空")
    private String supplyClass;

    /** 状态 [有效,无效] */
    @NotNull(message = "状态 [有效,无效]不能为空")
    private Integer status;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

}
