package com.fangcang.hotel.amadeusosbld.dto.response.order.sell;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @description:
 * @author: qiu
 * @create: 2024-04-12 18:43
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "roomListInfo")
public class RoomListInfo {

    @XmlElement(name = "requestableInformation")
    private RequestableInformation requestableInformation;

    public RequestableInformation getRequestableInformation() {
        return requestableInformation;
    }

    public void setRequestableInformation(RequestableInformation requestableInformation) {
        this.requestableInformation = requestableInformation;
    }
}