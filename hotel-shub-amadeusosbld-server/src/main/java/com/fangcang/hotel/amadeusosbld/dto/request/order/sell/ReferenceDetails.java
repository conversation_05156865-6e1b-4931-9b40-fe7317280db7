package com.fangcang.hotel.amadeusosbld.dto.request.order.sell;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/29 17:05
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "referenceDetails")
public class ReferenceDetails {

    @XmlElement(name = "type")
    private String type;

    @XmlElement(name = "value")
    private String value;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
