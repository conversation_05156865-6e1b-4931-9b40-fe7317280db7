package com.fangcang.hotel.hzbld.dto.request;

import java.util.Date;
import java.math.BigDecimal;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  每日房价列表
 *
 * <AUTHOR>
 * @date 2023-12-11 14:22:34
 */
@Data
public class DailyPriceParameter implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 营业日
     */
    private Date bizDate;

    /**
     * 房价（税后价）
     */
    private BigDecimal afterTaxPrice;

    /**
     * 优惠券码
     */
    private String couponNo;

}
