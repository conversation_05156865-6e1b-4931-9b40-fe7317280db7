package com.fangcang.hotel.bookingosbld.dto.request.order.retrieve;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/29 18:40
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "retrievalFacts")
public class RetrievalFacts {

    @XmlElement(name = "retrieve")
    private Retrieve retrieve;

    @XmlElement(name = "reservationOrProfileIdentifier")
    private ReservationOrProfileIdentifier reservationOrProfileIdentifier;

    public Retrieve getRetrieve() {
        return retrieve;
    }

    public void setRetrieve(Retrieve retrieve) {
        this.retrieve = retrieve;
    }

    public ReservationOrProfileIdentifier getReservationOrProfileIdentifier() {
        return reservationOrProfileIdentifier;
    }

    public void setReservationOrProfileIdentifier(ReservationOrProfileIdentifier reservationOrProfileIdentifier) {
        this.reservationOrProfileIdentifier = reservationOrProfileIdentifier;
    }
}
