package com.fangcang.hotel.common.api.mapping;

import cn.hutool.core.collection.CollectionUtil;
import com.fangcang.hotel.common.api.common.mapping.CityMappingApi;
import com.fangcang.hotel.common.api.common.mapping.dto.*;
import com.fangcang.hotel.common.controller.admin.mapping.vo.CityMappingAditReqVO;
import com.fangcang.hotel.common.controller.admin.mapping.vo.CityMappingQueryReqVO;
import com.fangcang.hotel.common.convert.mapping.CityMappingConvert;
import com.fangcang.hotel.common.dal.db.mapping.CityMappingDO;
import com.fangcang.hotel.common.service.mapping.CityMappingService;
import com.fangcang.hotel.framework.common.domain.ResultX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description: 城市映射API
 * @author: qiu
 * @create: 2023-09-26 17:58
 */
@RestController
public class CityMappingApiImpl implements CityMappingApi {

    @Autowired
    private CityMappingService cityMappingService;

    @Override
    public ResultX<List<AreasRespDTO>>  queryAreasList(AreasReqDTO areasRequest) {
        return cityMappingService.queryAreasList(areasRequest);
    }

    @Override
    public ResultX addOrUpdateCityMapping(List<CityMappingReqDTO> cityMappingReqDTOS) {
        if(CollectionUtil.isNotEmpty(cityMappingReqDTOS)){
            List<CityMappingAditReqVO> cityMappingAditReqVOS = CityMappingConvert.INSTANCE.converCityMappingAditList(cityMappingReqDTOS);
            cityMappingService.cityMappingAdd(cityMappingAditReqVOS);
        }
        return ResultX.success();

    }


    @Override
    public ResultX<List<CityMappingRespDTO>> cityMappingList(CityMappingQueryReqDTO cityMappingQueryReqDTO) {
        //请求参数转换
        CityMappingQueryReqVO cityMappingQueryReqVO = CityMappingConvert.INSTANCE.convert(cityMappingQueryReqDTO);

        //执行查询
        List<CityMappingDO> cityMappingDOS = cityMappingService.cityMappingList(cityMappingQueryReqVO);

        //返回结果转换
        List<CityMappingRespDTO> cityMappingRespDTOS = CityMappingConvert.INSTANCE.convertDTOList(cityMappingDOS);

        //转换
        return ResultX.success(cityMappingRespDTOS);
    }
}