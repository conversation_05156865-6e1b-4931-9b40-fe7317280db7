package com.fangcang.hotel.bookingosbld.dto.request.order.addmultielements;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/28 17:13
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "freetextData")
public class FreetextData {

    @XmlElement(name = "freetextDetail")
    private FreetextDetail freetextDetail;

    @XmlElement(name = "longFreetext")
    private String longFreetext;

    public FreetextDetail getFreetextDetail() {
        return freetextDetail;
    }

    public void setFreetextDetail(FreetextDetail freetextDetail) {
        this.freetextDetail = freetextDetail;
    }

    public String getLongFreetext() {
        return longFreetext;
    }

    public void setLongFreetext(String longFreetext) {
        this.longFreetext = longFreetext;
    }
}
