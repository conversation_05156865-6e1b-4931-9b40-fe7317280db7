package com.fangcang.hotel.tmchubosbld.dto.request.hotel;

import com.fangcang.hotel.tmchubosbld.dto.entity.BaseBusinessRequest;
import lombok.Data;

@Data
public class ProductDetailInfoRequestBase extends BaseBusinessRequest {

    /**
     * 酒店id
     */
    private Long hotelId;

    /** 入住日期 */
    private String checkInDate;

    /**
     * 离店日期
     */
    private String checkOutDate;

    /**
     * 间数
     */
    private Integer roomNum;

   /**
    * 是否查出钟点房产品，空或false或连住多天时不查出，true查出。
    */
   private Boolean needClockRoomProduct;

    /**
     * 房间客人数量信息，海外酒店必传
     */
    private RoomGuestNumber roomGuestNumber;
}
