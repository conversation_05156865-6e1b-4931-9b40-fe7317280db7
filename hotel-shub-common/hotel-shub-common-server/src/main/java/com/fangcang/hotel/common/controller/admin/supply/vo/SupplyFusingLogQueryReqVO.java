package com.fangcang.hotel.common.controller.admin.supply.vo;

import lombok.*;

import java.time.LocalDateTime;

/**
 * 供应商熔断日志 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class SupplyFusingLogQueryReqVO {

    /** 商家来源 SAAS 飞天 */
    private String merchantSource;

    /** 商家编码 */
    private String merchantCode;

    /** 供应类型 */
    private String supplyClass;

    /** 供应商编码 */
    private String supplyCode;

    /** 内容 */
    private String content;

    /** 结果 */
    private String result;

    /** 开始时间 */
    private LocalDateTime[] startTime;

    /** 预计结束时间 */
    private LocalDateTime[] estimateEndTime;

    /** 实际结束时间 */
    private LocalDateTime[] actualEndTime;

}
