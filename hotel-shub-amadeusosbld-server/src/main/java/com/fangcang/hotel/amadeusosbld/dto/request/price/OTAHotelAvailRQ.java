package com.fangcang.hotel.amadeusosbld.dto.request.price;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/18 10:45
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "OTA_HotelAvailRQ")
public class OTAHotelAvailRQ {

    @XmlAttribute(name = "xmlns")
    private String xmlns = "http://www.opentravel.org/OTA/2003/05";

    @XmlAttribute(name = "EchoToken")
    private String echoToken;

    @XmlAttribute(name = "Version")
    private String version;

    @XmlAttribute(name = "PrimaryLangID")
    private String primaryLangID;

    @XmlAttribute(name = "SummaryOnly")
    private String summaryOnly;

    @XmlAttribute(name = "RateRangeOnly")
    private String rateRangeOnly;

    @XmlAttribute(name = "AvailRatesOnly")
    private String availRatesOnly;

    @XmlAttribute(name = "SearchCacheLevel")
    private String searchCacheLevel;

    @XmlAttribute(name = "RateDetailsInd")
    private String rateDetailsInd;

    @XmlAttribute(name = "OnRequestInd")
    private String onRequestInd;

    @XmlElementWrapper(name = "AvailRequestSegments")
    @XmlElement(name = "AvailRequestSegment")
    private List<AvailRequestSegment> availRequestSegmentList;

    public String getEchoToken() {
        return echoToken;
    }

    public void setEchoToken(String echoToken) {
        this.echoToken = echoToken;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getPrimaryLangID() {
        return primaryLangID;
    }

    public void setPrimaryLangID(String primaryLangID) {
        this.primaryLangID = primaryLangID;
    }

    public String getSummaryOnly() {
        return summaryOnly;
    }

    public void setSummaryOnly(String summaryOnly) {
        this.summaryOnly = summaryOnly;
    }

    public String getRateRangeOnly() {
        return rateRangeOnly;
    }

    public void setRateRangeOnly(String rateRangeOnly) {
        this.rateRangeOnly = rateRangeOnly;
    }

    public String getAvailRatesOnly() {
        return availRatesOnly;
    }

    public void setAvailRatesOnly(String availRatesOnly) {
        this.availRatesOnly = availRatesOnly;
    }

    public String getSearchCacheLevel() {
        return searchCacheLevel;
    }

    public void setSearchCacheLevel(String searchCacheLevel) {
        this.searchCacheLevel = searchCacheLevel;
    }

    public String getRateDetailsInd() {
        return rateDetailsInd;
    }

    public void setRateDetailsInd(String rateDetailsInd) {
        this.rateDetailsInd = rateDetailsInd;
    }

    public List<AvailRequestSegment> getAvailRequestSegmentList() {
        return availRequestSegmentList;
    }

    public void setAvailRequestSegmentList(List<AvailRequestSegment> availRequestSegmentList) {
        this.availRequestSegmentList = availRequestSegmentList;
    }

    public String getOnRequestInd() {
        return onRequestInd;
    }

    public void setOnRequestInd(String onRequestInd) {
        this.onRequestInd = onRequestInd;
    }
}
