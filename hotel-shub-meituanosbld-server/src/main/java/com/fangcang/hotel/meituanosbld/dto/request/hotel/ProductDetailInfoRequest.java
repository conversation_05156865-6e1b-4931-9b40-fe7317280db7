package com.fangcang.hotel.meituanosbld.dto.request.hotel;

import com.fangcang.hotel.meituanosbld.dto.entity.BusinessRequest;
import lombok.Data;

import java.util.List;

@Data
public class ProductDetailInfoRequest extends BusinessRequest {

    /**
     * 请求查询的境外酒店ID列表，一次最多查询20个
     */
    private List<Long> hotelIds;
    /**
     * 入住日期，格式为yyyy-MM-dd，不能早于当前日期
     */
    private String checkinDate;
    /**
     * 离店日期，格式为yyyy-MM-dd，与入住时期相差不能超过28天
     */
    private String checkoutDate;
    /**
     * 每间房成人数量，上限8人
     */
    private Integer numberOfAdults;
    /**
     * 每间房儿童数量，上限3人
     */
    private Integer numberOfChildren;
    /**
     * 儿童年龄，如：6,12。 儿童数量>0时必填，用英文逗号分割
     */
    private String childrenAges;
    /**
     * 币种，ISO 4217货币代码，目前支持人民币（CNY）或美金（USD）。注意：如需使用USD请线下反馈给您的对接人
     */
    private String currencyCode;
    /**
     * 入住人国籍，两位ISO国家代码，详见ISO 国家代码
     */
    private String clientNationality;
    /**
     * 售卖渠道 1 只返回面向C端用户售卖的产品 2 只返回面向B端用户售卖的产品 3 空值或不传，默认返回面向C端用户的产品
     */
    private Integer salesChannel;
    /**
     * 预订间数，最大8间
     */
    private Integer roomNum;

}
