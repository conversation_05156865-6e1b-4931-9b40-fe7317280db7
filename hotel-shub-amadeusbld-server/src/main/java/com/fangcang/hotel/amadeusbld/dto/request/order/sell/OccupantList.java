package com.fangcang.hotel.amadeusbld.dto.request.order.sell;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/29 16:56
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "occupantList")
public class OccupantList {

    @XmlElement(name = "passengerReference")
    private PassengerReference passengerReference;

    public PassengerReference getPassengerReference() {
        return passengerReference;
    }

    public void setPassengerReference(PassengerReference passengerReference) {
        this.passengerReference = passengerReference;
    }
}
