package com.fangcang.hotel.meituanosbld.service.impl;

import com.fangcang.hotel.common.api.common.config.dto.ConfigInfoRespDTO;
import com.fangcang.hotel.data.api.enums.SupplyClassEnum;
import com.fangcang.hotel.data.api.service.SupplyOrderService;
import com.fangcang.hotel.data.api.service.SupplyProductService;
import com.fangcang.hotel.data.base.annotation.SupplyConfigPlugin;
import com.fangcang.hotel.data.base.services.CacheService;
import com.fangcang.hotel.data.base.services.BaseSupplyConfigService;
import com.fangcang.hotel.meituanosbld.config.MEITUANOSBLDExtendConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 配置信息服务
 */
@Slf4j
@Service("meituanosbldExtendsConfigService")
@SupplyConfigPlugin(supplyClass = "MEITUANOSBLD")
public class MEITUANOSBLDExtendsConfigSupplyConfigService extends BaseSupplyConfigService {

    @Autowired
    private SupplyOrderService meituanosbldOrderService;

    @Autowired
    private SupplyProductService meituanosbldProductService;


    @Autowired
    private CacheService cacheService;


    /**
     * 配置信息到内存 供应商特殊处理 其他可以不需要
     */
    public static Map<String, MEITUANOSBLDExtendConfig> meituanosbldExtendConfigHashMap = new HashMap<String, MEITUANOSBLDExtendConfig>();
    /**
     * 获取订单服务
     * @return
     */
    @Override
    public SupplyOrderService setOrderService() {
        return meituanosbldOrderService;
    }

    /**
     * 获取实时查询产品服务
     * @return
     */
    @Override
    public SupplyProductService getSupplyProductService() {
        return meituanosbldProductService;
    }

    /**
     * 获取所有账号列表
     * @param configInfoRespDTOS
     */
    @Override
    public void getAllConfigInfo(List<ConfigInfoRespDTO> configInfoRespDTOS) {
        for (ConfigInfoRespDTO configInfoRespDTO : configInfoRespDTOS) {
            String key = configInfoRespDTO.getSupplyClass() + "_" + configInfoRespDTO.getMerchantCode()
                    + "_" + configInfoRespDTO.getSupplyCode();
            try {
                MEITUANOSBLDExtendConfig meituanosbldExtendConfig = (MEITUANOSBLDExtendConfig) cacheService.getCacheConfigExtend(configInfoRespDTO.getMerchantSource(),
                        configInfoRespDTO.getMerchantCode(), SupplyClassEnum.MEITUANOSBLD, configInfoRespDTO.getSupplyCode(), MEITUANOSBLDExtendConfig.class);
                meituanosbldExtendConfigHashMap.put(key, meituanosbldExtendConfig);
            } catch (Exception e) {
                log.error("MEITUANOSBLD不落地订单供应商未配置:" + key);
            }
        }
    }

    public Map<String, MEITUANOSBLDExtendConfig> getAllConfigExtend() {
        return meituanosbldExtendConfigHashMap;
    }
}
