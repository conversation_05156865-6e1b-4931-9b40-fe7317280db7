package com.fangcang.hotel.common.controller.admin.prepaid.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @program: supply-hub
 * @ClassName SupplyPrepaidWhiteConfigReqVO
 * @description:
 * @author: 湫
 * @create: 2024/11/05/ 9:52
 * @Version 1.0
 **/
@Data
public class SupplyPrepaidWhiteConfigReqVO {

    /** 商家编码 */
    private String merchantCode;

    /** 商家名称 */
    private String merchantName;

    /** 商家来源 */
    private String merchantSource;

    //供应商类型
    @NotEmpty(message = "supplyClass不能为null")
    private String supplyClass;

    //供应商编码
    private String supplyCode;

    //供应商名称
    private String supplyName;

    //供应商酒店id
    private String spHotelId;

    //房仓酒店名称
    private String hotelName;

    //默认查询有效的
    private Integer status = 1;
}
