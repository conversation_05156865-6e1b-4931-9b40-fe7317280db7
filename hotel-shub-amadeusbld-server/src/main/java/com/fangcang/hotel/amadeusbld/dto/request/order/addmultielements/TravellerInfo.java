package com.fangcang.hotel.amadeusbld.dto.request.order.addmultielements;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/28 16:41
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "travellerInfo")
public class TravellerInfo {

    @XmlElement(name = "elementManagementPassenger")
    private ElementManagementPassenger elementManagementPassenger;

    @XmlElement(name = "passengerData")
    private PassengerData passengerData;

    public ElementManagementPassenger getElementManagementPassenger() {
        return elementManagementPassenger;
    }

    public void setElementManagementPassenger(ElementManagementPassenger elementManagementPassenger) {
        this.elementManagementPassenger = elementManagementPassenger;
    }

    public PassengerData getPassengerData() {
        return passengerData;
    }

    public void setPassengerData(PassengerData passengerData) {
        this.passengerData = passengerData;
    }
}
