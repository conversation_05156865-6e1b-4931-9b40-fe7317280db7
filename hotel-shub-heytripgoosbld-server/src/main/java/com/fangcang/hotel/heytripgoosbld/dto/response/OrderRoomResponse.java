package com.fangcang.hotel.heytripgoosbld.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/3/3 10:32
 * @Description:
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderRoomResponse {

    /**
     * 房型名
     */
    @JSONField(name = "RoomName")
    private String roomName;

    /**
     * 床型名
     */
    @JSONField(name = "BedType")
    private String bedType;

    /**
     * 窗
     */
    @JSONField(name = "Window")
    private Integer window;

    /**
     * 早餐数
     */
    @JSONField(name = "Breakfast")
    private Integer breakfast;

    @JSONField(name = "MealType")
    private Integer mealType;

    @JSONField(name = "Lunch")
    private Integer lunch;

    @JSONField(name = "Dinner")
    private Integer dinner;

    @JSONField(name = "HasMeal")
    private Boolean hasMeal;

    @JSONField(name = "MealTypeStr")
    private String mealTypeStr;

}
