package com.fangcang.hotel.common.api.common.supply.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.time.LocalDate;

import static com.fangcang.hotel.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;

/**
 * @description: 供应商每日起价
 * @author: qiu
 * @create: 2023-09-26 19:57
 */
@Data
public class SupplyHotelLowestPriceReqDelDTO {

    /** 最低售价 */
    private BigDecimal lowestPrice;

    /** 日期 */
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate saleDate;

    /** 酒店id */
    private String hotelId;

    /** 城市编码 */
    private String cityCode;

    /** 供应类型 */
    private String supplyClass;

    /** 供应商编码 */
    private String supplyCode;

    /** 商家来源 */
    @NotEmpty(message = "商家来源不能为空")
    private String merchantSource;

    /** 商家编码 */
    private String merchantCode;

    /** 类型：1-国内 2-海外 ["国内","海外"] */
    @NotEmpty(message = "类型：1-国内 2-海外 [’国内‘,’海外‘]不能为空")
    private String supplyType;


}