package com.fangcang.hotel.amadeusosbld.dto.response.order.addmultielements;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/7/9 15:54
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "errorOrWarningCodeDetails")
public class ErrorOrWarningCodeDetails {

    @XmlElement(name = "errorDetails")
    private ErrorDetails errorDetails;

    public ErrorDetails getErrorDetails() {
        return errorDetails;
    }

    public void setErrorDetails(ErrorDetails errorDetails) {
        this.errorDetails = errorDetails;
    }
}
