package com.fangcang.hotel.tmchubosbld.dto.response.order;

import com.fangcang.hotel.tmchubosbld.dto.entity.BusinessResponse;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CheckBookingInfoResponse extends BusinessResponse {

    /**
     * 价格计划id
     */
    private String ratePlanId;

    /**
     * 是否可订
     */
    private Integer canBook;

    /**
     * 价格信息
     */
    private List<OrderPriceItem> priceItems;

    /**
     * 到店另付费用；针对海外酒店有效
     */
    private BigDecimal payAtHotelFee;

    /**
     * 到店另付费用币种；针对海外酒店有效
     */
    private String payAtHotelFeeCurrency;

    /**
     * 每日价格均价总价
     */
    private BigDecimal totalSalePrice;
}
