package com.fangcang.hotel.bookingosbld.dto.response.price;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/18 18:23
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "RatePlan")
public class RatePlan {

    @XmlAttribute(name = "RatePlanCode")
    private String ratePlanCode;

    @XmlAttribute(name = "RateIndicator")
    private String rateIndicator;

    @XmlAttribute(name = "AvailabilityStatus")
    private String availabilityStatus;

    @XmlAttribute(name = "PrepaidIndicator")
    private String prepaidIndicator;

    @XmlAttribute(name = "RatePlanName")
    private String ratePlanName;

    @XmlElement(name = "Guarantee")
    private List<Guarantee> guaranteeList;

    @XmlElement(name = "CancelPenalties")
    private CancelPenalties cancelPenalties;

    @XmlElement(name = "Commission")
    private Commission commission;

    @XmlElement(name = "RatePlanDescription")
    private RatePlanDescription ratePlanDescription;

    @XmlElement(name = "MealsIncluded")
    private MealsIncluded mealsIncluded;

    @XmlElementWrapper(name = "AdditionalDetails")
    @XmlElement(name = "AdditionalDetail")
    private List<AdditionalDetail> additionalDetailList;

    public String getRatePlanCode() {
        return ratePlanCode;
    }

    public void setRatePlanCode(String ratePlanCode) {
        this.ratePlanCode = ratePlanCode;
    }

    public String getRateIndicator() {
        return rateIndicator;
    }

    public void setRateIndicator(String rateIndicator) {
        this.rateIndicator = rateIndicator;
    }

    public String getAvailabilityStatus() {
        return availabilityStatus;
    }

    public void setAvailabilityStatus(String availabilityStatus) {
        this.availabilityStatus = availabilityStatus;
    }

    public String getPrepaidIndicator() {
        return prepaidIndicator;
    }

    public void setPrepaidIndicator(String prepaidIndicator) {
        this.prepaidIndicator = prepaidIndicator;
    }

    public String getRatePlanName() {
        return ratePlanName;
    }

    public void setRatePlanName(String ratePlanName) {
        this.ratePlanName = ratePlanName;
    }

    public List<Guarantee> getGuaranteeList() {
        return guaranteeList;
    }

    public void setGuaranteeList(List<Guarantee> guaranteeList) {
        this.guaranteeList = guaranteeList;
    }

    public CancelPenalties getCancelPenalties() {
        return cancelPenalties;
    }

    public void setCancelPenalties(CancelPenalties cancelPenalties) {
        this.cancelPenalties = cancelPenalties;
    }

    public Commission getCommission() {
        return commission;
    }

    public void setCommission(Commission commission) {
        this.commission = commission;
    }

    public RatePlanDescription getRatePlanDescription() {
        return ratePlanDescription;
    }

    public void setRatePlanDescription(RatePlanDescription ratePlanDescription) {
        this.ratePlanDescription = ratePlanDescription;
    }

    public MealsIncluded getMealsIncluded() {
        return mealsIncluded;
    }

    public void setMealsIncluded(MealsIncluded mealsIncluded) {
        this.mealsIncluded = mealsIncluded;
    }

    public List<AdditionalDetail> getAdditionalDetailList() {
        return additionalDetailList;
    }

    public void setAdditionalDetailList(List<AdditionalDetail> additionalDetailList) {
        this.additionalDetailList = additionalDetailList;
    }
}
