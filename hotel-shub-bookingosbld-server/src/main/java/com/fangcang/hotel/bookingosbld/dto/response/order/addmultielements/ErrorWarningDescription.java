package com.fangcang.hotel.bookingosbld.dto.response.order.addmultielements;


import com.fangcang.hotel.bookingosbld.dto.response.order.completereservationdetails.FreeTextDetails;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/7/9 15:57
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "errorWarningDescription")
public class ErrorWarningDescription {

    @XmlElement(name = "freeTextDetails")
    private FreeTextDetails freeTextDetails;

    @XmlElement(name = "freeText")
    private String freeText;

    public FreeTextDetails getFreeTextDetails() {
        return freeTextDetails;
    }

    public void setFreeTextDetails(FreeTextDetails freeTextDetails) {
        this.freeTextDetails = freeTextDetails;
    }

    public String getFreeText() {
        return freeText;
    }

    public void setFreeText(String freeText) {
        this.freeText = freeText;
    }
}
