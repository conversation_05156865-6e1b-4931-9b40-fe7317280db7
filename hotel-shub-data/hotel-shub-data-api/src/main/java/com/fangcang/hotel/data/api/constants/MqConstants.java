package com.fangcang.hotel.data.api.constants;

/**
 * @program: glink_shub
 * @ClassName MqConstants
 * @description:
 * @author: 湫
 * @create: 2025/04/02/ 14:02
 * @Version 1.0
 **/

public class MqConstants {

    /**
     * shub 基础路由
     */
    private static final String BASE_TOPIC = "shub_";

    /**
     * 酒店起价路由
     */
    public static final String HOTELLOWESTPRICETOPIC = BASE_TOPIC + "hotel_lowest_price";


    /**
     * amadeusosbld 缓存产品路由
     */
    public static final String AMADEUSOSBLD_SUPPLY_PRODUCT_CACHE = BASE_TOPIC + "supply_product_cache_amadeusosbld";
    /**
     * 新增酒店起价tag
     */
    public static final String ADDHOTELLOWESTPRICETAG = "add";

    /**
     * 删除酒店起价tag
     */
    public static final String DELETEHOTELLOWESTPRICETAG = "delete";

    /**
     * 酒店起价group
     */
    public static final String HOTELLOWESTPRICEGROUP = "HotelLowestPriceConsumerGroup";


    /**
     * 每分钟tag
     */
    public static final String AMADEUSOSBLDSUPPLYPRODUCTMINUTETAG = "minute";

    /**
     * 每天tag
     */
    public static final String AMADEUSOSBLDSUPPLYPRODUCTDAYTAG = "day";


    /**
     * amadeusosbld 缓存产品group 每天
     */
    public static final String AMADEUSOSBLDDAYSUPPLYPRODUCTCONSUMERGROUP = "AmadeusosbldDaySupplyProductConsumerGroup";

    /**
     * amadeusosbld 缓存产品group 分钟
     */
    public static final String AMADEUSOSBLDMINUTESUPPLYPRODUCTCONSUMERGROUP = "AmadeusosbldMinuteSupplyProductConsumerGroup";

    /**
     * es 日志路由
     */
    public static final String SUPPLY_ES_LOG_TOPIC =BASE_TOPIC+ "es_log";

    /**
     * es 日志group
     */
    public static final String SUPPLY_ES_LOG_GROUP = "SupplyEsLogConsumerGroup";
}
