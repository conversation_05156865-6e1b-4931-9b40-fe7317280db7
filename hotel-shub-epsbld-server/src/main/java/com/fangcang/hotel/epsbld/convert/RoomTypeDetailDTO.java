package com.fangcang.hotel.epsbld.convert;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class RoomTypeDetailDTO {

    /**
     * 供应商酒店ID
     */
    private String spHotelId;

    /**
     * 供应商的房型ID
     */
    private String spRoomId;

    /**
     * fc的房型ID
     */
    private String fcRoomId;

    /**
     * cf酒店ID
     */
    private String fcHotelId;

    /**
     * 供应商的房型名称
     */
    private String spRoomName;

    /**
     * 产品（房型）售价信息
     */
    private List<SalePriceInfo> salePriceInfoList;


    @Data
    public static class SalePriceInfo {

        /**
         * 早餐类型（名称）
         */
        private String breakfast;

        /**
         * 早餐数
         */
        private Integer breakfastNum;

        /**
         * 早餐类型（默认自助早）
         */
        private Integer breakfastType = 3;

        /**
         * 配额（房间数）
         */
        protected int quotaNum;

        /**
         * 销售价格计划id
         */
        private String salePricePlanId;

        /**
         * 销售价格计划名称
         */
        private String salePricePlanName;

        /**
         * 供应商价格计划ID
         */
        private String supplyPricePlanId;

        /**
         * 总价
         */
        private BigDecimal totalPriceAmount;

        /**
         * 每个房间的附加费用的总金额
         */
        private BigDecimal totalSurchargesAmount;

        /**
         * 到店支付总金额
         */
        private BigDecimal totalHotelFee;

        /**
         * 每个房间的价格信息
         */
        private List<RoomPriceInfo> roomPriceInfoList;

        /**
         * 取消条款时间(yyyy-mm-dd)
         */
        private String cancelClauseDate;

        /**
         * 取消条款
         */
        private String cancelTerm = "一经预订，不可取消和修改。";

        /**
         * 供应商取消条款，是否可退
         */
        private boolean nonRefundable;

        /**
         * 床型名称
         */
        private String bedType;

        /**
         * 试预定地址，以及包含 token
         */
        private String token;

        /**
         * 床型关系:
         * 1表示 床型A与床型B 同时存在
         * 0表示 或者是床型A 或者是床型B
         */
        private Integer withOr;

        /**
         * 床型信息
         */
        private List<BedInfo> bedInfoList;
    }

    @Data
    public static class BedInfo {
        private String bedName; //床型名称
        private Integer bedSize; //数量
        private String bedCode; //床型编码
        private Integer bedNum; //床数
    }

    @Data
    public static class RoomPriceInfo implements Serializable {

        /**
         * 房间的数量
         */
        private int numRooms;

        /**
         * (附加费用的总金额 +  房价的基础 总金额 ) = (房价总金额 * 房间的数量) =  房价总金额
         * 房价总金额
         */
        private BigDecimal totalRoomPriceAmount;

        /**
         * 房价的基础 总金额
         */
        private BigDecimal totalBaseRoomPriceAmount;

        /**
         * 币种
         */
        protected String currency;

        /**
         * 每天价格信息 (包含了 附加费用信息)
         */
        private List<DayPriceInfo> dayPriceInfoList;

        /**
         * 附加费用的总金额 这个节点是对 dayPriceInfoList 里面 feeAmount 以及 propertyFeeInfoList propertyFeeAmount 总和
         */
        private BigDecimal totalSurchargesAmount;

        /**
         * 物业费用信息
         */
        private List<PropertyFeeInfo> propertyFeeInfoList;

        /**
         * 到店支付总金额
         */
        private BigDecimal totalHotelFee;

        /**
         * 到店支付费用信息
         */
        private List<HotelFeeInfo> hotelFeeInfoList;
    }

    @Data
    public static class PropertyFeeInfo implements Serializable {

        /**
         * 费用类型名称
         */
        private String propertyFeeType;

        /**
         * 费用金额
         */
        private BigDecimal propertyFeeAmount;
    }

    @Data
    public static class HotelFeeInfo implements Serializable {

        /**
         * 费用描述
         */
        private String description;

        /**
         * 费用金额
         */
        private BigDecimal amount;

        /**
         * 币种
         */
        private String currency;
    }

    @Data
    public static class DayPriceInfo implements Serializable {

        /**
         * 售价金额
         */
        private BigDecimal basePrice;

        /**
         * 售价日期
         */
        private String saleDate;

        /**
         * 每日单间返佣
         */
        private BigDecimal profitOnline;

        /**
         * 费用类型名称
         */
        private String feeType;

        /**
         * 费用金额
         */
        private BigDecimal feeAmount;
    }
}