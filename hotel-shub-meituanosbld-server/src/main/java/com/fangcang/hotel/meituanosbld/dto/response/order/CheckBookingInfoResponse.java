package com.fangcang.hotel.meituanosbld.dto.response.order;

import com.fangcang.hotel.meituanosbld.dto.response.hotel.CpApply;
import com.fangcang.hotel.meituanosbld.dto.response.hotel.MealType;
import com.fangcang.hotel.meituanosbld.dto.response.hotel.OtaBeds;
import com.fangcang.hotel.meituanosbld.dto.response.hotel.PriceModelList;
import lombok.Data;

import java.util.List;

@Data
public class CheckBookingInfoResponse{


    /**
     * 产品价格日历
     */
    private List<PriceModelList> priceModelList;
    /**
     * 产品入住偏好
     */
    private List<PreferenceGroupList> preferenceGroupList;
    /**
     * 产品名称
     */
    private String goodsName;
    /**
     * 床型，外层数组是或的关系，内层是与的关系
     */
    private List<List<OtaBeds>> otaBeds;
    /**
     * 餐食信息
     */
    private MealType mealType;
    /**
     * 物理房型ID
     */
    private Integer realRoomId;
    /**
     * 是否可取消。 [1]:不可取消 [2]:限时取消
     */
    private Integer refundable;
    /**
     * 取消政策 ，refundable=2时该字段不为空
     */
    private List<CpApply> cpApply;
    /**
     * 产品特殊说明，包括入住提示、收费服务、国籍限制等，建议展示给客人，部分包含html样式标签
     */
    private String checkPolicy;
    /**
     * 产品适用人群说明
     */
    private OhTargetUser ohTargetUser;

    /**
     *
     */
    private Boolean immediateConfirm;

    /**
     * 产品是否立即确认 1 立即确认 2 非立即确认
     */
    private Integer confirmType;


}
