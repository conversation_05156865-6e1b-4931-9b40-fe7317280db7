package com.fangcang.hotel.tmchubosbld.dto.entity;

import com.fangcang.hotel.core.enums.GlobalErrorCodeEnum;
import com.fangcang.hotel.framework.common.exception.BizException;
import com.fangcang.hotel.framework.common.exception.ErrorCode;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ResponseResult<T> {

    /** 返回编码 */
    private String returnCode;

    /** 返回信息 */
    private String returnMsg;

    /** 业务响应参数 */
    private T bussinessResponse;

    public static <T> ResponseResult<T> error(ResponseResult<?> result) {
        return error(result.getReturnCode(), result.getReturnMsg());
    }

    public static <T> ResponseResult<T> error(String code, String message) {
        ResponseResult<T> result = new ResponseResult<T>();
        result.returnCode = code;
        result.returnMsg = message;
        return result;
    }

    public static <T> ResponseResult<T> error(ErrorCode errorCode) {
        return error(errorCode.getCode(), errorCode.getMsg());
    }

    public static <T> ResponseResult<T> error(GlobalErrorCodeEnum codeEnum) {
        return error(codeEnum.getCode(), codeEnum.getMsg());
    }

    public static <T> ResponseResult<T> error(GlobalErrorCodeEnum codeEnum,String msg) {
        return error(codeEnum.getCode(), msg);
    }


    public static <T> ResponseResult<T> error(BizException bIzException) {
        return error(bIzException.getCode(), bIzException.getMessage());
    }

    @SuppressWarnings("unchecked")
    public static <T> ResponseResult<T> success(T bussinessResponse) {
        ResponseResult<T> result = new ResponseResult<T>();
        result.returnCode = GlobalErrorCodeEnum.SUCCESS.getCode();
        result.returnMsg = GlobalErrorCodeEnum.SUCCESS.getMsg();
        result.bussinessResponse = bussinessResponse;

        return result;
    }

    public static <T> ResponseResult<T> successList(T bussinessResponse) {
        ResponseResult<T> result = new ResponseResult<T>();
        result.returnCode = GlobalErrorCodeEnum.SUCCESS.getCode();
        result.returnMsg = GlobalErrorCodeEnum.SUCCESS.getMsg();
        result.bussinessResponse = bussinessResponse;
        return result;
    }

    public static ResponseResult<Object> success() {
        return success(1);
    }


    @JsonIgnore // 避免 jackson 序列化
    public boolean isSuccess() {
        return isSuccess(returnCode);
    }

    @JsonIgnore // 避免 jackson 序列化
    public boolean isError() {
        return !isSuccess();
    }

    public static boolean isSuccess(String returnCode) {
        return "000".equals(returnCode) || "SUCCESS".equals(returnCode);
    }
}
