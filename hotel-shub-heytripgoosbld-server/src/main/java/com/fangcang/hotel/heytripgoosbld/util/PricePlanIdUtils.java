package com.fangcang.hotel.heytripgoosbld.util;

import cn.hutool.core.util.StrUtil;
import com.fangcang.hotel.core.util.StringUtilExtend;

/**
 * <AUTHOR>
 * @Date 2024/12/02 17:56
 * @Description:
 */
public class PricePlanIdUtils {

    private final static String SPLIT = "_";

    /**
     * 生成价格计划ID
     *
     * @param supplyCode
     * @param spHotelId
     * @param spRoomId
     * @param pricePlanId
     * @return
     */
    public static String pricePlanIdPlus(String supplyCode, String spHotelId, String spRoomId, String pricePlanId) {
        StringBuffer sb = new StringBuffer();
        return sb.append(supplyCode)
                .append(SPLIT).append(spHotelId)
                .append(SPLIT).append(spRoomId)
                .append(SPLIT).append(pricePlanId)
                .toString();
    }

    /**
     * 获取spHotelId
     *
     * @param pricePlanIdPlus
     * @return
     */
    public static String getSpHotelId(String pricePlanIdPlus) {
        if (pricePlanIdPlus.contains(SPLIT)) {
            String[] pricePlanIdPlusArr = pricePlanIdPlus.split(SPLIT);
            if (pricePlanIdPlusArr.length >= 2) {
                return pricePlanIdPlusArr[1];
            }
        }
        return null;
    }

    /**
     * 获取spRoomId
     *
     * @param pricePlanIdPlus
     * @return
     */
    public static String getSpRoomId(String pricePlanIdPlus) {
        if (pricePlanIdPlus.contains(SPLIT)) {
            String[] pricePlanIdPlusArr = pricePlanIdPlus.split(SPLIT);
            if (pricePlanIdPlusArr.length >= 3) {
                return pricePlanIdPlusArr[2];
            }
        }
        return null;
    }

    /**
     * 获取pricePlanId
     *
     * @param pricePlanIdPlus
     * @return
     */
    public static String getPricePlanId(String pricePlanIdPlus) {
        if (pricePlanIdPlus.contains(SPLIT)) {
            String[] pricePlanIdPlusArr = pricePlanIdPlus.split(SPLIT);
            if (pricePlanIdPlusArr.length >= 4) {
                StringBuilder lastPart = new StringBuilder();
                for (int i = 3; i < pricePlanIdPlusArr.length; i++) {
                    if (i > 3) {
                        lastPart.append("_");
                    }
                    lastPart.append(pricePlanIdPlusArr[i]);
                }
                return lastPart.toString();
            }
        }
        return null;
    }

    /**
     * 切割价格计划id
     *
     * @param pricePlanIdPlus
     * @return
     */
    public static String[] getRateIdArr(String pricePlanIdPlus) {
        if (StrUtil.isBlank(pricePlanIdPlus)) {
            return null;
        }
        if (pricePlanIdPlus.contains(SPLIT)) {
            String[] pricePlanIdPlusArr = pricePlanIdPlus.split(SPLIT);
            if (pricePlanIdPlusArr.length >= 4) {
                String[] result = new String[4];
                result[0] = pricePlanIdPlusArr[0];
                result[1] = pricePlanIdPlusArr[1];
                result[2] = pricePlanIdPlusArr[2];

                StringBuilder lastPart = new StringBuilder();
                for (int i = 3; i < pricePlanIdPlusArr.length; i++) {
                    if (i > 3) {
                        lastPart.append("_");
                    }
                    lastPart.append(pricePlanIdPlusArr[i]);
                }
                result[3] = lastPart.toString();
                return result;
            } else {
                String[] result = new String[pricePlanIdPlusArr.length];
                for (int i = 0; i < pricePlanIdPlusArr.length; i++) {
                    result[i] = pricePlanIdPlusArr[i];
                }
                return result;
            }
        }
        return new String[]{pricePlanIdPlus};
    }

}
