package com.fangcang.hotel.amadeusosbld.dto.response.hotel;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/22 12:36
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "ContactInfo")
public class ContactInfoRs {

    @XmlAttribute(name = "Location")
    private String location;

    @XmlElementWrapper(name = "Phones")
    @XmlElement(name = "Phone")
    private List<Phone> phoneList;

    @XmlElementWrapper(name = "Addresses")
    @XmlElement(name = "Address")
    private List<Address> addressList;

    @XmlElementWrapper(name = "Names")
    @XmlElement(name = "Name")
    private List<Name> nameList;

    @XmlElementWrapper(name = "Emails")
    @XmlElement(name = "Email")
    private List<Email> emailList;

    @XmlElementWrapper(name = "URLs")
    @XmlElement(name = "URL")
    private List<URL> urlList;

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public List<Phone> getPhoneList() {
        return phoneList;
    }

    public void setPhoneList(List<Phone> phoneList) {
        this.phoneList = phoneList;
    }

    public List<Address> getAddressList() {
        return addressList;
    }

    public void setAddressList(List<Address> addressList) {
        this.addressList = addressList;
    }

    public List<Name> getNameList() {
        return nameList;
    }

    public void setNameList(List<Name> nameList) {
        this.nameList = nameList;
    }

    public List<Email> getEmailList() {
        return emailList;
    }

    public void setEmailList(List<Email> emailList) {
        this.emailList = emailList;
    }

    public List<URL> getUrlList() {
        return urlList;
    }

    public void setUrlList(List<URL> urlList) {
        this.urlList = urlList;
    }
}
