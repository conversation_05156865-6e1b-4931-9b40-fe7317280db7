package com.fangcang.hotel.meituanosbld.dto.response.order;

import lombok.Data;

/**
 * <AUTHOR>
 * @time 2024/8/30
 */
@Data
public class BaseInfo {

    /**
     * 美团订单ID
     */
    private Long mtOrderId;
    /**
     * 预约下单时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String createTime;
    /**
     * 订单状态： 10 创建订单 20 待支付 21 支付成功 22 支付失败 30 预订中 31 预订成功 32 预订失败 40 取消中 41 取消成功 42 取消失败 50 用户已入住 60 已消费退款
     */
    private Integer orderStatus;
    /**
     * 订单是否可取消 true 订单可取消 false 订单不可取消
     */
    private Boolean isCancel;
    /**
     * 酒店确认号
     */
    private String confirmationNumber;

}
