package com.fangcang.hotel.rjbld.manger.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fangcang.hotel.core.enums.GlobalErrorCodeEnum;
import com.fangcang.hotel.data.api.dto.HttpResponse;
import com.fangcang.hotel.data.api.enums.SupplyClassEnum;
import com.fangcang.hotel.data.base.constants.LogIndexSuffixConstants;
import com.fangcang.hotel.data.base.util.HttpClientUtil;
import com.fangcang.hotel.data.base.util.LogIndexUtil;
import com.fangcang.hotel.data.base.util.StringUtil;
import com.fangcang.hotel.framework.common.exception.BizException;
import com.fangcang.hotel.framework.common.util.StrUtilX;
import com.fangcang.hotel.framework.operatelog.auto.SlsLogger;
import com.fangcang.hotel.framework.operatelog.core.sls.SlsEnum;
import com.fangcang.hotel.rjbld.config.RJBLDConfig;
import com.fangcang.hotel.rjbld.config.RJBLDExtendConfig;
import com.fangcang.hotel.rjbld.constants.RJBLDConstants;
import com.fangcang.hotel.rjbld.dto.request.*;
import com.fangcang.hotel.rjbld.dto.response.BaseResponse;
import com.fangcang.hotel.rjbld.dto.response.hotel.*;
import com.fangcang.hotel.rjbld.dto.response.order.*;
import com.fangcang.hotel.rjbld.dto.response.price.QueryRoomPriceResponse;
import com.fangcang.hotel.rjbld.manger.RJBLDManager;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;


/**
 * 供应商接口实现类
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:15
 */
@Service("rjbldManager")
@Slf4j
public class RJBLDManagerImpl implements RJBLDManager {

    @Autowired
    private SlsLogger slsLogger;


    @Autowired
    private RJBLDConfig rjbldConfig;


    /**
     * 查询酒店列表
     *
     * @param request      请求参数
     * @param extendConfig 配置信息
     * @return
     */
    @Override
    public GETChannelRmtypeResp queryHotelList(GETChannelRmtypeReq request, RJBLDExtendConfig extendConfig) {
        GETChannelRmtypeResp baseResponse = null;
        try {
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询酒店列表接口请求报文,request{}" , JSON.toJSONString(request));
            }

            String resp = commonRequest(request, extendConfig, RJBLDConstants.QUERY_HOTEL_AND_ROOM_LIST);
            baseResponse = JSON.parseObject(resp, new TypeReference<GETChannelRmtypeResp>() {
            });
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询酒店列表接口响应报文,baseResponse{}" , JSON.toJSONString(baseResponse));
            }

        } catch (Exception e) {
            log.error("调用如家不落地查询酒店列表接口失败，系统异常" , e);
            slsLogger.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), "查询酒店列表");
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }

        if (checkResponseResult(baseResponse)) {
            return baseResponse;
        } else {
            log.error("调用如家不落地查询酒店列表接口失败，返回格式不正确，baseResponse{}" , JSON.toJSONString(baseResponse));
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getMsg(), "查询酒店列表",JSON.toJSONString(baseResponse));
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, msg);
        }
    }


    /**
     * 查询酒店详情
     *
     * @param request      请求参数
     * @param extendConfig 配置信息
     * @return
     */
    @Override
    public QueryHotelDetailResponse queryHotelDetail(QueryHotelDetailRequest request, RJBLDExtendConfig extendConfig) {
        QueryHotelDetailResponse baseResponse = null;
        try {
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询酒店详情接口请求报文,request{}" , JSON.toJSONString(request));
            }

            String resp = commonRequest(request, extendConfig, RJBLDConstants.QUERY_HOTEL_DETAIL);
            baseResponse = JSON.parseObject(resp, new TypeReference<QueryHotelDetailResponse>() {
            });
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询酒店详情接口响应报文,baseResponse{}" , JSON.toJSONString(baseResponse));
            }

        } catch (Exception e) {
            log.error("调用如家不落地查询酒店详情接口失败，系统异常" , e);
            slsLogger.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), "查询酒店详情");
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }

        if (checkResponseResult(baseResponse)) {
            return baseResponse;
        } else {
            log.error("调用如家不落地查询酒店详情接口失败，返回格式不正确，baseResponse{}" , JSON.toJSONString(baseResponse));
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getMsg(), "查询酒店详情",JSON.toJSONString(baseResponse));
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, msg);
        }
    }

    /**
     * 查询房型价格详情
     *
     * @param request      请求参数
     * @param extendConfig 配置信息
     * @return
     */
    @Override
    public QueryRoomPriceResponse queryRoomPriceList(QueryRoomPriceRequest request, RJBLDExtendConfig extendConfig) {
        QueryRoomPriceResponse baseResponse = null;
        try {
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询房型价格详情接口请求报文,request{}" , JSON.toJSONString(request));
            }
            String resp = commonRequest(request, extendConfig, RJBLDConstants.QUERY_ROOM_PRICE_LIST);
            baseResponse = JSON.parseObject(resp, new TypeReference<QueryRoomPriceResponse>() {
            });
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询房型价格详情接口响应报文,baseResponse{}" , JSON.toJSONString(baseResponse));
            }
        } catch (Exception e) {
            log.error("调用如家不落地查询房型价格详情接口失败，系统异常" , e);
            slsLogger.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), "查询房型价格详情");
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }

        if (checkResponseResult(baseResponse)) {
            return baseResponse;
        } else {
            log.error("调用如家不落地查询房型价格详情接口失败，返回格式不正确，baseResponse{}" , JSON.toJSONString(baseResponse));
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getMsg(), "查询房型价格详情",JSON.toJSONString(baseResponse));
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, msg);
        }
    }

    @Override
    public GetHotelRmMCResponse queryRoomTypeDetail(GetHotelRmMCRequest request, RJBLDExtendConfig extendConfig) {
        GetHotelRmMCResponse baseResponse = null;
        try {
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询房型详情接口请求报文,request{}" , JSON.toJSONString(request));
            }
            String resp = commonRequest(request, extendConfig, RJBLDConstants.QUERY_ROOM_TYPE_DETAIL);
            baseResponse = JSON.parseObject(resp, new TypeReference<GetHotelRmMCResponse>() {
            });
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询房型详情接口响应报文,baseResponse{}" , JSON.toJSONString(baseResponse));
            }
        } catch (Exception e) {
            log.error("调用如家不落地查询房型详情接口失败，系统异常" , e);
            slsLogger.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), "查询房型详情");
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }

        if (checkResponseResult(baseResponse)) {
            return baseResponse;
        } else {
            log.error("调用如家不落地查询房型详情接口失败，返回格式不正确，baseResponse{}" , JSON.toJSONString(baseResponse));
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getMsg(), "查询房型详情",JSON.toJSONString(baseResponse));
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, msg);
        }
    }


    /**
     * 试预定订单
     *
     * @param request      请求参数
     * @param extendConfig 配置信息
     * @return
     */
    @Override
    public CheckAvailabilityResponse preBookingOrder(CheckAvailabilityRequest request, RJBLDExtendConfig extendConfig) {
        CheckAvailabilityResponse baseResponse = null;
        try {
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地试预定订单接口请求报文,request{}" , JSON.toJSONString(request));
            }
            String resp = commonRequest(request, extendConfig, RJBLDConstants.PREBOOKING);
            baseResponse = JSON.parseObject(resp, new TypeReference<CheckAvailabilityResponse>() {
            });
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地试预定订单接口响应报文,baseResponse{}" , JSON.toJSONString(baseResponse));
            }
        } catch (Exception e) {
            log.error("调用如家不落地试预定订单接口失败，系统异常" , e);
            slsLogger.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), "试预定订单");
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }

        if (checkResponseResult(baseResponse)) {
            return baseResponse;
        } else {
            log.error("调用如家不落地试预定订单接口失败，返回格式不正确，baseResponse{}" , JSON.toJSONString(baseResponse));
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getMsg(), "试预定订单",JSON.toJSONString(baseResponse));
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, msg);
        }
    }


    /**
     * 创建订单
     *
     * @param request      请求参数
     * @param extendConfig 配置信息
     * @return
     */
    @Override
    public CreateOrderResponse createOrder(CreateOrderRequest request, RJBLDExtendConfig extendConfig) {
        CreateOrderResponse baseResponse = null;
        try {
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地创建订单接口请求报文,request{}" , JSON.toJSONString(request));
            }
            String resp = commonRequest(request, extendConfig, RJBLDConstants.CREATE_ORDER);
            baseResponse = JSON.parseObject(resp, new TypeReference<CreateOrderResponse>() {
            });
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地创建订单接口响应报文,baseResponse{}" , JSON.toJSONString(baseResponse));
            }
        } catch (Exception e) {
            log.error("调用如家不落地创建订单接口失败，系统异常" , e);
            slsLogger.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), "创建订单");
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }

        if (checkResponseResult(baseResponse)) {
            return baseResponse;
        } else {
            log.error("调用如家不落地创建订单接口失败，返回格式不正确，baseResponse{}" , JSON.toJSONString(baseResponse));
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getMsg(), "创建订单",JSON.toJSONString(baseResponse));
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, msg);
        }
    }


    /**
     * 取消订单
     *
     * @param request      请求参数
     * @param extendConfig 配置信息
     * @return
     */
    @Override
    public BaseResponse cancelOrder(CancelOrderRequest request, RJBLDExtendConfig extendConfig) {
        BaseResponse baseResponse = null;
        try {
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地取消订单接口请求报文,request{}" , JSON.toJSONString(request));
            }
            String resp = commonRequest(request, extendConfig, RJBLDConstants.CANCEL_ORDER);
            baseResponse = JSON.parseObject(resp, new TypeReference<BaseResponse>() {
            });
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地取消订单接口响应报文,baseResponse{}" , JSON.toJSONString(baseResponse));
            }
        } catch (Exception e) {
            log.error("调用如家不落地取消订单接口失败，系统异常" , e);
            slsLogger.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), "取消订单");
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }

        if (checkResponseResult(baseResponse)) {
            return baseResponse;
        } else {
            log.error("调用如家不落地取消订单接口失败，返回格式不正确，baseResponse{}" , JSON.toJSONString(baseResponse));
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getMsg(), "取消订单",JSON.toJSONString(baseResponse));
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, msg);
        }
    }

    @Override
    public QueryOrderStatusResp queryOrderStatus(QueryOrderStatusReq request, RJBLDExtendConfig extendConfig) {
        QueryOrderStatusResp baseResponse = null;
        try {
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询订单状态接口请求报文,request{}" , JSON.toJSONString(request));
            }
            String resp = commonRequest(request, extendConfig, RJBLDConstants.QUERY_ORDER_STATUS);
            baseResponse = JSON.parseObject(resp, new TypeReference<QueryOrderStatusResp>() {
            });
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询订单状态接口响应报文,baseResponse{}" , JSON.toJSONString(baseResponse));
            }
        } catch (Exception e) {
            log.error("调用如家不落地查询订单状态接口失败，系统异常" , e);
            slsLogger.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), "查询订单状态");
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }

        if (checkResponseResult(baseResponse)) {
            return baseResponse;
        } else {
            log.error("调用如家不落地查询订单状态接口失败，返回格式不正确，baseResponse{}" , JSON.toJSONString(baseResponse));
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getMsg(), "查询订单状态",JSON.toJSONString(baseResponse));
            log.error(msg);
            return baseResponse;
            //throw new BizException(ErrorCodeConstants.SUPPLY_RETURN_DATA_FORMAT_ERROR.getCode(), msg);
        }
    }


    /**
     * 查询订单详情
     *
     * @param request      请求参数
     * @param extendConfig 配置信息
     * @return
     */
    @Override
    public QueryOrderResponse queryOrder(QueryOrderRequest request, RJBLDExtendConfig extendConfig) {
        QueryOrderResponse queryOrderResponse = null;
        try {
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询订单详情接口请求报文,request{}" , JSON.toJSONString(request));
            }
            String resp = commonRequest(request, extendConfig, RJBLDConstants.QUERY_ORDER);
            queryOrderResponse = JSON.parseObject(resp, new TypeReference<QueryOrderResponse>() {
            });
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询订单详情接口响应报文,baseResponse{}" , JSON.toJSONString(queryOrderResponse));
            }
        } catch (Exception e) {
            log.error("调用如家不落地查询订单详情接口失败，系统异常" , e);
            slsLogger.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), "查询订单详情");
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }

        if (checkResponseResult(queryOrderResponse)) {
            return queryOrderResponse;
        } else {
            log.error("调用如家不落地查询订单详情接口失败，返回格式不正确，baseResponse{}" , JSON.toJSONString(queryOrderResponse));
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getMsg(), "查询订单详情",JSON.toJSONString(queryOrderResponse));
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, msg);
        }
    }

    @Override
    public HotelFacilitiesListDataResp queryHotelFacilitiesList(HotelFacilitiesListDataReq request, RJBLDExtendConfig extendConfig) {
        HotelFacilitiesListDataResp hotelFacilitiesListDataResp = null;
        try {
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询酒店设施详情接口请求报文,request{}" , JSON.toJSONString(request));
            }
            String resp = commonRequest(request, extendConfig, RJBLDConstants.QUERY_HOTEL_FACILITIES_LIST);
            hotelFacilitiesListDataResp = JSON.parseObject(resp, new TypeReference<HotelFacilitiesListDataResp>() {
            });
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询酒店设施详情接口响应报文,baseResponse{}" , JSON.toJSONString(hotelFacilitiesListDataResp));
            }
        } catch (Exception e) {
            log.error("调用如家不落地查询酒店设施详情接口失败，系统异常" , e);
            slsLogger.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), "查询酒店设施详情");
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }

        if (checkResponseResult(hotelFacilitiesListDataResp)) {
            return hotelFacilitiesListDataResp;
        } else {
            log.error("调用如家不落地查询酒店设施详情接口失败，返回格式不正确，baseResponse{}" , JSON.toJSONString(hotelFacilitiesListDataResp));
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getMsg(), "查询酒店设施详情",JSON.toJSONString(hotelFacilitiesListDataResp));
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, msg);
        }
    }

    @Override
    public CityListDataResp queryCityInfoList(BaseRequest baseRequest, RJBLDExtendConfig extendConfig) {
        CityListDataResp cityListDataResp = null;
        try {
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询城市列表接口请求报文,request{}" , JSON.toJSONString(baseRequest));
            }
            String resp = commonRequest(baseRequest, extendConfig, RJBLDConstants.QUERY_CITY_INFO_LIST);
            cityListDataResp = JSON.parseObject(resp, new TypeReference<CityListDataResp>() {
            });
            if (rjbldConfig.getIsDebug() == 1){
                log.info("调用如家不落地查询城市列表接口响应报文,baseResponse{}" , JSON.toJSONString(cityListDataResp));
            }
        } catch (Exception e) {
            log.error("调用如家不落地查询城市列表接口失败，系统异常" , e);
            slsLogger.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), "查询城市列表");
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }

        if (checkResponseResult(cityListDataResp)) {
            return cityListDataResp;
        } else {
            log.error("调用如家不落地查询城市列表接口失败，返回格式不正确，baseResponse{}" , JSON.toJSONString(cityListDataResp));
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR.getMsg(), "查询城市列表",JSON.toJSONString(cityListDataResp));
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_RETURN_DATA_FORMAT_ERROR, msg);
        }
    }


    /**
     * 验证响应结果
     *
     * @param baseResponse
     * @return
     */
    public boolean checkResponseResult(BaseResponse baseResponse) {
        if (baseResponse == null) {
            return false;
        }
        // 判断响应结果 (需自己处理, 根据供应商返回编码)
        if (!baseResponse.getResCode().equals(RJBLDConstants.SUCCESS)) {
            return false;
        }

        //判断是否为字符串
        String resultStr = JSON.toJSONString(baseResponse);
        return StrUtilX.isNotEmpty(resultStr);
    }


    /**
     * 供应商接口请求公共实现方法
     *
     * @param request      请求参数
     * @param extendConfig 配置
     * @param api          接口地址或者方法名
     * @return
     */
    public String commonRequest(BaseRequest request, RJBLDExtendConfig extendConfig, String api) {
        HttpResponse httpResponse = null;
        String resp = null;
        try {
            // 接口权限验证公共参数
            request.setLicense(extendConfig.getLicense());
            request.setOprId(extendConfig.getOprId());

            // 连接超时重试次数
            int retrySize = rjbldConfig.getConnectTimeOutRetrySize();

            //发单需要单独配置超时时间
            Integer connectTimeOut = rjbldConfig.getConnectTimeOut();
            Integer socketTimeOut = rjbldConfig.getSocketTimeOut();
            if (StringUtil.isValidString(api) && api.equals(RJBLDConstants.CREATE_ORDER)) {
                Integer timeOut = rjbldConfig.getSendOrderTimeOut();
                connectTimeOut = timeOut;
                socketTimeOut = timeOut;
            }


            do {
                // 保证每次发起请求时终端流水都是唯一 操作时间
                request.setSeq(String.valueOf(System.currentTimeMillis()));
                // 格式
                String requestContent = JSON.toJSONString(request);
                // 发送请求
                httpResponse = HttpClientUtil.doPost(rjbldConfig.getBaseUrl() + api, requestContent, RJBLDConstants.CONTENTTYPE,
                        connectTimeOut, socketTimeOut);
                if (HttpResponse.STATUS.SUCCESS == httpResponse.status) {
                    break;
                }
            } while (--retrySize > 0);

            if (HttpResponse.STATUS.CONNECT_TIME_OUT == httpResponse.status || HttpResponse.STATUS.READ_TIME_OUT == httpResponse.status) {
                log.error("请求如家接口失败,接口超时.url:{}" , api);
                return null;
            } else if (HttpResponse.STATUS.SYSTEM_ERROR == httpResponse.status) {
                log.error("请求如家接口失败,接口调用异常.url:{}" , api);
                return null;
            }
            resp = httpResponse.getContent();
            return resp;
        } catch (Exception e) {
            log.error("如家发送请求失败,发生未知错误.url:{}" , api, e);
            return null;
        }finally {
            if (rjbldConfig.getIsDebug() == 1){
                String index = "hotel-shub-rjbld-server";
                Map<String, String> map = new HashMap<>();
                map.put(SlsEnum.LEVEL.getType(), "info");
                map.put(SlsEnum.NAME.getType(), index);
                map.put(SlsEnum.MESSAGE.getType(), "原始日志");
                map.put("url", rjbldConfig.getBaseUrl() + api);
                map.put("request", JSON.toJSONString(request));
                map.put("response", resp);
                String topic = "创建订单步骤1-查询产品";
                slsLogger.saveLog(map,topic, index);
            }

        }

    }
}
