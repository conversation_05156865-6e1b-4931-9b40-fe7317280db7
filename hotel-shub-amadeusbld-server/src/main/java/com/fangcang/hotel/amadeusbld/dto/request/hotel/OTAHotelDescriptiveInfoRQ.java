package com.fangcang.hotel.amadeusbld.dto.request.hotel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/22 10:24
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "OTA_HotelDescriptiveInfoRQ")
public class OTAHotelDescriptiveInfoRQ {

    @XmlAttribute(name = "xmlns")
    private String xmlns = "http://www.opentravel.org/OTA/2003/05";

    @XmlAttribute(name = "PrimaryLangID")
    private String primaryLangID;

    @XmlAttribute(name = "EchoToken")
    private String echoToken;

    @XmlAttribute(name = "Version")
    private String version;

    @XmlElementWrapper(name = "HotelDescriptiveInfos")
    @XmlElement(name = "HotelDescriptiveInfo")
    private List<HotelDescriptiveInfo> hotelDescriptiveInfoList;

    public String getPrimaryLangID() {
        return primaryLangID;
    }

    public void setPrimaryLangID(String primaryLangID) {
        this.primaryLangID = primaryLangID;
    }

    public String getEchoToken() {
        return echoToken;
    }

    public void setEchoToken(String echoToken) {
        this.echoToken = echoToken;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public List<HotelDescriptiveInfo> getHotelDescriptiveInfoList() {
        return hotelDescriptiveInfoList;
    }

    public void setHotelDescriptiveInfoList(List<HotelDescriptiveInfo> hotelDescriptiveInfoList) {
        this.hotelDescriptiveInfoList = hotelDescriptiveInfoList;
    }
}
