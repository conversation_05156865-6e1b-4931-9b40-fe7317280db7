package com.fangcang.hotel.rjbld.xxl.jobs;

import com.fangcang.hotel.rjbld.service.impl.RJBLDServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 *  ${bean.classDesc}
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:15
 */

@Slf4j
@Component
public class RJBLDJobRunner{

    @Autowired
    private RJBLDServiceImpl rjbldService;




    /**
     * 初始化如家有房型映射的酒店到缓存用于起价同步
     * @throws Exception
     */
//    @XxlJob("InitHotelIdForQueryLowestPriceTask")
//    public void InitHotelIdForQueryLowestPriceTask() throws Exception {
//        XxlJobHelper.log("XXL-JOB, Hello World.");
//
//        XxlJobHelper.handleSuccess();
//    }


    /**
     * 同步如家酒店ID到缓存，用于同步酒店基础信息
     *
     * @throws Exception
     */
    @XxlJob("RJBLDsyncHotelIdsToCacheTask")
    public void RJBLDsyncHotelIdsToCacheTask() throws Exception {
        XxlJobHelper.log("同步如家酒店ID到缓存任务开始");
        rjbldService.getHotelIdToCache();
        XxlJobHelper.log("同步如家酒店ID到缓存任务结束");
        XxlJobHelper.handleSuccess();
    }

    /**
     *同步酒店基础信息
     *
     * @throws Exception
     */
    @XxlJob("RJBLDsyncHotelBasicInfoTask")
    public void RJBLDsyncHotelBasicInfoTask() throws Exception {
        XxlJobHelper.log("同步如家酒店基础信息任务开始");
        rjbldService.syncHotelBaseInfo();
        XxlJobHelper.log("同步如家酒店基础信息任务结束");
        XxlJobHelper.handleSuccess();
    }

    /**
     *  同步如家起价任务
     * @throws Exception
     */
    @XxlJob("RJBLDsyncLowestPriceTask")
    public void RJBLDsyncLowestPriceTask() throws Exception {
        XxlJobHelper.log("同步如家起价任务开始");
        rjbldService.syncLowesPriceInfo();
        XxlJobHelper.log("同步如家起价任务结束");
        XxlJobHelper.handleSuccess();
    }

    /**
     *  同步如家城市列表
     *
     * @throws Exception
     */
    @XxlJob("RJBLDsyncCityInfoListTask")
    public void RJBLDsyncCityInfoListTask() throws Exception {
        XxlJobHelper.log("同步如家城市列表任务开始");
        rjbldService.syncCityInfoList();
        XxlJobHelper.log("同步如家城市列表任务结束");
        XxlJobHelper.handleSuccess();
    }
}

