package com.fangcang.hotel.bookingosbld.dto.response.price;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/18 17:39
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Transportation")
public class Transportation {

    @XmlAttribute(name = "TransportationCode")
    private String transportationCode;

    public String getTransportationCode() {
        return transportationCode;
    }

    public void setTransportationCode(String transportationCode) {
        this.transportationCode = transportationCode;
    }
}
