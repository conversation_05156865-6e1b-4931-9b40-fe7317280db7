package com.fangcang.hotel.amadeusbld.dto.response.price;


import com.fangcang.hotel.amadeusbld.dto.response.common.SoapFault;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/21 10:19
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "soap:Body")
public class HotelAvailResponseBody {

    @XmlElement(name = "soap:Fault")
    private SoapFault soapFault;

    @XmlElement(name = "OTA_HotelAvailRS")
    private OTAHotelAvailRS otaHotelAvailRS;

    public SoapFault getSoapFault() {
        return soapFault;
    }

    public void setSoapFault(SoapFault soapFault) {
        this.soapFault = soapFault;
    }

    public OTAHotelAvailRS getOtaHotelAvailRS() {
        return otaHotelAvailRS;
    }

    public void setOtaHotelAvailRS(OTAHotelAvailRS otaHotelAvailRS) {
        this.otaHotelAvailRS = otaHotelAvailRS;
    }
}
