package com.fangcang.hotel.irevebld.manger;


import com.fangcang.hotel.irevebld.dto.request.*;
import com.fangcang.hotel.irevebld.dto.response.ResultDto;
import com.fangcang.hotel.irevebld.dto.response.order.CancelOrderDto;
import com.fangcang.hotel.irevebld.dto.response.order.CreateOrderDto;
import com.fangcang.hotel.irevebld.dto.response.order.OrderDetailDto;
import com.fangcang.hotel.irevebld.dto.response.order.OrderPreBookDto;
import com.fangcang.hotel.irevebld.dto.response.product.ProductIdDto;
import com.fangcang.hotel.irevebld.dto.response.product.ProductIncrementDto;
import com.fangcang.hotel.irevebld.dto.response.product.ProductSaleDto;

/**
 * ireve不落地 调用外部接口封装
 */
public interface IREVEBLDManager {

    /**
     * 查询酒店id列表
     */
    ResultDto<Object> queryHotelIdList(HotelReq req, AccountReq accountReq);

    /**
     * 查询酒店详情信息
     */
    ResultDto<Object> queryHotelInfo(HotelStaticInfoReq req, AccountReq accountReq);

    /**
     * 查询增量酒店id列表
     */
    ResultDto<Object> queryHotelIncrement(HotelReq req, AccountReq accountReq);

    /**
     * 查询酒店下可售的产品id列表
     */
    ResultDto<ProductIdDto> queryProductIdList(ProductReq req, AccountReq accountReq);

    /**
     * 查询客户在酒店下产品多天售卖信息
     */
    ResultDto<ProductSaleDto> queryProductSaleInfoList(ProductReq req, AccountReq accountReq);

    /**
     * 查询酒店产品增量
     */
    ResultDto<ProductIncrementDto> queryProductIncrement(ProductReq req, AccountReq accountReq);

    /**
     * 试预订
     */
    ResultDto<OrderPreBookDto> preBook(PreBookReq req, AccountReq accountReq);

    /**
     * 创建订单
     */
    ResultDto<CreateOrderDto> createOrder(CreateOrderReq req, AccountReq accountReq);

    /**
     * 取消订单
     */
    ResultDto<CancelOrderDto> cancelOrder(OrderReq req, AccountReq accountReq);

    /**
     * 查询订单详情
     */
    ResultDto<OrderDetailDto> queryOrderDetail(OrderReq req, AccountReq accountReq);
}
