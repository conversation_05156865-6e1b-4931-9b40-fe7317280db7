package com.fangcang.hotel.common.config;

import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.Data;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;

/**
@program: glink_shub
@ClassName EsConfig
@description:
@author: 湫
@create: 2025/06/26/ 16:41
@Version 1.0
**/
@RefreshScope //动态刷新配置
@Component //注入到spring容器中
@Data //lombok注解，自动生成get/set方法
public class EsConfig {
    @Value("${elasticsearch.host}")
    private String[] host;

    @Value("${elasticsearch.port}")
    private int port;

    @Value("${elasticsearch.username}")
    private String userName;

    @Value("${elasticsearch.password}")
    private String password;

    private static final String HTTP_SCHEME = "http";

    final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();


    /**
     * 处理请求地址
     * @param address
     * @return
     */
    private HttpHost makeHttpHost(String address) {
        assert StringUtils.isNotEmpty(address);
        String[] hostAddress = address.split(":");
        if (hostAddress.length == 2) {
            String ip = hostAddress[0];
            Integer port = Integer.valueOf(hostAddress[1]);
            return new HttpHost(ip, port, HTTP_SCHEME);
        } else {
            return null;
        }

    }

    @Bean(name = "restClientBuilder")
    public RestClientBuilder restClientBuilder() {
        HttpHost[] hosts = Arrays.stream(host)
                .map(this::makeHttpHost)
                .filter(Objects::nonNull)
                .toArray(HttpHost[]::new);
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(userName, password));
        return RestClient.builder(hosts).setHttpClientConfigCallback(httpClientBuilder ->
                httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
                        .setMaxConnPerRoute(100)
                        //最大连接数
                        .setMaxConnTotal(100)
        ).setRequestConfigCallback(builder -> {
            builder.setConnectTimeout(-1);
            builder.setSocketTimeout(60000);
            builder.setConnectionRequestTimeout(-1);
            return builder;
        });
    }
}
