package com.fangcang.hotel.common.service.supply;

import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyPayConfigAditReqVO;
import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyPayConfigPageReqVO;
import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyPayConfigQueryReqVO;
import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyPayConfigRespVO;
import com.fangcang.hotel.common.dal.db.supply.SupplyPayConfigDO;
import com.fangcang.hotel.framework.common.domain.PageResult;

import javax.validation.Valid;
import java.util.List;

/**
 * 供应商付款配置 Service 接口
 *
 * <AUTHOR>
 */
public interface SupplyPayConfigService {

    /**
     * 创建供应商付款配置
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long supplyPayConfigAdd(@Valid SupplyPayConfigAditReqVO reqVO);

    /**
     * 更新供应商付款配置
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long supplyPayConfigEdit(@Valid SupplyPayConfigAditReqVO reqVO);

    /**
     * 删除供应商付款配置
     *
     * @param id 编号
     */
    void supplyPayConfigDel(Long id);

    /**
     * 获得供应商付款配置信息
     *
     * @param id 编号
     * @return 供应商付款配置信息
     */
    SupplyPayConfigDO supplyPayConfigDetail(Long id);


    /**
     * 根据id查询供应商付款配置
     * @param id 主键
     * @return 供应商付款配置
     */
    SupplyPayConfigRespVO selectSupplyPayConfigById(Long id);


    /**
     * 根据id查询供应商付款配置
     * @param reqVO 查询条件
     * @return 供应商付款配置
     */
    SupplyPayConfigRespVO selectSupplyPayConfig(SupplyPayConfigQueryReqVO reqVO);

    /**
     * 获得供应商付款配置列表
     *
     * @param reqVO 查询条件
     * @return 供应商付款配置列表
     */
    List<SupplyPayConfigDO> supplyPayConfigList(@Valid SupplyPayConfigQueryReqVO reqVO);

    /**
     * 获得供应商付款配置分页
     *
     * @param reqVO 查询条件
     * @return 供应商付款配置分页
     */
    PageResult<SupplyPayConfigRespVO> supplyPayConfigPage(@Valid SupplyPayConfigPageReqVO reqVO);

}
