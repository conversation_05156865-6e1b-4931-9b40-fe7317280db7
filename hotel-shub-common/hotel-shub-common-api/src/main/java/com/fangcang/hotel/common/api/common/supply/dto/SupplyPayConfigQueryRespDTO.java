package com.fangcang.hotel.common.api.common.supply.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description: 供应商支付配置查询返回DTO
 * @author: qiu
 * @create: 2024-04-08 10:17
 */
@Data
public class SupplyPayConfigQueryRespDTO {

    private Long id;

    /** 商家编码 */
    private String merchantCode;

    /** 商家名称 */
    private String merchantName;

    /** 商家来源 */
    private String merchantSource;

    /** 供应类型 */
    private String supplyClass;

    /** 供应商编码 */
    private String supplyCode;

    /** 供应商名称 */
    private String supplyName;

    /** 支付类型 [1现付,2 预付] */
    private Integer payMethod;

    /** 付款分类 [0:billBack,1:动态VCC,2.固定VCC] */
    private Integer modeOfPay;

    /** 付款维度 [0:供应类型,1.供应商,2.供应商+国家,3.供应商+酒店，4.客户] */
    private Integer payDimension;

    /** 属性名 */
    private String attributeName;

    /** 属性值 */
    private String attributeValue;

    /**
     * 付款主体编码 用于区分不同付款主体
     */
    private String paymentSubjectCode;

    /**
     * 付款主体名称
     */
    private String paymentSubjectName;

    /**
     * 付款主体币种
     */
    private String paymentSubjectCurrency;

    /** 固定VCCId */

    private Long payVccId;

    /** 备注 备注信息不会进行拼接，配置什么传入什么给amadeus。 */
    private String remark;

    /** 开户行 */
    private String bankName;

    /** 开户名 */
    private String payName;

    /** 卡号 */
    private String cardNumber;

    /**
     * VCC卡类型(0:mastercard(CA) 1:visa(VI) 2:amex(AX))
     */
    private Integer cardType;

    /**
     * VCC码
     */
    private String cardCode;

    /**
     * 有效时间
     */
    private String activeDate;

    /**
     * 持卡人姓
     */
    private String surName;

    /**
     * 持卡人名
     */
    private String firstName;

    /** 状态 [有效,无效] */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdDt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedDt;

    /**
     * 最后更新人
     */
    private String updatedBy;


}