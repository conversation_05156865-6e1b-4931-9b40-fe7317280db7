package com.fangcang.hotel.common.service.vcc;

import com.fangcang.hotel.common.api.common.supply.dto.SupplyPayConfigQueryReqDTO;
import com.fangcang.hotel.common.api.common.supply.dto.SupplyPayConfigQueryRespDTO;
import com.fangcang.hotel.common.api.common.vcc.dto.*;
import com.fangcang.hotel.framework.common.domain.ResultX;

import javax.validation.Valid;
import java.util.List;

/**
 * @description:
 * @author: qiu
 * @create: 2024-04-18 20:36
 */
public interface VirtualCardService {

    /**
     * 查询付款主体列表
     * @return
     */
    ResultX<List<PaySubjectDTO>> queryPaySubjectList(@Valid PaySubjectReqDTO req);

    /**
     * 生成虚拟卡
     * @param requestDTO
     * @return
     */
    ResultX<VCCInfoResponseDTO> generateVirtualCard(@Valid VCCInfoRequestDTO requestDTO);


    /**
     * 生成billBack 回调
     * @param requestDTO
     * @return
     */
    ResultX<CallbackBillBackInfoResponseDTO> callbackBillBack(@Valid CallbackBillBackInfoRequestDTO requestDTO);

    /**
     * 取消虚拟卡
     * @param requestDTO
     * @return
     */
    ResultX<CancelVCCResponseDTO> cancelNotice(@Valid CancelVCCRequestDTO requestDTO);

    /**
     * 取消虚拟卡
     * @param requestDTO
     * @return
     */
    ResultX<SupplyPayConfigQueryRespDTO> queryVccPayConfig(@Valid SupplyPayConfigQueryReqDTO requestDTO);
}