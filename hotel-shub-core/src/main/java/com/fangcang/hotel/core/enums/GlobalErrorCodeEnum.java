package com.fangcang.hotel.core.enums;

import com.fangcang.hotel.framework.common.exception.ErrorCodeInterface;

/**
 * @description:
 * @author: qiu
 * @create: 2024-01-30 20:01
 */
public enum GlobalErrorCodeEnum implements ErrorCodeInterface {

    AUTH_LOGIN_BAD_CREDENTIALS("1002000000", "SUPPLY_PASSWORD_ERROR", "登录失败，账号密码不正确"),
    AUTH_LOGIN_USER_DISABLED("1002000001", "SUPPLY_USER_IS_DISABLED", "登录失败，账号被禁用"),
    AUTH_LOGIN_BAD_ROLE("1002000008", "AUTH_LOGIN_BAD_ROLE", "没有配置角色"),
    // ========== 菜单模块 1002001000 ==========
    MENU_NOT_EXISTS("1002001003", "MENU_NOT_EXISTS", "菜单不存在"),
    // ========== 角色模块 1002002000 ==========
    ROLE_NOT_EXISTS("1002002000", "ROLE_NOT_EXISTS", "角色不存在"),
    ROLE_NAME_DUPLICATE("1002002001", "ROLE_NAME_DUPLICATE", "已经存在名为【{}】的角色"),
    // ========== 用户模块 1002003000 ==========
    USER_USERNAME_EXISTS("1002003000", "SUPPLY_USERNAME_EXISTED", "用户账号已经存在"),
    USER_NOT_EXISTS("1002003003", "SUPPLY_USER_NOT_EXISTS", "用户不存在"),


    //成功
    SUCCESS("20000", "SUCCESS", "成功"),
    TIME_OUT("300003", "CONNECT_TIME_OUT", "系统超时"),
    SYSTEM_ERROR("999999", "SYSTEM_EXCEPTION", "系统异常"),



    //用户异常
    USER_NOT_FOUND("300001", "USER_NOT_FOUND", "用户不存在"),
    USER_NOT_LOGIN("300002", "USER_NOT_LOGIN", "用户未登录"),
    USER_PASSWORD_ERROR("300003", "USER_PASSWORD_ERROR", "用户密码错误"),
    USER_TOKEN_EMPT("300005", "USER_TOKEN_EMPT", "用户未登录，请先登录"),




    //全局统一异常 400-499
    BAD_REQUEST("400", "INVALID_PARAMETER", "请求参数不正确"),
    UNAUTHORIZED("401", "SUPPLY_UNAUTHORIZED", "账号未登录"),
    FORBIDDEN("403", "SUPPLY_FORBIDDEN", "没有该操作权限"),
    NOT_FOUND("404", "SUPPLY_NOT_FOUND", "请求地址不存在"),
    METHOD_NOT_ALLOWED("405", "SUPPLY_METHOD_NOT_ALLOWED", "请求方法不正确"),
    PACK_ERROR("406", "SUPPLY_PACK_ERROR", "包装异常"),
    LOCKED("423", "SUPPLY_LOCKED", "请求失败，请稍后重试"),
    TOO_MANY_REQUESTS("429", "SUPPLY_TOO_MANY_REQUESTS", "请求过于频繁，请稍后重试"),


    // ========== 服务端错误段 ==========
    NOT_IMPLEMENTED("501", "SUPPLY_NOT_IMPLEMENTED", "功能未实现/未开启"),
    REPETIR_ERROR("502", "SUPPLY_REPETIR_ERROR", "数据已存在，请重新输入"),

    SUPPLY_VCC_CARD_NUMBER_ALREADY_EXISTS("503", "SUPPLY_VCC_CARD_NUMBER_ALREADY_EXISTS", "已存在VCC卡号"),

    // sls 日志接入异常
    NOT_NULL("30000", "SLS_NOT_NULL", "必填参数不能为空"),
    LOGSTORE_ERROR("30001", "LOGSTORE_ERROR", "创建LogStore异常"),


    //供应商异常  服务 接口 限流...
    SUPPLY_NOT_EXISTS("0121001", "SUPPLY_NOT_EXISTS", "供应商不存在"),
    SUPPLY_FILE_CONTENT_IS_EMPTY("0121002", "SUPPLY_FILE_CONTENT_IS_EMPTY", "文件内容为空"),

    // forward 应用异常


    SUPPLYTYPE_NOT_SUPPLYCLASS("300001", "SUPPLY_TYPE_NOT_SUPPLYCLASS", "无可用供应商类型"),
    SUPPLYTYPE_NOT_SUPPLYCODE("300002", "SUPPLY_TYPE_NOT_SUPPLYCODE", "无可用供应商编码"),
    RETURN_DATA_NULL("300003", "SUPPLY_RETURN_DATA_NULL", "响应数据为空"),
    SUPPLYCODE_NOTFOUND("300004", "SUPPLY_CODE_NOTFOUND", "供应商编码不存在"),
    SUPPLYCLASS_NOTFOUND("300005", "SUPPLY_CLASS_NOTFOUND", "供应商类型不存在"),
    SUPPLYCLASS_SERVERNAME_NOTFOUND("300006", "SUPPLY_CLASS_SERVERNAME_NOTFOUND", "供应商未配置服务名称"),
    SUPPLY_SERVICE_NOTFOUND("300007", "SUPPLY_SERVICE_NOTFOUND", "未查询到供应商服务"),

    SUPPLY_ORDER_NOTFOUND("310008", "SUPPLY_ORDER_NOTFOUND", "订单不存在"),
    SUPPLY_SUPPLY_CLASS_INVALID("300009", "SUPPLY_SUPPLY_CLASS_INVALID", "供应商类型无效"),


    // config 应用异常
    CHANNEL_CONFIG_NOT_EXISTS("50001", "SUPPLY_CHANNEL_CONFIG_NOT_EXISTS", "渠道配置不存在"),
    SUPPLY_WHITE_LIST_NOT_EXISTS("50002", "SUPPLY_WHITE_LIST_NOT_EXISTS", "供应商白名单不存在"),
    MERCHANT_CONFIG_NOT_EXISTS("50003", "SUPPLY_MERCHANT_CONFIG_NOT_EXISTS", "商家配置不存在"),
    CREATE_SECRET_KEY_ERROR("50004", "SUPPLY_CREATEKEY_ERROR", "生成密钥异常"),


    //common 应用异常
    OPERATE_LOG_NOT_EXISTS("210001", "SUPPLY_OPERATE_LOG_NOT_EXISTS", "操作日志不存在"),
    SUPPLY_HOTEL_NOT_EXISTS("211001", "SUPPLY_HOTEL_NOT_EXISTS", "供应商酒店信息不存在"),
    HOTEL_MAPPING_NOT_EXISTS("211002", "SUPPLY_HOTEL_MAPPING_NOT_EXISTS", "酒店映射不存在"),
    HOTEL_MAPPING_ERROR_NOT_EXISTS("211003", "SUPPLY_HOTEL_MAPPING_ERROR_NOT_EXISTS", "错误映射不存在"),
    SUPPLY_HOTEL_LOWEST_PRICE_NOT_EXISTS("211004", "SUPPLY_HOTEL_LOWEST_PRICE_NOT_EXISTS", "供应商酒店起价信息不存在"),
    CONFIG_EXTEND_NOT_EXISTS("211005", "SUPPLY_CONFIG_EXTEND_NOT_EXISTS", "供应商配置扩展不存在"),
    CONFIG_INFO_NOT_EXISTS("211006", "SUPPLY_CONFIG_INFO_NOT_EXISTS", "供应商配置信息不存在"),
    CONFIG_TEMPLET_NOT_EXISTS("211007", "SUPPLY_CONFIG_TEMPLET_NOT_EXISTS", "配置模板不存在"),
    CITY_MAPPING_NOT_EXISTS("211008", "SUPPLY_CITY_MAPPING_NOT_EXISTS", "城市映射不存在"),
    SUPPLY_IMAGE_NOT_EXISTS("211009", "SUPPLY_IMAGE_NOT_EXISTS", "供应商图片信息不存在"),
    SUPPLY_ORDER_NOT_EXISTS("2110010", "SUPPLY_ORDER_NOT_EXISTS", "供应商订单不存在"),
    SUPPLY_ORDER_ITEM_NOT_EXISTS("2110011", "SUPPLY_ORDER_ITEM_NOT_EXISTS", "供应商订单详情不存在"),
    ROOM_MAPPING_NOT_EXISTS("2110012", "SUPPLY_ROOM_MAPPING_NOT_EXISTS", "房型映射不存在"),
    SUPPLY_INFO_NOT_EXISTS("2110013", "SUPPLY_INFO_NOT_EXISTS", "供应类型信息不存在"),
    SUPPLY_ROOM_NOT_EXISTS("2110014", "SUPPLY_ROOM_NOT_EXISTS", "供应商房型信息不存在"),
    SUPPLY_SHARE_CONFIG_NOT_EXISTS("2110015", "SUPPLY_SHARE_CONFIG_NOT_EXISTS", "供应商共享配置不存在"),
    SUPPLY_SHARE_CONFIG_IDENTICAL("2110016", "SUPPLY_SHARE_CONFIG_IDENTICAL", "共享方和被共享方不能相同"),
    MERCHANT_INTERFACE_FREQUENCY_NOT_EXISTS("2110017", "SUPPLY_MERCHANT_INTERFACE_FREQUENCY_NOT_EXISTS", "商家频率不存在"),

    SUPPLY_PAY_CONFIG_NOT_EXISTS("2110018", "SUPPLY_PAY_CONFIG_NOT_EXISTS", "供应商付款配置不存在"),
    SUPPLY_SALEABLE_HOTELS_NOT_EXISTS("2110019", "SUPPLY_SALEABLE_HOTELS_NOT_EXISTS", "供应商可售酒店范围不存在"),
    SUPPLY_VCC_PAY_ACCOUNT_NOT_EXISTS("2110020", "SUPPLY_VCC_PAY_ACCOUNT_NOT_EXISTS", "供应商固定VCC配置不存在"),
    SUPPLY_DATA_RANGE_INVALID("2110021", "SUPPLY_DATA_RANGE_INVALID", "供应商数据范围"),
    SUPPLY_HOTEL_ID_NOT_EMPTY("2110022", "SUPPLY_HOTEL_ID_NOT_EMPTY", "供应商酒店id不能为空"),
    SUPPLY_VCC_PAY_ACCOUNT_NOT_NULL("2110023", "SUPPLY_VCC_PAY_ACCOUNT_NOT_NULL", "供应商固定VCC配置不能为空"),
    SUPPLY_SUPPLY_CODE_NOT_NULL("2110024", "SUPPLY_SUPPLY_CODE_NOT_NULL", "供应商编码不允许为空"),
    SUPPLY_SUPPLY_NAME_NOT_NULL("2110025", "SUPPLY_SUPPLY_NAME_NOT_NULL", "供应商名称不允许为空"),
    SUPPLY_UNABLE_TO_ADD_INVALID_DATA("2110026", "SUPPLY_UNABLE_TO_ADD_INVALID_DATA", "不能新增无效数据哦"),
    SUPPLY_PAY_CONFIG_REPEAT("2110027", "SUPPLY_PAY_CONFIG_REPEAT", "已存在该付款类型配置"),
    SUPPLY_PARTIAL_SUCCESS("2110028", "SUPPLY_PARTIAL_SUCCESS", "成功{0}条,失败{1}条"),
    SUPPLY_GET_DYNAMICVCC_ERROR("2110029", "SUPPLY_GET_DYNAMICVCC_ERROR", "获取动态VCC信息失败"),
    SUPPLY_GET_EXCHANGE_RATE_ERROR("2110030", "SUPPLY_GET_EXCHANGE_RATE_ERROR", "获取汇率失败"),
    SUPPLY_GET_MERCHATN_CURRENCY_ERROR("2110031", "SUPPLY_GET_MERCHATN_CURRENCY_ERROR", "获取商家币种失败"),
    SUPPLY_SUPPLYCLASS_INPUT_ERROR("2110032", "SUPPLY_SUPPLYCLASS_INPUT_ERROR", "供应类型输入错误,仅支持英文字符"),
    SUPPLY_INCORRECT_FILE_TYPE("2110033", "SUPPLY_INCORRECT_FILE_TYPE", "文件类型不正确"),
    SUPPLY_FILE_TEMPLATE_ERROR("2110034", "SUPPLY_FILE_TEMPLATE_ERROR", "文件模板不正确"),
    SUPPLY_FUSING_CONFIG_NOT_EXISTS("2110035", "SUPPLY_FUSING_CONFIG_NOT_EXISTS", "供应商熔断配置不存在"),
    SUPPLY_FUSING_LOG_NOT_EXISTS("2110036", "SUPPLY_FUSING_LOG_NOT_EXISTS", "供应商熔断日志不存在"),
    VCC_PAY_ACCOUNT_INFO_NOT_EXIST("2110037", "VCC_PAY_ACCOUNT_INFO_NOT_EXIST", "VCC付款配置不存在"),

    FAIL_SIGNATURE("********", "SUPPLY_FAIL_SIGNATURE", "token解析失败"),
    EXPIRED_SIGNATURE("********", "SUPPLY_EXPIRED_SIGNATURE", "token过期"),
    EXCEPTION_SIGNATURE("********", "SUPPLY_EXCEPTION_SIGNATURE", "token解析异常"),
    EXCEPTION_DATETIME("********", "EXCEPTION_DATETIME", "日期格式错误"),
    EXCEPTION_TIMEZONE("********", "EXCEPTION_TIMEZONE", "区域时区查询异常"),


    // shub 接口异常码  400001 ~ 419999
    MERCHANTCONFIG_NOT_FOUND("400001", "SUPPLY_MERCHANTCONFIG_NOT_FOUND", "商家信息不存在"),
    SUPPLY_TOKEN_INVALID("400002", "SUPPLY_TOKEN_INVALID", "accessToken"),
    SIGNATURE_VERIFICATION_FAILED("400003", "SUPPLY_SIGNATURE_VERIFICATION_FAILED", "签名验证失败"),
    SIGNATURE_NOT_NULL("400004", "SUPPLY_SIGNATURE_NOT_NULL", "signature不可为空"),
    TIMESTAMP_NOT_NULL("400005", "SUPPLY_TIMESTAMP_NOT_NULL", "timestamp不可为空"),
    TIMESTAMP_LENGTH_EXCEEDS("400006", "SUPPLY_TIMESTAMP_LENGTH_EXCEEDS", "timestamp长度必须为13位"),
    NONCE_NOT_NULL("400007", "SUPPLY_NONCE_NOT_NULL", "nonce不可为空"),
    MERCHANTSOURCE_NOT_NULL("400008", "SUPPLY_MERCHANTSOURCE_NOT_NULL", "商家来源不可为空"),

    MERCHANTCODE_NOT_NULL("400009", "SUPPLY_MERCHANTCODE_NOT_NULL", "商家编码不可为空"),
    REQUEST_HAS_EXPIRED("400010", "SUPPLY_REQUEST_HAS_EXPIRED", "请求已过期"),
    SUPPLY_MERCHANTSOURCE_INVALID("4000011", "SUPPLY_MERCHANTSOURCE_INVALID", "商家来源无效"),


    // shub 接口异常码  420000 ~ 429999
    OBJECT_CONVER_EXCEPTION("420001", "SUPPLY_OBJECT_CONVER_EXCEPTION", "对象转换异常"),
    MISSING_PARAM("420002", "SUPPLY_MISSING_PARAM", "缺少必要参数"),

    SUPPLIER_NOT_CONFIGURED("420003", "SUPPLY_SUPPLIER_NOT_CONFIGURED", "供应商未配置"),
    ROOM_NUM_UNLIKE("420004", "SUPPLY_ROOM_NUM_UNLIKE", "房型数不一致"),
    CURRENCY_NOT_EQUAL("420005", "SUPPLY_CURRENCY_NOT_EQUAL", "币种不一致"),
    PAYMETHOD_NOT_EQUAL("420006", "SUPPLY_PAYMETHOD_NOT_EQUAL", "支付类型错误"),
    DAY_MAX_LIMIT("420007", "SUPPLY_DAY_MAX_LIMIT", "超过最大入住天数"),
    SUPPLY_SERVICE_NOT_FOUND("420008", "SUPPLY_SERVICE_NOT_FOUND", "供应商服务未注册"),
    ROOMGUESTNUMBERS_NOT_NULL("420009", "SUPPLY_ROOMGUESTNUMBERS_NOT_NULL", "房间人数信息不可为空"),
    SIGNATURE_ERROR("420010", "SIGNATURE_ERROR", "密钥错误"),


    // 映射错误 510000~519999
    HOTEL_NOT_MAPPING("510001", "SUPPLY_HOTEL_NOT_MAPPING", "酒店未映射"),
    ROOM_NOT_MAPPING("510002", "SUPPLY_ROOM_NOT_MAPPING", "房型未映射"),
    NOT_FOUND_ACTIVE_ROOM_MAPPING("510003", "SUPPLY_NOT_FOUND_ACTIVE_ROOM_MAPPING", "未找到有效房型映射"),

    //试预定异常码 610000 ~ 619999
    SUPPLY_PRE_DISSATISFY_BOOKINGCLAUSE("610002", "SUPPLY_PRE_DISSATISFY_BOOKINGCLAUSE", "试预订失败，不满足预定条款"),
    SUPPLY_PRE_BOOKING_FULL_ROOM("610003", "SUPPLY_PRE_BOOKING_FULL_ROOM", "试预订失败, 供应商返回满房{0}"),
    SUPPLY_PRE_BOOKING_PENDING_QUERY("610004", "SUPPLY_PRE_BOOKING_PENDING_QUERY", "试预订失败, 供应商返回不可订{0}"),
    SUPPLY_PRE_BOOKING_FAIL("610005", "SUPPLY_PRE_BOOKING_FAIL", "试预订失败, 供应商返回失败{0}"),
    SUPPLY_PRE_BOOKING_RETURN_AMT_ISNULL("610006", "SUPPLY_PRE_BOOKING_RETURN_AMT_ISNULL", "试预订未返回金额"),
    SUPPLY_PRE_DISSATISFY_BOOKROOMSCLAUSE("610007", "SUPPLY_PRE_DISSATISFY_BOOKROOMSCLAUSE", "试预订失败，不满足间数条款"),
    SUPPLY_PRE_BOOKING_INVALID_DATE("610008", "SUPPLY_PRE_BOOKING_INVALID_DATE", "试预订失败, 预订日期"),
    SUPPLY_PRE_BOOKING_ROOM_NUM_NOT_ENOUGH("610009", "SUPPLY_PRE_BOOKING_ROOM_NUM_NOT_ENOUGH", "试预订失败, 房量不足"),
    SUPPLY_PRE_BOOKING_EXCEPTION("6100010", "SUPPLY_PRE_BOOKING_EXCEPTION", "试预订失败, 系统异常"),

    //下单异常码 620000 ~ 629999

    SUPPLY_CREATE_ORDER_FIAIL("620001", "SUPPLY_CREATE_ORDER_FIAIL", "下单失败，{0}"),
    SUPPLY_RETURN_RESULT_FAIL("620002", "SUPPLY_RETURN_RESULT_FAIL", "下单失败，供应商返回下单失败"),
    CHARGE_PRICE("620003", "SUPPLY_CHARGE_PRICE", "下单失败，供应商返回产品变价"),
    BREAKFAST_NUM_UNLIKE("620004", "SUPPLY_BREAKFAST_NUM_UNLIKE", "下单失败，早餐数量不一致"),
    PRODUCT_TYPE_UNLIKE("620005", "SUPPLY_PRODUCT_TYPE_UNLIKE", "下单失败，产品类型不一致"),
    ORDER_REPETITION("620006", "SUPPLY_ORDER_REPETITION", "下单失败，订单已创建"),
    SUPPLY_CREATE_FULL_ROOM("620007", "SUPPLY_CREATE_FULL_ROOM", "下单失败，供应商返回满房"),

    SUPPLY_CREATE_INVALID_DATE("620008", "SUPPLY_CREATE_INVALID_DATE", "下单失败，入离日期"),
    BED_NOT_EXIST("620009", "SUPPLY_BED_NOT_EXIST", "下单失败,缺少床型参数，请核实请求参数是否符合"),
    BED_NOT_FOUND("620010", "SUPPLY_BED_NOT_FOUND", "下单失败,找不到具体床型，请核实订单是否有正确"),
    SUPPLY_CREATE_ORDER_EXCEPTION("620011", "SUPPLY_CREATE_ORDER_EXCEPTION", "下单失败, 系统异常"),
    SUPPLY_CREATE_QUOTA_ENOUGH("6200012", "SUPPLY_CREATE_QUOTA_ENOUGH", "下单失败，配额不足"),
    SUPPLY_IN_ORDER_OPERATION("6200013", "SUPPLY_IN_ORDER_OPERATION", "订单操作中"),
    SUPPLY_SAVE_ORDER_ERROR("6200014", "SUPPLY_SAVE_ORDER_ERROR", "供应商创建订单成功,保存订单失败,系统异常。"),


    //预定条款 begin
    //最大入住间数 大于时异常
    SUPPLY_CREATE_ONLY_ONE_ROOM("6210001", "SUPPLY_CREATE_ONLY_ONE_ROOM", "不满足预订间数条款，最多只能预定一间房"),
    SUPPLY_CREATE_ONLY_TWO_ROOM("6210002", "SUPPLY_CREATE_ONLY_TWO_ROOM", "不满足预订间数条款，最多只能预定两间房"),
    SUPPLY_CREATE_ONLY_THREE_ROOM("6210003", "SUPPLY_CREATE_ONLY_THREE_ROOM", "不满足预订间数条款，最多只能预定三间房"),
    SUPPLY_CREATE_ONLY_FOUR_ROOM("6210004", "SUPPLY_CREATE_ONLY_FOUR_ROOM", "不满足预订间数条款，最多只能预定四间房"),
    SUPPLY_CREATE_ONLY_FIVE_ROOM("6210005", "SUPPLY_CREATE_ONLY_FIVE_ROOM", "不满足预订间数条款，最多只能预定五间房"),
    SUPPLY_CREATE_ONLY_SIX_ROOM("6210006", "SUPPLY_CREATE_ONLY_SIX_ROOM", "不满足预订间数条款，最多只能预定六间房"),
    SUPPLY_CREATE_ONLY_SEVEN_ROOM("6210007", "SUPPLY_CREATE_ONLY_SEVEN_ROOM", "不满足预订间数条款，最多只能预定七间房"),
    SUPPLY_CREATE_ONLY_EIGHT_ROOM("6210008", "SUPPLY_CREATE_ONLY_EIGHT_ROOM", "不满足预订间数条款，最多只能预定八间房"),
    SUPPLY_CREATE_ONLY_NINE_ROOM("6210009", "SUPPLY_CREATE_ONLY_NINE_ROOM", "不满足预订间数条款，最多只能预定九间房"),
    SUPPLY_CREATE_ONLY_TEN_ROOM("6210010", "SUPPLY_CREATE_ONLY_TEN_ROOM", "不满足预订间数条款，最多只能预定十间房"),

    //最少入住间数 小于时异常
    SUPPLY_PREDETERMINE_MIN_TWO_ROOM("6220002", "SUPPLY_PREDETERMINE_MIN_TWO_ROOM", "不满足预订间数条款，最少预定两间房"),
    SUPPLY_PREDETERMINE_MIN_THREE_ROOM("6220003", "SUPPLY_PREDETERMINE_MIN_THREE_ROOM", "不满足预订间数条款，最少预定三间房"),
    SUPPLY_PREDETERMINE_MIN_FOUR_ROOM("6220004", "SUPPLY_PREDETERMINE_MIN_FOUR_ROOM", "不满足预订间数条款，最少只能预定四间房"),
    SUPPLY_PREDETERMINE_MIN_FIVE_ROOM("6220005", "SUPPLY_PREDETERMINE_MIN_FIVE_ROOM", "不满足预订间数条款，最少只能预定五间房"),
    SUPPLY_PREDETERMINE_MIN_SIX_ROOM("6220006", "SUPPLY_PREDETERMINE_MIN_SIX_ROOM", "不满足预订间数条款，最少只能预定六间房"),
    SUPPLY_PREDETERMINE_MIN_SEVEN_ROOM("6220007", "SUPPLY_PREDETERMINE_MIN_SEVEN_ROOM", "不满足预订间数条款，最少只能预定七间房"),
    SUPPLY_PREDETERMINE_MIN_EIGHT_ROOM("6220008", "SUPPLY_PREDETERMINE_MIN_EIGHT_ROOM", "不满足预订间数条款，最少只能预定八间房"),
    SUPPLY_PREDETERMINE_MIN_NINE_ROOM("6220009", "SUPPLY_PREDETERMINE_MIN_NINE_ROOM", "不满足预订间数条款，最少只能预定九间房"),
    SUPPLY_PREDETERMINE_MIN_TEN_ROOM("6220010", "SUPPLY_PREDETERMINE_MIN_TEN_ROOM", "不满足预订间数条款，最少只能预定十间房"),

    SUPPLY_DISSATISFY_BOOKROOMSCLAUSE("6210011", "SUPPLY_DISSATISFY_BOOKROOMSCLAUSE", "不满足预订间数条款"),


    //提前*天不满足预定异常
    SUPPLY_ADVANCE_BOOKING_IN_ADVANCE_DAY("6230001", "SUPPLY_ADVANCE_BOOKING_IN_ADVANCE_DAY", "不满足提天预订条款,当前不可预定"),


    //连住条款不满足异常

    //最少连住天数 小于时异常
    SUPPLY_STAY_MIN_STAY_ONE_DAY("6240001", "SUPPLY_STAY_MIN_STAY_ONE_DAY", "不满足最少连住天数条款"),
    //限住天数 不等于时异常
    SUPPLY_STAY_LIMIT_STAY_ONE_DAY("6240002", "SUPPLY_STAY_LIMIT_STAY_ONE_DAY", "不满足限住天数条款"),
    //最多连住天数 大于时异常
    SUPPLY_STAY_MAX_STAY_ONE_DAY("6240003", "SUPPLY_STAY_MAX_STAY_ONE_DAY", "不满足最多连住天数条款"),


    // 预定条款 end


    // 查询订单异常码 630000 ~ 639999
    SUPPLY_ORDER_CHECKIN_IS_EMPTY("630001", "SUPPLY_ORDER_CHECKIN_IS_EMPTY", "查询实际入住信息失败，实际入住信息为空"),
    SUPPLY_ORDER_CHECKIN_ORDER_NOT_LEFT("6300002", "SUPPLY_ORDER_CHECKIN_ORDER_NOT_LEFT", "查询实际入住信息失败，订单状态非已离店"),
    SUPPLY_ORDER_CODE_EXISTED("6300003", "SUPPLY_ORDER_CODE_EXISTED", "供应商订单号已经存在"),
    QUERY_ORDER_ERROR("6300004", "SUPPLY_QUERY_ORDER_ERROR", "查询订单状态异常"),
    ORDER_NOT_EXIST_MAPPING("6300005", "SUPPLY_ORDER_NOT_EXIST_MAPPING", "订单映射不存在"),
    ORDER_EXIST_MUCH_MAPPING("6300006", "SUPPLY_ORDER_EXIST_MUCH_MAPPING", "订单存在多映射"),
    SUPPLY_ORDER_NOT_EXIST("6300007", "SUPPLY_ORDER_NOT_EXIST", "供应商订单不存在"),
    SUPPLY_QUERY_ORDER_EXCEPTION("6300008", "SUPPLY_QUERY_ORDER_EXCEPTION", "查询订单失败, 系统异常"),


    // 取消订单异常码 640000 ~ 649999
    SUPPLY_CANCEL_ORDER_EXCEPTION("640003", "SUPPLY_CANCEL_ORDER_EXCEPTION", "取消订单失败, 系统异常"),
    SUPPLY_CANCEL_ORDER_FIAIL("640001", "SUPPLY_CANCEL_ORDER_FIAIL", "取消订单失败，供应商返回取消失败"),
    NO_CANCEL_ORDER("640002", "SUPPLY_NO_CANCEL_ORDER", "取消订单失败，供应商返回不可取消"),
    SUPPLY_DISSATISFY_CANCELCLAUSE("640003", "SUPPLY_DISSATISFY_CANCELCLAUSE", "订单不支持取消,不满足取消条款"),


    // 供应商订单其他异常码 650000 ~ 659999
    SUPPLY_API_EXCEPTION("650000", "SUPPLY_API_EXCEPTION", "{0}失败,供应商接口异常"),
    SUPPLY_RETURN_DATA_FORMAT_ERROR("650001", "SUPPLY_RETURN_DATA_FORMAT_ERROR", "{0}失败,供应商返回结果格式不正确, 响应参数:{1}"),
    SUPPLY_RETURN_CANNOT_BOOK("650002", "SUPPLY_RETURN_CANNOT_BOOK", "供应商返回不可订"),
    SUPPLY_RETURN_FULL_ROOM("650003", "SUPPLY_RETURN_FULL_ROOM", "供应商返回不可订，存在满房"),
    PRODUCT_NOT_FOUND("650004", "SUPPLY_PRODUCT_NOT_FOUND", "没有找到指定产品"),


    // shub 订单异常码 660000 ~ 669999
    ORDER_STATUS_ERROR("660001", "SUPPLY_ORDER_STATUS_ERROR", "订单状态异常"),

    // 酒店批量查询异常码
    HQ_FILE_SIZE_EXCEED("670001", "IMPORT_FILE_SIZE_EXCEED", "单次最大上传文件数量不可大于10000条，请删除多余数据！"),
    HQ_FILE_EMPTY_ERROR("670002", "IMPORT_FILE_EMPTY_ERROR", "文件内容为空"),
    HQ_HEADER_MISMATCH_ERROR("670003", "IMPORT_HEADER_MISMATCH_ERROR", "导入文件,文件模板不正确"),
    HQ_FILE_FORMAT_ERROR("670004", "IMPORT_FILE_FORMAT_ERROR", "文件格式不正确"),
    HQ_MERCHANT_SOURCE_EMPTY("670005", "MERCHANT_SOURCE_EMPTY", "商家来源不能为空"),
    HQ_MERCHANT_SOURCE_INVALID("670006", "IMPORT_MERCHANT_SOURCE_INVALID", "商家来源不存在"),
    HQ_MERCHANT_CODE_EMPTY("670007", "IMPORT_MERCHANT_CODE_EMPTY", "商家编码不能为空"),
    HQ_MERCHANT_CODE_NOT_EXIST("670008", "IMPORT_MERCHANT_CODE_NOT_EXIST", "商家信息不存在"),
    HQ_SUPPLIER_TYPE_CODE_EMPTY("670009", "IMPORT_SUPPLIER_TYPE_CODE_EMPTY", "供应商类型不能为空"),
    HQ_SUPPLIER_TYPE_CODE_NOT_EXIST("670010", "IMPORT_SUPPLIER_TYPE_CODE_NOT_EXIST", "供应商类型不存在"),
    HQ_SUPPLIER_CODE_EMPTY("670011", "IMPORT_SUPPLIER_CODE_EMPTY", "供应商编码不能为空"),
    HQ_SUPPLIER_CODE_NOT_EXIST("670012", "IMPORT_SUPPLIER_CODE_NOT_EXIST", "供应商编码不存在,请重新输入"),
    HQ_SUPPLIER_HOTEL_ID_EMPTY("670013", "IMPORT_SUPPLIER_HOTEL_ID_EMPTY", "供应商酒店id不能为空"),
    HQ_SUPPLIER_HOTEL_ID_NOT_EXIST("670014", "IMPORT_SUPPLIER_HOTEL_ID_NOT_EXIST", "供应商酒店ID不存在,请重新输入"),
    HQ_CHECKIN_DATE_EMPTY("670015", "IMPORT_CHECKIN_DATE_EMPTY", "入住日期不能为空"),
    HQ_CHECKIN_DATE_INVALID("670016", "IMPORT_CHECKIN_DATE_INVALID", "查询日期必须晚于或等于当前日期,请重新输入"),
    HQ_CHECKIN_DATE_FORMAT_ERROR("670017", "IMPORT_CHECKIN_DATE_FORMAT_ERROR", "入住日期格式不对,请重新输入"),
    HQ_SUPPLIER_HOTEL_DATE_DUPLICATE("670018", "IMPORT_SUPPLIER_HOTEL_DATE_DUPLICATE", "供应商酒店id的入住日期已存在,请重新输入"),
    ;
    private String number;

    /**
     * 错误码
     */
    private String code;
    /**
     * 错误提示
     */
    private String msg;


    private GlobalErrorCodeEnum(String number, String code, String msg) {
        this.number = number;
        this.code = code;
        this.msg = msg;
    }
    @Override
    public String getNumber() {
        return number;
    }
    @Override
    public String getCode() {
        return code;
    }
    @Override
    public String getMsg() {
        return msg;
    }
}