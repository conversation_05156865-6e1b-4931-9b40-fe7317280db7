package com.fangcang.hotel.amadeusbld.dto.response.order.completereservationdetails;

import com.fangcang.hotel.amadeusbld.dto.response.common.BodyError;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/29 19:47
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Hotel_CompleteReservationDetailsReply")
public class HotelCompleteReservationDetailsReply extends BodyError {

    @XmlElement(name = "retrievalKeyGroup")
    private RetrievalKeyGroupResponse retrievalKeyGroupResponse;

    @XmlElement(name = "hotelSalesRequirementsSection")
    private HotelSalesRequirementsSection hotelSalesRequirementsSection;

    public RetrievalKeyGroupResponse getRetrievalKeyGroupResponse() {
        return retrievalKeyGroupResponse;
    }

    public void setRetrievalKeyGroupResponse(RetrievalKeyGroupResponse retrievalKeyGroupResponse) {
        this.retrievalKeyGroupResponse = retrievalKeyGroupResponse;
    }

    public HotelSalesRequirementsSection getHotelSalesRequirementsSection() {
        return hotelSalesRequirementsSection;
    }

    public void setHotelSalesRequirementsSection(HotelSalesRequirementsSection hotelSalesRequirementsSection) {
        this.hotelSalesRequirementsSection = hotelSalesRequirementsSection;
    }
}
