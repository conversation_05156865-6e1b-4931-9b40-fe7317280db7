package com.fangcang.hotel.rjbld.enums;

import com.fangcang.hotel.data.api.enums.GuestTypeEnum;

/**
 *
 *  床型转换枚举
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:15
 */
public enum RJBLDGuestTypeEnum {

    RJBLD_1("1", "所有客人", GuestTypeEnum.All),
    RJBLD_2("2", "内宾", GuestTypeEnum.InSea);

    public String key;

    public String name;

    public GuestTypeEnum value;

    private RJBLDGuestTypeEnum (String key, String name, GuestTypeEnum value) {
        this.key = key;
        this.name = name;
        this.value = value;
    }

    public static RJBLDGuestTypeEnum getEnumByKey(String key) {
        for (RJBLDGuestTypeEnum enumObj : RJBLDGuestTypeEnum.values()) {
            if (enumObj.key.equals(key)) {
                return enumObj;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public GuestTypeEnum getValue() {
        return value;
    }

    public void setValue(GuestTypeEnum value) {
        this.value = value;
    }
}
