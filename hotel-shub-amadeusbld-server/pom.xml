<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mongoso.cloud</groupId>
        <artifactId>supply-hub-project</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>hotel-shub-amadeusbld-server</artifactId>

    <dependencies>



        <!--  公共 common-api -->
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>hotel-shub-common-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>mgs-starter-web</artifactId>
        </dependency>

        <!--        &lt;!&ndash;  公共 config-api &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>com.mongoso.cloud</groupId>-->
        <!--            <artifactId>hotel-shub-config-api</artifactId>-->
        <!--            <version>${revision}</version>-->
        <!--        </dependency>-->

        <!-- data 模块 api -->
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>hotel-shub-data-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- data-base -->
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>hotel-shub-data-base</artifactId>
            <version>${revision}</version>
        </dependency>

        <!--xxl-job -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <!-- mgs 的核心代码 -->
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>hotel-shub-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- mgs 的扩展 redis -->
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>mgs-starter-redis</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!--mongodb-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>


        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- hutool -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <!--mybatisPlus-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>


        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>
    </dependencies>

    <build>
        <!--包名-->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.8</version> <!-- 如果 spring.boot.version 版本修改，则这里也要跟着修改 -->
                <configuration>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <!-- 指定配置文件所在的resource目录 -->
                <directory>src/main/resources</directory>
                <!--打包包含的文件-->
                <includes>
                    <include>logback-spring.xml</include>
                    <include>application.yml</include>
                    <include>bootstrap.yml</include>
                    <include>bootstrap-*.yml</include>
                    <include>amadues.js</include>
                </includes>
                <!--打包需要排除的文件-->
                <excludes>
                    <exclude>test/*</exclude>
                </excludes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <!-- 指定配置文件所在的resource目录 -->
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>**/**</exclude>
                </excludes>
            </resource>
        </resources>
    </build>

</project>