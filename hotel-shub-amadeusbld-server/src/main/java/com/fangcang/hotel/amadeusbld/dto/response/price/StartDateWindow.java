package com.fangcang.hotel.amadeusbld.dto.response.price;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/18 18:14
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "StartDateWindow")
public class StartDateWindow {

    @XmlAttribute(name = "DOW")
    private String dow;

    public String getDow() {
        return dow;
    }

    public void setDow(String dow) {
        this.dow = dow;
    }
}
