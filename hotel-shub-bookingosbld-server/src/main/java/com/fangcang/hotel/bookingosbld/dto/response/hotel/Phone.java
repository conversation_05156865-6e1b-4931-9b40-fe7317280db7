package com.fangcang.hotel.bookingosbld.dto.response.hotel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/22 15:16
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Phone")
public class Phone {

    @XmlAttribute(name = "PhoneTechType")
    private String phoneTechType;

    @XmlAttribute(name = "PhoneNumber")
    private String phoneNumber;

    public String getPhoneTechType() {
        return phoneTechType;
    }

    public void setPhoneTechType(String phoneTechType) {
        this.phoneTechType = phoneTechType;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
}
