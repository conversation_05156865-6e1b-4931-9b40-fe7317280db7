package com.fangcang.hotel.amadeusbld.dto.response.hotel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/22 12:08
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "CategoryCodes")
public class CategoryCodes {

    @XmlElement(name = "LocationCategory")
    private LocationCategory locationCategory;

    @XmlElement(name = "SegmentCategory")
    private SegmentCategory segmentCategory;


    @XmlElement(name = "HotelCategory")
    private HotelCategory hotelCategory;


    @XmlElement(name = "GuestRoomInfo")
    private List<GuestRoomInfo> guestRoomInfoList;

    public LocationCategory getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(LocationCategory locationCategory) {
        this.locationCategory = locationCategory;
    }

    public SegmentCategory getSegmentCategory() {
        return segmentCategory;
    }

    public void setSegmentCategory(SegmentCategory segmentCategory) {
        this.segmentCategory = segmentCategory;
    }

    public HotelCategory getHotelCategory() {
        return hotelCategory;
    }

    public void setHotelCategory(HotelCategory hotelCategory) {
        this.hotelCategory = hotelCategory;
    }

    public List<GuestRoomInfo> getGuestRoomInfoList() {
        return guestRoomInfoList;
    }

    public void setGuestRoomInfoList(List<GuestRoomInfo> guestRoomInfoList) {
        this.guestRoomInfoList = guestRoomInfoList;
    }
}
