package com.fangcang.hotel.common.aspect;

import com.fangcang.hotel.common.constants.CommonConstants;
import com.fangcang.hotel.framework.common.util.StrUtilX;
import com.fangcang.hotel.framework.mybatis.core.interceptor.DynamicTableNameInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.List;

/**
 * @description: 动态表名称 切面获取请求参数 中 supplyClass 并拼接
 * @author: qiu
 * @create: 2023-08-16 16:05
 */
@Aspect
@Component
@Slf4j
public class CommonAspect {






    @Around("@annotation(com.fangcang.hotel.common.annotations.SupplyClassTableName)")
    public Object mappingSetDynamicTableName(ProceedingJoinPoint joinPoint) throws Throwable {
        /**
         * 问题1：当业务流程复杂是会出现嵌套的业务层方法，并且每一层业务方法都加 @SupplyClassTableName注解。每次调用 @SupplyClassTableName注解方法都会往前线程ThreadLocal设置数据，
         *       并且数据是一样的。这样的步骤就是多余的。
         *       解决方法：
         *       在进来@SupplyClassTableName方法之前。先判断当前线程ThreadLocal是否存在数据，存在了数据就不用设置了，直接执行真正的业务方法。没有就设置
         *
         * 问题2：涉及@SupplyClassTableName注解方法嵌套调用。怎么确保从ThreadLocal移除数据，避免造成内存泄漏!
         *       移除时机要在第一个  @SupplyClassTableName注解 方法执行完成 再去移除数据？
         *
         *
         *    如下面: 此时现在有一个业务 涉及到方法有 A方法 里面有 B方法 里面 有C方法、这三个方法都添加了 @SupplyClassTableName注解
         *
         *    在执行方法A之前 会从当前线程 ThreadLocal 中 获取 SupplyClass， 此时进来 当前线程的 ThreadLocal 数据是肯定为null，所以进到 else里面 ，在else里面会把 SupplyClass 设置到当前线程 ThreadLocal 中，为方法A 设置一个 退出标志 isQuit = true ，然后执行方法A里面业务。
         *       进到方法A方法里面有个B方法：此时执行到B方法，在执行B办法之前有进到aop切面这里。这时也很和方法A一样从当前线程的 ThreadLocal 获取数据。此时的 ThreadLocal 是有数据的，因为在方法A设置有了。进到if 里面 为方法B 设置一个 退出标志 isQuit = false，然后执行B方法里面业务
         *          进到方法B业务里面 有个C方法：此时也是和方法B一样 ThreadLocal 有数据 ，为方法C设置一个 退出标志 isQuit = false ,然后执行方法C里面业务
         *              执行sql ...
         *          方法C执行完成了，判断方法C的 isQuit 变量， 此时 isQuit 变量值是 false，所以不会进到 if 里面去 ，不会移除当前线程 ThreadLocal 数据
         *       方法B执行完成了, 判断方法B的 isQuit 变量， 此时 isQuit 变量值是 false，所以不会进到 if 里面去 ，不会移除当前线程 ThreadLocal 数据
         *    方法A执行完成了, 判断方法A的 isQuit 变量， 此时 isQuit 变量值是 true，所以会进到 if 里面去 ，移除当前线程 ThreadLocal 数据，最后整个业务结束！
         *
         *
         *
         *   set ThreadLocal
         *   methodA start   isQuit = true
         *       methodB start isQuit = false
         *          methodC start isQuit = false
         *              .... start isQuit = false
         *                  执行sql
         *              .... end isQuit = false
         *          methodC end isQuit = false
         *       methodB end isQuit = false
         *   methodA end isQuit = true
         *   remove ThreadLocal
         *
         */
        boolean isQuit = true;

        Object proceed;
        try {
            //从当前线程ThreadLocal 获取数据
            String supplyClassThreadLocal = DynamicTableNameInterceptor.getSupplyClassThreadLocal();
            //当前整个业务是否结束标志

            //不为null 证明是嵌套方法调用
            if (StrUtilX.isNotEmpty(supplyClassThreadLocal)) {
                isQuit = false;
            } else {
                // 获取方法参数
                Object[] args = joinPoint.getArgs();
                boolean flag = false;
                for (Object arg : args) {
                    try {
                        if (arg instanceof List) {
                            List list = (List) arg;
                            if (list.size() > 0) {
                                Object o = list.get(0);
                                try {
                                    Field field = o.getClass().getDeclaredField(CommonConstants.DECLARED_FIELD_SUPPLY);
                                    field.setAccessible(true);
                                    String supplyClass = field.get(o).toString();
                                    DynamicTableNameInterceptor.setSupplyClassThreadLocal(supplyClass);
                                    //找到即结束
                                    break;
                                } catch (Exception e) {
                                    getSupperClass(flag, o, CommonConstants.COUNT);
                                }
                            }
                        } else if (arg instanceof Object) {
                            try {
                                //当前类不存在
                                Field field = arg.getClass().getDeclaredField(CommonConstants.DECLARED_FIELD_SUPPLY);
                                field.setAccessible(true);
                                String supplyClass = field.get(arg).toString();
                                DynamicTableNameInterceptor.setSupplyClassThreadLocal(supplyClass);
                                //找到即结束
                                break;
                            } catch (Exception e) {
                                getSupperClass(flag, arg, CommonConstants.COUNT);
                            }
                        }

                        if (flag) {
                            break;
                        }
                    } catch (Exception e) {
                        log.error("根据供应类型分表系统异常", e);
                    }
                }
            }

            //执行真正业务方法
            proceed = joinPoint.proceed();
        } finally {
            //是退出
            if (isQuit) {
                //移除当前线程的数据，避免造成内存泄漏
                DynamicTableNameInterceptor.removeSupplyClassThreadLocal();
            }

        }

        return proceed;
    }

    private void getSupperClass(Boolean flag, Object arg, Integer count) {
        try {
            Class<?> aClass = arg.getClass();
            for (Integer i = 0; i < count; i++) {
                aClass = aClass.getSuperclass();
                try {
                    String name = aClass.getName();
                    //如果是java.lang.Object则结束
                    if ("java.lang.Object".equals(name)) {
                        break;
                    }
                    Field field = aClass.getDeclaredField(CommonConstants.DECLARED_FIELD_SUPPLY);
                    if (field != null) {
                        flag = true;
                        field.setAccessible(true);
                        String supplyClass = field.get(arg).toString();
                        DynamicTableNameInterceptor.setSupplyClassThreadLocal(supplyClass);
                        break;
                    }
                } catch (Exception e) {
                    continue;
                }
            }
        } catch (Exception e) {
            log.error("根据供应类型分表系统异常", e);
        }
    }

}