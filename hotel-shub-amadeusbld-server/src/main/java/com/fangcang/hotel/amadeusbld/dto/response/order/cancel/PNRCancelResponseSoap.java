package com.fangcang.hotel.amadeusbld.dto.response.order.cancel;

import com.fangcang.hotel.amadeusbld.dto.response.common.ResponseHeader;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/21 10:18
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "soapenv:Envelope")
public class PNRCancelResponseSoap {

    @XmlAttribute(name = "xmlns:soap")
    private String xmlnsSoap = "http://schemas.xmlsoap.org/soap/envelope/";

    @XmlAttribute(name = "xmlns:awsse")
    private String xmlnsAwsse = "http://xml.amadeus.com/2010/06/Session_v3";

    @XmlAttribute(name = "xmlns:wsa")
    private String xmlnsWsa = "http://www.w3.org/2005/08/addressing";

    @XmlElement(name = "soapenv:Header")
    private ResponseHeader responseHeader;

    @XmlElement(name = "soapenv:Body")
    private PNRCancelResponseBody pnrCancelResponseBody;

    public PNRCancelResponseBody getPnrCancelResponseBody() {
        return pnrCancelResponseBody;
    }

    public void setPnrCancelResponseBody(PNRCancelResponseBody pnrCancelResponseBody) {
        this.pnrCancelResponseBody = pnrCancelResponseBody;
    }

    public ResponseHeader getResponseHeader() {
        return responseHeader;
    }

    public void setResponseHeader(ResponseHeader responseHeader) {
        this.responseHeader = responseHeader;
    }
}
