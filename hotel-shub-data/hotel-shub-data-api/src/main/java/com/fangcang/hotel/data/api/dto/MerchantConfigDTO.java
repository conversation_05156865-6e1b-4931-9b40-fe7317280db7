package com.fangcang.hotel.data.api.dto;

import lombok.Data;

/**
 * @description:
 * @author: qiu
 * @create: 2023-10-21 13:42
 */
@Data
public class MerchantConfigDTO {
    /**
     * id
     */
    private Long id;

    /**
     * 商家来源
     */
    private String merchantSource;

    /**
     * 商家编码
     */
    private String merchantCode;

    /**
     * 商家简称
     */
    private String merchantShortName;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 渠道公钥
     */
    private String channelPublicKey;

    /**
     * 订单回调地址
     */
    private String orderStatusPushUrl;

    /**
     * 状态 [有效,无效]
     */
    private Integer status;

    /**
     * 是否推送起价 [0:否  1:是]
     */
    private Integer isPushMinimumPrice;

    /**
     * 推送起价地址
     */
    private String pushMinimumPriceUrl;

    /**
     * 是否落地起价 [0:否  1:是]
     */
    private Integer isLandedMinimumPrice;
}