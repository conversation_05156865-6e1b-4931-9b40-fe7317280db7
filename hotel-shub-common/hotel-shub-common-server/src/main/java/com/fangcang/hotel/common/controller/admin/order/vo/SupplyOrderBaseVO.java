package com.fangcang.hotel.common.controller.admin.order.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.fangcang.hotel.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.fangcang.hotel.framework.common.util.DateUtilX.FORMAT_yyyy_MM_dd;

/**
 * 供应商订单 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class SupplyOrderBaseVO {

    /** id */
    private Long id;

    /** 商家订单号 */
    @NotEmpty(message = "商家订单号不能为空")
    private String merchantOrderCode;

    /** 供应商订单号 */
    private String supplyOrderCode;

    /** 供应商确认号 */
    private String supplyConfirmNo;

    /** 供应商订单状态 */
    @NotEmpty(message = "供应商订单状态不能为空")
    private String supplyOrderStatus;

    /** 订单状态 SHub 订单确认结果 */
    @NotNull(message = "订单状态 SHub 订单确认结果不能为空")
    private Integer orderStatus;

    /** 供应类型 */
    @NotEmpty(message = "供应类型不能为空")
    private String supplyClass;

    /** 供应商编码 */
    @NotEmpty(message = "供应商编码不能为空")
    private String supplyCode;

    /** 入住时间 */
    @NotNull(message = "入住时间不能为空")
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate checkInDate;

    /** 离店时间 */
    @NotNull(message = "离店时间不能为空")
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    private LocalDate checkOutDate;

    /** 订单总金额 */
    @NotNull(message = "订单总金额不能为空")
    private BigDecimal totalOrderAmount;

    /** 到店另付金额 */
    private BigDecimal payInStorePrice;

    /** 到店另付币种 */
    private Integer payInStoreCurrency;
    /** 币种 */
    @NotNull(message = "币种不能为空")
    private Integer currency;

    /** 房间数量 */
    @NotNull(message = "房间数量不能为空")
    private Integer numberOfRooms;

    /** 价格计划ID */
    @NotEmpty(message = "价格计划ID不能为空")
    private String supplyRateId;

    /** 入住人 */
    @NotEmpty(message = "入住人不能为空")
    private String guests;

    /** 供应商酒店ID */
    @NotEmpty(message = "供应商酒店ID不能为空")
    private String spHotelId;

    /** 商家来源 SAAS 还是 飞天 */
    @NotEmpty(message = "商家来源 SAAS 还是 飞天不能为空")
    private String merchantSource;

    /** 商家编码 */
    @NotEmpty(message = "商家编码不能为空")
    private String merchantCode;

    /** 酒店id */
    @NotEmpty(message = "酒店id不能为空")
    private String hotelId;

    /** 供应商房型ID */
    @NotEmpty(message = "供应商房型ID不能为空")
    private String spRoomId;

    /** 房型ID */
    @NotEmpty(message = "房型ID不能为空")
    private String roomId;

    /** 儿童年龄 */
    private String childAge;

    /** 成人数 */
    private Integer adultNum;

    /** 担保类型 */
    private Integer guaranteeFlag;

    /** VIP订单 */
    private Integer vipOrder;

    /** 出行类型 */
    private Integer travelType;

    /** 退订费 */
    private BigDecimal refundPrice;

    /** 退订费币种 */
    private Integer refundCurrency;

    /**
     * 供应商返佣
     */
    private BigDecimal supplyShouldRackBack;

    /**
     * 供应奖励
     */
    private BigDecimal supplyReward;

    /** 状态 [有效,无效] */
    @NotNull(message = "状态 [有效,无效]不能为空")
    private Integer status;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

}
