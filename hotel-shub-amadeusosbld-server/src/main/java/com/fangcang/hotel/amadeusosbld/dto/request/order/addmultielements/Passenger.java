package com.fangcang.hotel.amadeusosbld.dto.request.order.addmultielements;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/28 17:03
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "passenger")
public class Passenger {

    @XmlElement(name = "firstName")
    private String firstName;

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }
}
