package com.fangcang.hotel.amadeusbld.dto.response.price;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlValue;

/**
 * <AUTHOR>
 * @date 2021/6/18 19:24
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Text")
public class RoomRateDescriptionText {

    @XmlAttribute(name = "Formatted")
    private String formatted;

    @XmlValue
    private String value;

    public String getFormatted() {
        return formatted;
    }

    public void setFormatted(String formatted) {
        this.formatted = formatted;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
