package com.fangcang.hotel.huizhiosbld.config;

import cn.hutool.core.collection.CollectionUtil;
import com.fangcang.hotel.core.util.StringUtilExtend;
import com.fangcang.hotel.data.api.enums.SupplyClassEnum;
import com.fangcang.hotel.data.base.services.CacheService;
import com.fangcang.hotel.huizhiosbld.constants.HUIZHIOSBLDConstants;
import com.fangcang.hotel.huizhiosbld.dto.request.GetCityRequest;
import com.fangcang.hotel.huizhiosbld.dto.response.*;
import com.fangcang.hotel.huizhiosbld.enums.ResponseCode;
import com.fangcang.hotel.huizhiosbld.manger.HUIZHIOSBLDManager;
import com.fangcang.hotel.huizhiosbld.util.LogPrintUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @Date 2024/11/15 16:41
 * @Description:
 */

@Component
public class LoadNationalityInfos implements ApplicationRunner {

    @Autowired
    private HUIZHIOSBLDConfig huizhiosbldConfig;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private LogPrintUtil logPrintUtil;

    @Autowired
    private HUIZHIOSBLDManager huizhiosbldManager;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        CompletableFuture.runAsync(() -> {
            try {
                //获取供应商配置
                HUIZHIOSBLDExtendConfig extendConfig = (HUIZHIOSBLDExtendConfig) cacheService.getCacheConfigExtend(huizhiosbldConfig.getMerchantSource(),
                        huizhiosbldConfig.getMerchantCode(), SupplyClassEnum.HUIZHIOSBLD, huizhiosbldConfig.getSupplyCode(), HUIZHIOSBLDExtendConfig.class);
                if (extendConfig == null) {
                    logPrintUtil.simplePrintInfoLog("LoadNationalityInfos", "供应商配置信息为空");
                    return;
                }

                //先请求城市数据接口
                GetCityResponse getCityResponse = null;
                try {
                    GetCityRequest getCityRequest = GetCityRequest.builder().build();
                    getCityResponse = huizhiosbldManager.getCity(getCityRequest, extendConfig);
                } catch (Exception e) {
                    logPrintUtil.simplePrintErrorLog("LoadNationalityInfos", "getCity error:{}", e.getMessage());
                }

                if (getCityResponse == null
                        || !getCityResponse.getCode().equals(ResponseCode.SUCCESS.getValue())
                        || getCityResponse.getResult() == null
                        || CollectionUtil.isEmpty(getCityResponse.getResult().getCountrylist())) {
                    logPrintUtil.simplePrintInfoLog("LoadNationalityInfos", "getCityResponse is null");
                    return;
                }

                for (CountryListResponse countryListResponse : getCityResponse.getResult().getCountrylist()) {
                    if (StringUtilExtend.isValidString(countryListResponse.getCountrycode())
                            && StringUtilExtend.isValidString(countryListResponse.getNationality())) {
                        HUIZHIOSBLDConstants.nationalityMap.put(countryListResponse.getCountrycode(), countryListResponse.getNationality());
                    }
                }

                if (HUIZHIOSBLDConstants.nationalityMap.isEmpty()) {
                    logPrintUtil.simplePrintInfoLog("LoadNationalityInfos", "nationalityMap is null");
                } else {
                    logPrintUtil.simplePrintInfoLog("LoadNationalityInfos", "nationalityMap size is " + HUIZHIOSBLDConstants.nationalityMap.size());
                }
            } catch (Exception e) {
                logPrintUtil.simplePrintErrorLog("LoadNationalityInfos", "LoadNationalityInfos error:{}", e.getMessage());
            }

            try {
                if (StringUtilExtend.isValidString(HUIZHIOSBLDConstants.LAST_NAME)) {
                    HUIZHIOSBLDConstants.lastNameSet.addAll(Arrays.asList(HUIZHIOSBLDConstants.LAST_NAME.split(",")));
                } else {
                    logPrintUtil.simplePrintErrorLog("LoadNationalityInfos", "姓氏集合为空");
                }
            } catch (Exception e) {
                logPrintUtil.simplePrintErrorLog("LoadNationalityInfos", "初始化姓氏集合异常 error:{}", e.getMessage());
            }
        });
    }
}
