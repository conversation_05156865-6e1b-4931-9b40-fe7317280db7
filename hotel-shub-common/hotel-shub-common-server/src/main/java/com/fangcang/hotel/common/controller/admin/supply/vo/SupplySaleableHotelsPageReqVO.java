package com.fangcang.hotel.common.controller.admin.supply.vo;

import com.fangcang.hotel.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 可售酒店范围 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SupplySaleableHotelsPageReqVO extends PageParam {


    /** 商家编码 */
    private String merchantCode;

    /** 商家名称 */
    private String merchantName;

    /** 商家来源 */
    private String merchantSource;

    /** 供应类型 */
    private String supplyClass;

    /** 供应商编码 */
    private String supplyCode;

    /** 供应商名称 */
    private String supplyName;

    /** 数据范围 [0:指定酒店,1:所有酒店] */
    private Integer dataRange;

    /** 供应商酒店ID */
    private String spHotelId;

    /** 供应商酒店名称 */
    private String spHotelName;

}
