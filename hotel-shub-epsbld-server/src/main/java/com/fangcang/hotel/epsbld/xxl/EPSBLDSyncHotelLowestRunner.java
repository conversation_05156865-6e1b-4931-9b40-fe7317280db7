package com.fangcang.hotel.epsbld.xxl;

import com.fangcang.hotel.common.api.common.mapping.HotelMappingApi;
import com.fangcang.hotel.common.api.common.mapping.dto.HotelMappingPageRespDTO;
import com.fangcang.hotel.common.api.common.mapping.dto.HotelMappingQueryReqDTO;
import com.fangcang.hotel.common.api.common.supply.SupplyHotelLowestPriceApi;
import com.fangcang.hotel.common.api.common.supply.dto.SupplyHotelLowestPriceReqDTO;
import com.fangcang.hotel.data.api.dto.*;
import com.fangcang.hotel.data.api.enums.SupplyClassEnum;
import com.fangcang.hotel.data.api.enums.SupplyTypeEnum;
import com.fangcang.hotel.data.api.service.SupplyProductService;
import com.fangcang.hotel.data.base.services.RegisterSupplyService;
import com.fangcang.hotel.epsbld.constant.RedisCacheKeyConstants;
import com.fangcang.hotel.epsbld.properties.EPSBLDApplicationConfigProperties;
import com.fangcang.hotel.framework.common.domain.PageResult;
import com.fangcang.hotel.framework.common.domain.ResultX;
import com.fangcang.hotel.framework.common.util.CollUtilX;
import com.fangcang.hotel.framework.common.util.DateUtilX;
import com.fangcang.hotel.framework.redis.core.RedisTemplateX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 同步eps酒店起价
 */
@Slf4j
@Component
public class EPSBLDSyncHotelLowestRunner {

    @Autowired
    private EPSBLDApplicationConfigProperties epsbldApplicationConfigProperties;

    @Autowired
    private RegisterSupplyService registerSupplyService;

    @Autowired
    private HotelMappingApi hotelMappingApi;

    @Autowired
    private SupplyHotelLowestPriceApi supplyHotelLowestPriceApi;

    /**
     * 初始化起价酒店id到缓存 用于消费同步起价
     */
    @XxlJob("initHotelIdToCacheRunner")
    public void initHotelIdToCacheRunner() {
        try {
            XxlJobHelper.log("执行初始化起价酒店id到缓存任务开始");
            if (!RedisTemplateX.hasKey(RedisCacheKeyConstants.EPS_HOT_HOTEL_ID)) {
                RedisTemplateX.sUnionAndStore(RedisCacheKeyConstants.EPS_HOTEL_ID_LOWEST_PRICE, "tempkey", RedisCacheKeyConstants.EPS_HOT_HOTEL_ID);
            }
            XxlJobHelper.log("执行初始化起价酒店id到缓存任务结束");
        } catch (Exception e) {
            log.error("执行初始化起价酒店id到缓存任务异常", e);
            XxlJobHelper.log("执行初始化起价酒店id到缓存任务异常", e);
        }
    }

    /**
     * 同步起价
     */
    @XxlJob("syncHotelLowestRunner")
    public void syncHotelLowestRunner() {
        try {
            XxlJobHelper.log("执行同步起价任务开始");
            if (RedisTemplateX.hasKey(RedisCacheKeyConstants.EPS_HOT_HOTEL_ID)) {
                // 获取酒店ID
                List<String> spHotelIdList = RedisTemplateX.setPopCount(RedisCacheKeyConstants.EPS_HOT_HOTEL_ID, epsbldApplicationConfigProperties.getConsumerHotelIdCount());

                // 查询已映射供应商酒店ID
                HotelMappingQueryReqDTO hotelMappingQueryReqDTO = new HotelMappingQueryReqDTO();
                hotelMappingQueryReqDTO.setSupplyClass(SupplyClassEnum.EPSBLD.getSupplierClass());
                hotelMappingQueryReqDTO.setSpHotelIds(spHotelIdList);
                hotelMappingQueryReqDTO.setPage(false);
                ResultX<PageResult<HotelMappingPageRespDTO>> result = hotelMappingApi.querySpHotelIdHotelMappingPage(hotelMappingQueryReqDTO);

                if (result.isSuccess() && CollUtilX.isNotEmpty(result.getData().getList())) {
                    // 获取供应商产品服务
                    SupplyProductService supplyProductService = registerSupplyService.getSupplyProductService(SupplyClassEnum.EPSBLD.getSupplierClass());

                    // 跑起价的天数
                    Integer lowesPriceDay = epsbldApplicationConfigProperties.getLowesPriceDay();
                    Integer checkInDay = epsbldApplicationConfigProperties.getCheckInDay();
                    Integer dayTotal = epsbldApplicationConfigProperties.getDayTotal();
                    // 获取当期时间 + 1
                    LocalDate currentDate = LocalDate.now();

                    int num = lowesPriceDay % checkInDay == 0 ? lowesPriceDay / checkInDay : lowesPriceDay / checkInDay + 1;
                    // 构建查询产品参数
                    SupplyProductRequest supplyProductRequest = buildSupplyProductRequest();

                    // 遍历酒店ID
                    for (HotelMappingPageRespDTO hotelMappingPageRespDTO : result.getData().getList()) {
                        List<SupplyHotelLowestPriceReqDTO> supplyHotelLowestPriceReqDTOS = new ArrayList<>();
                        int count = 0;
                        String spHotelIdStr = hotelMappingPageRespDTO.getSpHotelIdAndHotelId();
                        String[] hotelIdArr = spHotelIdStr.split("-");
                        String spHotelId = hotelIdArr[0];
                        supplyProductRequest.setSpHotelId(spHotelId);

                        SupplyHotelLowestPriceReqDTO lastLowestPrice = null;

                        for (int i = 1; i <= num; i++) {
                            LocalDate checkInDate = currentDate.plusDays(count + 1);

                            int end = (count += checkInDay) + 1;
                            end = end > lowesPriceDay ? lowesPriceDay + 1 : end;
                            LocalDate checkoutDate = currentDate.plusDays(end);

                            supplyProductRequest.setCheckInDate(checkInDate.toString());
                            supplyProductRequest.setCheckOutDate(checkoutDate.toString());
                            XxlJobHelper.log("查询spHotelId：{},查询日期：{},{}", supplyProductRequest.getSpHotelId(), checkInDate, checkoutDate);
                            ResultX<SupplyProductResponse> responseResultX = supplyProductService.querySupplyProduct(supplyProductRequest);
                            XxlJobHelper.log("查询结果：{}", responseResultX.getData().getProductDetails().size());

                            // 请求成功并且返回产品价格不为null
                            if (!responseResultX.isSuccess() || CollUtilX.isEmpty(responseResultX.getData().getProductDetails())) {
                                break;
                            }
                            List<ProductMiddleDTO> productDetails = responseResultX.getData().getProductDetails();
                            ProductMiddleDTO productMiddleDto;
                            if (productDetails.size() == 1) {
                                productMiddleDto = productDetails.get(0);
                            } else {
                                // 找到最低价
                                productMiddleDto = productDetails.stream().min(Comparator.comparing(ProductMiddleDTO::getTotalAmount)).get();
                            }

                            // 证明是第一次查询的
                            List<ProductRoomIndexDetailDTO> roomIndexDetails = productMiddleDto.getRoomIndexDetails();
                            if (CollUtilX.isEmpty(roomIndexDetails) || CollUtilX.isEmpty(roomIndexDetails.get(0).getProductDetails())) {
                                // 证明是第一次查询 价格信息就为 null。那么直接结束这个酒店后续日期查询。
                                break;
                            }
                            List<ProductDetailMiddleDTO> productDetailsList = roomIndexDetails.get(0).getProductDetails();
                            for (ProductDetailMiddleDTO productDetail : productDetailsList) {
                                SupplyHotelLowestPriceReqDTO supplyHotelLowestPriceReqDTO = new SupplyHotelLowestPriceReqDTO();
                                BigDecimal lowestPrice = productDetail.getBasePrice();
                                supplyHotelLowestPriceReqDTO.setLowestPrice(lowestPrice);
                                supplyHotelLowestPriceReqDTO.setLowestPriceCurreny(productMiddleDto.getBaseCurrency());
                                supplyHotelLowestPriceReqDTO.setHotelId(hotelIdArr[1]);
                                supplyHotelLowestPriceReqDTO.setSaleDate(DateUtilX.strToLocalDate(productDetail.getSaleDate()));
                                supplyHotelLowestPriceReqDTO.setPayInStorePrice(productMiddleDto.getRoomIndexDetails().get(0).getPayInStorePrice());
                                supplyHotelLowestPriceReqDTO.setPayInStoreCurrency(productMiddleDto.getRoomIndexDetails().get(0).getPayInStoreCurrency());
                                supplyHotelLowestPriceReqDTO.setSupplyClass(SupplyClassEnum.EPSBLD.getSupplierClass());
                                supplyHotelLowestPriceReqDTO.setMerchantSource(epsbldApplicationConfigProperties.getMerchantSource());
                                supplyHotelLowestPriceReqDTO.setMerchantCode(epsbldApplicationConfigProperties.getMerchantCode());
                                supplyHotelLowestPriceReqDTO.setSupplyCode(epsbldApplicationConfigProperties.getSupplyCode());
                                supplyHotelLowestPriceReqDTO.setSupplyType(SupplyTypeEnum.DOMESTIC.getSupplyType());
                                supplyHotelLowestPriceReqDTO.setCreatedDt(LocalDateTime.now());
                                supplyHotelLowestPriceReqDTO.setCreatedBy("syncHotelLowestRunner");
                                supplyHotelLowestPriceReqDTO.setStatus(1);
                                lastLowestPrice = supplyHotelLowestPriceReqDTO;
                                supplyHotelLowestPriceReqDTOS.add(supplyHotelLowestPriceReqDTO);
                            }
                        }

                        XxlJobHelper.log("起价数据插入数量：{}", supplyHotelLowestPriceReqDTOS.size());
                        if (CollUtilX.isNotEmpty(supplyHotelLowestPriceReqDTOS) && lastLowestPrice != null) {
                            for (int i = supplyHotelLowestPriceReqDTOS.size(); i < dayTotal; i++) {
                                SupplyHotelLowestPriceReqDTO cpLowestPriceReqDTO = new SupplyHotelLowestPriceReqDTO();
                                BeanUtils.copyProperties(lastLowestPrice, cpLowestPriceReqDTO);
                                LocalDate localDate = currentDate.plusDays(i + 1);
                                cpLowestPriceReqDTO.setSaleDate(localDate);
                                supplyHotelLowestPriceReqDTOS.add(cpLowestPriceReqDTO);
                            }
                            // 落地起价
                            ResultX resultX = supplyHotelLowestPriceApi.hotelLowestPriceAdd(supplyHotelLowestPriceReqDTOS);
                        }
                    }
                }
            }
            XxlJobHelper.log("执行同步起价任务结束");
        } catch (Exception e) {
            log.error("执行同步起价任务异常", e);
            XxlJobHelper.log("执行同步起价任务异常", e);
        }
    }

    /**
     * 构建查询产品参数
     */
    private SupplyProductRequest buildSupplyProductRequest() {
        SupplyProductRequest supplyProductRequest = new SupplyProductRequest();
        supplyProductRequest.setMerchantSource(epsbldApplicationConfigProperties.getMerchantSource());
        supplyProductRequest.setSupplyCode(epsbldApplicationConfigProperties.getSupplyCode());
        supplyProductRequest.setMerchantCode(epsbldApplicationConfigProperties.getMerchantCode());
        RoomGuestNumberDTO roomGuestNumberDTO = new RoomGuestNumberDTO();
        roomGuestNumberDTO.setRoomIndex(1);
        roomGuestNumberDTO.setAdultNum(2);
        supplyProductRequest.setRoomGuestNumbers(Collections.singletonList(roomGuestNumberDTO));
        return supplyProductRequest;
    }

}
