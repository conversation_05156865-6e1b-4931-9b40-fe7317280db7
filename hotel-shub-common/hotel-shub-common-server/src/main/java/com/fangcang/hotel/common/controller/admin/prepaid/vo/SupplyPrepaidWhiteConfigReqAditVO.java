package com.fangcang.hotel.common.controller.admin.prepaid.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data

public class SupplyPrepaidWhiteConfigReqAditVO {

    /** 商家编码 */
    @NotNull(message = "商家编码不能为空")
    @ExcelProperty("商家编码")
    private String merchantCode;

    /** 商家名称 */
    private String merchantName;

    /** 商家来源 */
    @NotNull(message = "商家来源不能为空")
    @ExcelProperty("商家来源")
    private String merchantSource;

    //供应商类型
    @NotNull(message = "供应商类型不能为空")
    @ExcelProperty("供应商类型编码")
    private String supplyClass;

    //供应商编码
    @NotNull(message = "供应商编码不能为空")
    @ExcelProperty("供应商编码")
    private String supplyCode;

    //供应商酒店id，多个酒店英文逗号拼接
    @NotNull(message = "供应商酒店编码不能为空")
    @ExcelProperty("供应商酒店编码")
    private String addSpHotelIds;

    //操作人
    private String operator;

}
