package com.fangcang.hotel.ratehawkosbld.dto.entity;

import com.fangcang.hotel.data.api.dto.SupplyProductRequest;
import com.fangcang.hotel.ratehawkosbld.config.RateHawkOSBLDExtendConfig;
import com.fangcang.hotel.ratehawkosbld.dto.response.HotelPageResponse;
import lombok.Data;

@Data
public class DataConverterParam {

    //请求参数
    private SupplyProductRequest supplyProductRequest;

    /**
     * 酒店价格数据
     */
    private HotelPageResponse hotelPageResponse;

    /**
     * 扩展表参数
     */
    private RateHawkOSBLDExtendConfig rateHawkOSBLDExtendConfig;

}
