package com.fangcang.hotel.bookingosbld.dto.request.order.completereservationdetails;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/29 16:13
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "reservation")
public class ReservationCancel {

    @XmlElement(name = "companyId")
    private String companyId;

    @XmlElement(name = "controlNumber")
    private String controlNumber;

    @XmlElement(name = "controlType")
    private String controlType;

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getControlNumber() {
        return controlNumber;
    }

    public void setControlNumber(String controlNumber) {
        this.controlNumber = controlNumber;
    }

    public String getControlType() {
        return controlType;
    }

    public void setControlType(String controlType) {
        this.controlType = controlType;
    }
}
