package com.fangcang.hotel.bookingosbld.dto.response.order.addmultielements;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/7/1 11:49
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "hotelProduct")
public class HotelProduct {


    @XmlElement(name = "property")
    private Property property;

    @XmlElement(name = "hotelRoom")
    private HotelRoom hotelRoom;

    @XmlElement(name = "negotiated")
    private Negotiated negotiated;

    @XmlElement(name = "otherHotelInfo")
    private OtherHotelInfo otherHotelInfo;

    public Property getProperty() {
        return property;
    }

    public void setProperty(Property property) {
        this.property = property;
    }

    public HotelRoom getHotelRoom() {
        return hotelRoom;
    }

    public void setHotelRoom(HotelRoom hotelRoom) {
        this.hotelRoom = hotelRoom;
    }

    public Negotiated getNegotiated() {
        return negotiated;
    }

    public void setNegotiated(Negotiated negotiated) {
        this.negotiated = negotiated;
    }

    public OtherHotelInfo getOtherHotelInfo() {
        return otherHotelInfo;
    }

    public void setOtherHotelInfo(OtherHotelInfo otherHotelInfo) {
        this.otherHotelInfo = otherHotelInfo;
    }
}
