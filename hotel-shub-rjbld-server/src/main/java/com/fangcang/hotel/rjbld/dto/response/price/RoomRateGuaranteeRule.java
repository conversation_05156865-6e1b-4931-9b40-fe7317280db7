package com.fangcang.hotel.rjbld.dto.response.price;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  担保详情
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:14
 */
@Data
public class RoomRateGuaranteeRule implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 房型编号
     */
    @JSONField(name = "RoomTypeCode")
    private String roomTypeCode;

    /**
     * 价格码
     */
    @JSONField(name = "RateCode")
    private String rateCode;

    /**
     * 促销ID
     */
    @JSONField(name = "ActCd")
    private String actCd;

    /**
     * 策略代码
     */
    @JSONField(name = "GuaranteeRuleCode")
    private String guaranteeRuleCode;

    /**
     * 策略描述
     */
    @JSONField(name = "GuaranteeRuleName")
    private String guaranteeRuleName;

    /**
     * 需要到达时间
     */
    @JSONField(name = "ArrivalTimeRequired")
    private Integer arrivalTimeRequired;

    /**
     * 最晚保留时间HH:mm
     */
    @JSONField(name = "HoldTime")
    private String holdTime;

    /**
     * 是否需要预付
     */
    @JSONField(name = "IsPrepay")
    private Integer isPrepay;

    /**
     * 取消规则
     */
    @JSONField(name = "RoomRateCancelRule")
    private RoomRateCancelRule roomRateCancelRule;

    /**
     * 订金规则
     */
    @JSONField(name = "RoomRateDepositRule")
    private RoomRateDepositRule roomRateDepositRule;

}
