package com.fangcang.hotel.forward.dynamic;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClientBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * @description: 动态服务
 * @author: qiu
 * @create: 2023-07-28 16:58
 */
@Component
@SuppressWarnings("all")
public class DyServernamicFeignClient<T> {

    private FeignClientBuilder feignClientBuilder;

    public DyServernamicFeignClient(@Autowired ApplicationContext appContext) {
        this.feignClientBuilder = new FeignClientBuilder(appContext);
    }


    /**
     * 降级机制
     *
     * @param interFegin  接口
     * @param serviceName 服务名称
     * @param fallback    降级回调
     * @return
     */
    public T getFeignClient(final Class<T> interFegin, String serviceName, final Class fallbackFactory) {
        return (T) this.feignClientBuilder.forType(interFegin, serviceName).fallback(fallbackFactory).build();
    }


}