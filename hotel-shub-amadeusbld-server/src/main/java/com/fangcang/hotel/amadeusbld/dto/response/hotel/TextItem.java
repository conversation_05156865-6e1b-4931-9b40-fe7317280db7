package com.fangcang.hotel.amadeusbld.dto.response.hotel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/22 12:17
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "TextItem")
public class TextItem {

    @XmlElement(name = "Description")
    private Description description;

    public void setDescription(Description description) {
        this.description = description;
    }
}
