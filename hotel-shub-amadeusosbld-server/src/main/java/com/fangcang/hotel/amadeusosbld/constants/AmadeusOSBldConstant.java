package com.fangcang.hotel.amadeusosbld.constants;

public class AmadeusOSBldConstant {
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    public static final String DATE_FORMAT2 = "yyyy-MM-dd HH:mm:ss";

    public static final String COLON = ":";

    /**
     * 连续入住单位
     */
    public static String CHECK_IN_UNIT = "DAY";

    /**
     * 币种
     */
    public static String CURRENCY_CODE = "CNY";

    /**
     * 付款方式
     */
    public static String PRE_PAYMENT = "YF";

    /**
     * 付款方式
     */
    public static String ARRIVE_PAYMENT = "DDF";

    /**
     * 付款名称
     */
    public static String PAYMENT_NAME = "(到店付)";

    /**
     * 是否需要到店付产品 ,1需要；其它不需要
     */
    public static String NEEDPAYATHOTELPRODUCT = "1";

    /**
     * 是否需要到预付产品 ,1需要；其它不需要
     */
    public static String NEEDPREPAIDPRODUCT = "1";

//    /**
//     * 单人价
//     */
//    public static String SINGLEPRICE = "SP";
//
//    /**
//     * 双人价
//     */
//    public static String DOUBLEPRICE = "DP";

    /**
     * 发单方式：1-即时确认
     */
    public static String SEND_ORDER_TYPE_1 = "1";

    /**
     * 发单方式：2-非即时确认，非即时确认方式默认返回saas待确认，独立任务去下单回写已确认状态
     */
    public static String SEND_ORDER_TYPE_2 = "2";

    public static String CREATE_ORDER_TIME_OUT = "调用供应商下单接口超时，请去GDS后台确认订单是否已生成";

    public static final String UNDERLINE = "_";

    //翻译接口地址
    public static final String TRANSLATION_URL = "translations/hotel/roominfo";

    //房型聚合接口
    public static final String ROOMAGGREGATION_URL = "translations/hotel/matchedroom";
}
