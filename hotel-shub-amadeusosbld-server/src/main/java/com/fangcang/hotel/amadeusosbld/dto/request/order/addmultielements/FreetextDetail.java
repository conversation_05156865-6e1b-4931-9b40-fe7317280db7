package com.fangcang.hotel.amadeusosbld.dto.request.order.addmultielements;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/28 17:15
 */


@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "freetextDetail")
public class FreetextDetail {

    @XmlElement(name = "subjectQualifier")
    private String subjectQualifier;

    @XmlElement(name = "type")
    private String type;

    public String getSubjectQualifier() {
        return subjectQualifier;
    }

    public void setSubjectQualifier(String subjectQualifier) {
        this.subjectQualifier = subjectQualifier;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
