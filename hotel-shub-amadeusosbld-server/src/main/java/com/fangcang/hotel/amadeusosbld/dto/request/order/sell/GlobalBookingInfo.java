package com.fangcang.hotel.amadeusosbld.dto.request.order.sell;


import com.fangcang.hotel.amadeusosbld.dto.response.order.addmultielements.BookingSource;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/29 16:51
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "globalBookingInfo")
public class GlobalBookingInfo {

    @XmlElement(name = "markerGlobalBookingInfo")
    private MarkerGlobalBookingInfo markerGlobalBookingInfo;

    @XmlElement(name = "bookingSource")
    private BookingSource bookingSource;

    @XmlElement(name = "representativeParties")
    private List<RepresentativeParties> representativePartiesList;

    public MarkerGlobalBookingInfo getMarkerGlobalBookingInfo() {
        return markerGlobalBookingInfo;
    }

    public void setMarkerGlobalBookingInfo(MarkerGlobalBookingInfo markerGlobalBookingInfo) {
        this.markerGlobalBookingInfo = markerGlobalBookingInfo;
    }

    public List<RepresentativeParties> getRepresentativePartiesList() {
        return representativePartiesList;
    }

    public void setRepresentativePartiesList(List<RepresentativeParties> representativePartiesList) {
        this.representativePartiesList = representativePartiesList;
    }

    public BookingSource getBookingSource() {
        return bookingSource;
    }

    public void setBookingSource(BookingSource bookingSource) {
        this.bookingSource = bookingSource;
    }
}
