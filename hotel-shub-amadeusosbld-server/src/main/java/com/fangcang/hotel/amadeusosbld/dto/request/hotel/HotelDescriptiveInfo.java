package com.fangcang.hotel.amadeusosbld.dto.request.hotel;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/22 10:34
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "HotelDescriptiveInfo")
public class HotelDescriptiveInfo {

    @XmlAttribute(name = "HotelCode")
    private String hotelCode;

    @XmlElement(name = "HotelInfo")
    private HotelInfo hotelInfo;

    @XmlElement(name = "FacilityInfo")
    private FacilityInfo facilityInfo;

    @XmlElement(name = "Policies")
    private Policies policies;

    @XmlElement(name = "AreaInfo")
    private AreaInfo areaInfo;

    @XmlElement(name = "AffiliationInfo")
    private AffiliationInfo affiliationInfo;

    @XmlElement(name = "ContactInfo")
    private ContactInfo contactInfo;

    @XmlElement(name = "MultimediaObjects")
    private MultimediaObjects multimediaObjects;

    @XmlElementWrapper(name = "ContentInfos")
    @XmlElement(name = "ContentInfo")
    private List<ContentInfo> contentInfoList;

    public String getHotelCode() {
        return hotelCode;
    }

    public void setHotelCode(String hotelCode) {
        this.hotelCode = hotelCode;
    }

    public HotelInfo getHotelInfo() {
        return hotelInfo;
    }

    public void setHotelInfo(HotelInfo hotelInfo) {
        this.hotelInfo = hotelInfo;
    }

    public FacilityInfo getFacilityInfo() {
        return facilityInfo;
    }

    public void setFacilityInfo(FacilityInfo facilityInfo) {
        this.facilityInfo = facilityInfo;
    }

    public Policies getPolicies() {
        return policies;
    }

    public void setPolicies(Policies policies) {
        this.policies = policies;
    }

    public AreaInfo getAreaInfo() {
        return areaInfo;
    }

    public void setAreaInfo(AreaInfo areaInfo) {
        this.areaInfo = areaInfo;
    }

    public AffiliationInfo getAffiliationInfo() {
        return affiliationInfo;
    }

    public void setAffiliationInfo(AffiliationInfo affiliationInfo) {
        this.affiliationInfo = affiliationInfo;
    }

    public ContactInfo getContactInfo() {
        return contactInfo;
    }

    public void setContactInfo(ContactInfo contactInfo) {
        this.contactInfo = contactInfo;
    }

    public MultimediaObjects getMultimediaObjects() {
        return multimediaObjects;
    }

    public void setMultimediaObjects(MultimediaObjects multimediaObjects) {
        this.multimediaObjects = multimediaObjects;
    }

    public List<ContentInfo> getContentInfoList() {
        return contentInfoList;
    }

    public void setContentInfoList(List<ContentInfo> contentInfoList) {
        this.contentInfoList = contentInfoList;
    }
}
