package com.fangcang.hotel.rjbld.dto.request;


import com.fangcang.hotel.rjbld.dto.request.BaseRequest;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  查询酒店图片信息请求参数
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:14
 */
@Data
public class GetHotelRmMCRequest extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 酒店编码
     */
    @JSONField(name = "HotelCd")
    private String hotelCd;

}
