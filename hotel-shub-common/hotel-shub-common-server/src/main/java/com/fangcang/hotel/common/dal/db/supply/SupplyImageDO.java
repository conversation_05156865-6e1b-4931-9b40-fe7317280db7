package com.fangcang.hotel.common.dal.db.supply;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.hotel.core.pojo.BaseDOX;
import com.fangcang.hotel.framework.mybatis.core.pojo.BaseDO;
import lombok.*;

/**
 * 供应商图片信息 DO
 *
 * <AUTHOR>
 */
@TableName("t_supply_image")
@KeySequence("t_supply_image_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplyImageDO extends BaseDOX {

    /** id */
    @TableId
    private Long id;

    /** 供应商酒店ID */
    private String spHotelId;

    /** 供应商房型ID */
    private String spRoomId;

    /** 图片类型 [餐厅,休闲,会议室,外观,大堂/接待台,客房,其他,公共区域,周边景点] */
    private String imageType;

    /** 图片地址 */
    private String imageUrl;

    /** 供应商类型 */
    private String supplyClass;

    /** 状态 [有效,无效] */
    private Integer status;


}
