package com.fangcang.hotel.heytripgoosbld.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025/3/3 13:53
 * @Description:
 */

@RefreshScope
@Component
@Data
@ConfigurationProperties(prefix = "heytripgoosbld.application.config")
public class HEYTRIPGOOSBLDConfig {

    /**
     * 请求超时（毫秒）
     */
    private Integer connectTimeOut = 5000;

    /**
     * 响应超时（毫秒）
     */
    private Integer socketTimeOut = 20000;

    /**
     * 商家来源
     */
    private String merchantSource;

    /**
     * 商家编码
     */
    private String merchantCode;

    /**
     * 供应商编码
     */
    private String supplyCode;


    /**
     * 一次消费酒店数
     */
    private Integer consumeHotelIdNumber;

    /**
     * 一次消费的酒店或房型数
     */
    private Integer consumeLoadInfosNumber;

    /**
     * 连接超时次数
     */
    private Integer connectTimeOutRetrySize = 3;

    /**
     * 道旅请求地址前缀
     */
    private String baseUrl;

    /**
     * 是否开启调式模式，0--否，1--是。默认0--关闭
     */
    private Integer isDebug;

    /**
     * 用户名
     */
    private Integer username;

    /**
     * 密码
     */
    private Integer password;

    private String forwardOrDirect;

    private String forwardUrl;

    /**
     * 消费酒店ID数量
     */
    private Integer consumerHotelIdCount;

    /**
     * 跑起价天数
     */
    private Integer lowesPriceDay;

    /**
     * 总天数
     */
    private Integer dayTotal;

    /**
     * 推送酒店ID秘钥
     */
    private String secretKey;

    /**
     * 价格计划名称
     */
    private String spPricePlanName;

    /**
     * 订单发送方式 1 同步 2异步
     */
    private Integer sendOrderType;

    /**
     * 每一批次的天数
     */
    private Integer daysPerBatch;

    /**
     * 是否打印日志
     * 0:不打印
     * 1:打印
     */
    private Integer printLog = 0;

    /**
     * 订单发送超时时间
     */
    private Integer sendOrderTimeOut = 60000;

    /**
     * 落地酒店信息超时时间
     */
    private Integer loadHotelInfosTimeOut = 60000;


    /**
     * 是否需要过滤掉中国大陆的资源
     * 0:不需要
     * 1:需要
     */
    private Integer isNeedFilterCN = 0;

    /**
     * 批量落地酒店起价的数量
     */
    private Integer batchLoadHotelLowestPriceNum = 300;

}
