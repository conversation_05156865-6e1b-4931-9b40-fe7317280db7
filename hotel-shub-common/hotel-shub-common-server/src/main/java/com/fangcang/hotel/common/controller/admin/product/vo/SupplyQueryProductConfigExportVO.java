package com.fangcang.hotel.common.controller.admin.product.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @program: supply-hub
 * @ClassName SupplyQueryProductConfigExportVO
 * @description:
 * @author: 湫
 * @create: 2024/11/05/ 14:03
 * @Version 1.0
 **/

@Data
public class SupplyQueryProductConfigExportVO {

    /**
     * 供应商编码
     */
    @ExcelProperty(value = "供应商编码", index = 0)
    private String supplyCode;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称", index = 1)
    private String supplyName;

    /**
     * 商家编码
     */
    @ExcelProperty(value = "商家编码", index = 2)
    private String merchantCode;

    /**
     * 商家名称
     */
    @ExcelProperty(value = "商家名称", index = 3)
    private String merchantName;

    /**
     * 商家来源
     */
    @ExcelProperty(value = "商家来源", index = 4)
    private String merchantSource;

    /**
     * 供应商酒店id
     */
    @ExcelProperty(value = "供应商酒店编码", index = 5)
    private String spHotelId;

    /**
     * 房仓酒店名称
     */
    @ExcelProperty(value = "酒店名称", index = 6)
    private String hotelName;

    /**
     * 供应商类别
     */
    @ExcelProperty(value = "供应商类型编码", index = 7)
    private String supplyClass;


}
