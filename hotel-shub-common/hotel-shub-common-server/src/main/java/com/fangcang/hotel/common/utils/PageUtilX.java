package com.fangcang.hotel.common.utils;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/11/4 12:33
 */
public class PageUtilX {

    public static <T> Integer getTotalPage(List<T> list) {
        int totalCount = list.size();
        int pageSize = 2000;
        int totalPage = totalCount/2000;
        if(totalCount != totalPage*pageSize){
            totalPage ++;
        }
        return totalPage;
    }

    public static <T> Integer getTotalPage(List<T> list,Integer pageSize) {
        int totalCount = list.size();
        int totalPage = totalCount/pageSize;
        if(totalCount != totalPage*pageSize){
            totalPage ++;
        }
        return totalPage;
    }

    public static <T> List<T> page(List<T> list, int pageIndex) {
        int totalCount = list.size();
        int pageSize = 2000;

        int pageStartIndex = (pageIndex-1)*pageSize;
        int pageEndIndex = pageIndex*pageSize;
        if(pageEndIndex > totalCount) {
            pageEndIndex = totalCount;
        }
        return list.subList(pageStartIndex, pageEndIndex);
    }

    /**
     * 截取集合数据
     *
     * @param list
     * @param pageIndex 截取的批次，从1开始
     * @param pageSize 截取的数据大小
     * @return
     * @param <T>
     */
    public static <T> List<T> page(List<T> list, int pageIndex, int pageSize) {
        int totalCount = list.size();

        int pageStartIndex = (pageIndex-1)*pageSize;
        int pageEndIndex = pageIndex*pageSize;
        if(pageEndIndex > totalCount) {
            pageEndIndex = totalCount;
        }
        return list.subList(pageStartIndex, pageEndIndex);
    }
}
