package com.fangcang.hotel.common.api.common.mapping.dto;

import com.fangcang.hotel.common.api.common.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description: 房型映射DTO
 * @author: qiu
 * @create: 2023-08-18 14:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoomMappingReqDTO extends BaseDTO {

    private Long id;

    /** 供应商房型ID */
    private String spRoomId;

    /** 供应商房型ID列表 */
    private List<String> spRoomIds;

    /** 房型ID */
    private String roomId;

    /** 酒店id */
    private String hotelId;

    /** 供应商酒店ID */
    private String spHotelId;

    /** 供应商酒店ID列表 */
    private List<String> spHotelIds;

    /** 供应商房型名称 */
    private String spRoomName;

    private String spRoomNameEn;

    /** 房型名称 */
    private String roomName;

    private String roomNameEn;

    /** 供应类型 */
    @NotNull(message = "供应类型不能为空")
    private String supplyClass;

}