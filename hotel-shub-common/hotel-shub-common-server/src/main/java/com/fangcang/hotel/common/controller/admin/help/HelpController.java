package com.fangcang.hotel.common.controller.admin.help;

import cn.hutool.core.util.StrUtil;
import com.fangcang.hotel.common.config.CommonConfig;
import com.fangcang.hotel.common.controller.admin.config.vo.ConfigExtendQueryReqVO;
import com.fangcang.hotel.common.controller.admin.config.vo.ConfigInfoQueryReqVO;
import com.fangcang.hotel.common.controller.admin.config.vo.MerchantConfigQueryReqVO;
import com.fangcang.hotel.common.controller.admin.config.vo.SupplyShareConfigQueryReqVO;
import com.fangcang.hotel.common.controller.admin.supply.vo.SupplyInfoQueryReqVO;
import com.fangcang.hotel.common.dal.db.config.ConfigExtendDO;
import com.fangcang.hotel.common.dal.db.config.ConfigInfoDO;
import com.fangcang.hotel.common.dal.db.config.MerchantConfigDO;
import com.fangcang.hotel.common.dal.db.config.SupplyShareConfigDO;
import com.fangcang.hotel.common.dal.db.supply.SupplyInfoDO;
import com.fangcang.hotel.common.service.InitServiceImpl;
import com.fangcang.hotel.common.service.config.ConfigExtendService;
import com.fangcang.hotel.common.service.config.ConfigInfoService;
import com.fangcang.hotel.common.service.config.MerchantConfigService;
import com.fangcang.hotel.common.service.config.SupplyShareConfigService;
import com.fangcang.hotel.common.service.mapping.InactiveService;
import com.fangcang.hotel.common.service.supply.SupplyInfoService;
import com.fangcang.hotel.data.api.constants.EsConstants;
import com.fangcang.hotel.data.api.enums.MerchantSourceEnum;
import com.fangcang.hotel.data.api.enums.SupplyClassEnum;
import com.fangcang.hotel.framework.common.domain.ResultX;
import com.fangcang.hotel.core.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.List;

import static com.fangcang.hotel.framework.common.domain.ResultX.success;

/**
 * @description:
 * @author: qiu
 * @create: 2023-12-15 16:35
 */
@RestController
@RequestMapping("/help")
@Validated
@Slf4j
public class HelpController {

    @Resource
    private ConfigInfoService configInfoService;

    @Resource
    private ConfigExtendService configExtendService;

    @Autowired
    private InitServiceImpl initService;

    @Autowired
    private InactiveService inactiveService;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private SupplyInfoService supplyInfoService;

    @Autowired
    private SupplyShareConfigService supplyShareConfigService;

    @Autowired
    private MerchantConfigService merchantConfigService;



    @GetMapping("/loadConfigInfoToCache")
    public ResultX loadConfigInfoToCache(String merchantSource) {
        ConfigInfoQueryReqVO reqVO = new ConfigInfoQueryReqVO();
        if (StrUtil.isNotBlank(merchantSource)) {
            reqVO.setMerchantSource(merchantSource);
        }
        MerchantSourceEnum merchantSourceEnum = MerchantSourceEnum.getMerchantSourceEnumByCode(reqVO.getMerchantSource());
        if(merchantSourceEnum == null){
            return success("商家来源不存在");
        }

        //加载指定商家来源的配置信息
        if (reqVO.getMerchantSource() != null) {
            List<ConfigInfoDO> list = configInfoService.configInfoList(reqVO);
            inactiveService.loadAllConfigInfoToRedis(reqVO.getMerchantSource(), list);

            inactiveService.loadAllSupplyConfigToRedis(reqVO.getMerchantSource(), list);

            inactiveService.loadAllSupplyClassAndCodeToRedis(reqVO.getMerchantSource(), list);

            SupplyShareConfigQueryReqVO supplyShareConfigQueryReqVO = new SupplyShareConfigQueryReqVO();
            supplyShareConfigQueryReqVO.setProviderMerhcantSource(reqVO.getMerchantSource());
            supplyShareConfigQueryReqVO.setStatus(StatusEnum.ACTIVE.getValue());
            List<SupplyShareConfigDO> supplyShareConfigDOS = supplyShareConfigService.supplyShareConfigList(supplyShareConfigQueryReqVO);
            inactiveService.loadAllShareConfigToRedis(reqVO.getMerchantSource(),supplyShareConfigDOS);


            MerchantConfigQueryReqVO merchantConfigQueryReqVO = new MerchantConfigQueryReqVO();
            merchantConfigQueryReqVO.setMerchantSource(reqVO.getMerchantSource());
            merchantConfigQueryReqVO.setStatus(StatusEnum.ACTIVE.getValue());
            List<MerchantConfigDO> listResultX = merchantConfigService.merchantConfigList(merchantConfigQueryReqVO);
            inactiveService.loadAllMerchantConfigToRedis(reqVO.getMerchantSource(),listResultX);
        } else {
            //加载所有商家来源的配置信息
            for (MerchantSourceEnum value : MerchantSourceEnum.values()) {
                reqVO.setMerchantSource(value.getCode());
                List<ConfigInfoDO> list = configInfoService.configInfoList(reqVO);
                inactiveService.loadAllConfigInfoToRedis(value.getCode(), list);

                inactiveService.loadAllSupplyConfigToRedis(value.getCode(), list);

                inactiveService.loadAllSupplyClassAndCodeToRedis(value.getCode(), list);

                SupplyShareConfigQueryReqVO supplyShareConfigQueryReqVO = new SupplyShareConfigQueryReqVO();
                supplyShareConfigQueryReqVO.setProviderMerhcantSource(value.getCode());
                supplyShareConfigQueryReqVO.setStatus(StatusEnum.ACTIVE.getValue());
                List<SupplyShareConfigDO> supplyShareConfigDOS = supplyShareConfigService.supplyShareConfigList(supplyShareConfigQueryReqVO);
                inactiveService.loadAllShareConfigToRedis(value.getCode(),supplyShareConfigDOS);


                MerchantConfigQueryReqVO merchantConfigReqDTO = new MerchantConfigQueryReqVO();
                merchantConfigReqDTO.setMerchantSource(value.getCode());
                merchantConfigReqDTO.setStatus(StatusEnum.ACTIVE.getValue());
                List<MerchantConfigDO> listResultX = merchantConfigService.merchantConfigList(merchantConfigReqDTO);
                inactiveService.loadAllMerchantConfigToRedis(value.getCode(),listResultX);
            }
        }
        return success();
    }


    @GetMapping("/loadSupplyInfoToCache")
    public ResultX loadSupplyInfoToCache() {
        SupplyInfoQueryReqVO supplyInfoQueryReqVO = new SupplyInfoQueryReqVO();
        supplyInfoQueryReqVO.setStatus(StatusEnum.ACTIVE.getValue());
        List<SupplyInfoDO> supplyInfoDOList = supplyInfoService.supplyInfoList(supplyInfoQueryReqVO);
        inactiveService.loadAllSupplyInfoToRedis(supplyInfoDOList);
        return success();
    }


    @GetMapping("/loadConfigExtendToCache")
    public ResultX loadConfigExtendToCache(String merchantSource) {
        ConfigExtendQueryReqVO reqVO = new ConfigExtendQueryReqVO();
        if (StrUtil.isNotBlank(merchantSource)) {
            reqVO.setMerchantSource(merchantSource);
        }
        MerchantSourceEnum merchantSourceEnum = MerchantSourceEnum.getMerchantSourceEnumByCode(reqVO.getMerchantSource());
        if(merchantSourceEnum == null){
            return success("商家来源不存在");
        }

        if (StrUtil.isNotEmpty(reqVO.getMerchantSource())) {
            //重新加载缓存 供应商配置
            //查询
            List<ConfigExtendDO> list = configExtendService.configExtendList(reqVO);
            inactiveService.loadAllConfigExtendToRedis(reqVO.getMerchantSource(), list);
        } else {
            for (MerchantSourceEnum value : MerchantSourceEnum.values()) {
                reqVO.setMerchantSource(value.getCode());
                List<ConfigExtendDO> list = configExtendService.configExtendList(reqVO);
                inactiveService.loadAllConfigExtendToRedis(value.getCode(), list);
            }
        }
        return success();
    }

    /**
     * 从数据库加载指定供应商映射信息到redis
     *
     * @param supplyClass 供应商类型
     * @return 结果
     */
    @GetMapping("/loadSupplyClassMappingToCache")
    public ResultX loadSupplyClassMappingToCache(String supplyClass) {
        try {
            if (StrUtil.isBlank(supplyClass)) {
                return success("supplyClass 不可为空");
            }
            SupplyClassEnum supplierClass = SupplyClassEnum.getSupplierClass(supplyClass);
            if (supplierClass == null) {
                return success("supplyClass 无效");
            }

            // 加载供应商映射到缓存
            initService.loadSupplyMappingInfoToCache(supplyClass);

            // 删除酒店房型映射
            inactiveService.deleteHotelAndRoomMappuingCache(supplyClass, null);
            return success();
        } catch (Exception e) {
            log.error("查询酒店映射信息异常", e);
            return ResultX.success("error");
        }
    }


    /**
     * 从数据库加载指定供应商映射信息到redis
     *
     * @param supplyClass 供应商类型
     * @return 结果
     */
    @GetMapping("/deleteHotelAndRoomMappuingCache")
    public ResultX deleteHotelAndRoomMappuingCache(String supplyClass, String hotelIds) {
        try {
            if (StrUtil.isBlank(supplyClass)) {
                return success("supplyClass 不可为空");
            }
            SupplyClassEnum supplyClassEnum = SupplyClassEnum.getSupplierClass(supplyClass);
            if (supplyClassEnum == null) {
                return success("supplyClass 无效");
            }
            List<String> hotelIdList = null;
            if (StrUtil.isNotBlank(hotelIds)) {
                hotelIdList = StrUtil.split(hotelIds, ",|，");
            }

            // 删除酒店房型映射
            inactiveService.deleteHotelAndRoomMappuingCache(supplyClass, hotelIdList);
            return success();
        } catch (Exception e) {
            log.error("删除酒店房型映射缓存信息异常", e);
            return ResultX.success("error");
        }
    }

    /**
     * 获取修改密钥
     *
     * @return
     */
    @GetMapping("/getConfigKey")
    public ResultX<String> getConfigKey() {
        return success(commonConfig.getConfigKey());
    }

}