package com.fangcang.hotel.huizhiosbld.constants;

import com.fangcang.hotel.data.api.constants.RedisKeyConstants;
import com.fangcang.hotel.data.api.enums.SupplyClassEnum;

/**
 * Rediskey常量类
 */
public class HUIZHIOSBLDRedisKeyConstants {

    //房型映射
    public static final String ROOM_MAPPING_CACHE_KEY = RedisKeyConstants.PREFIX + "ROOM_MAPPING:" + SupplyClassEnum.HUIZHIOSBLD.getSupplierClass();

    /**
     * 热门酒店IDkey ，这个用来跑起价得
     */
    public static final String HUIZHI_HOT_HOTEL_ID = RedisKeyConstants.PREFIX + SupplyClassEnum.HUIZHIOSBLD.getSupplierClass() + ":HOT_HOTEL_IDS";

    /**
     * 用于存储所有需要更新起价的数据 用于循环初始化起价
     */
    public static final String HUIZHI_HOTEL_ID_LOWEST_PRICE = RedisKeyConstants.PREFIX + SupplyClassEnum.HUIZHIOSBLD.getSupplierClass() + "_HOTEL_ID_LOWEST_PRICE";

    /**
     * 冷起价缓存key
     */
    public static final String HUIZHI_HOTEL_ID_COLD_LOWEST_PRICE = RedisKeyConstants.PREFIX + SupplyClassEnum.HUIZHIOSBLD.getSupplierClass() + "_HOTEL_ID_COLD_LOWEST_PRICE";

    /**
     * 需要同步的酒店id集合
     */
    public static final String HUIZHI_NEED_SYNC_HOTEL_ID = RedisKeyConstants.PREFIX + SupplyClassEnum.HUIZHIOSBLD.getSupplierClass() + "_NEED_SYNC_HOTEL_ID";
}

