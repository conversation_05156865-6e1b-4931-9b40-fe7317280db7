package com.fangcang.hotel.heytripgoosbld.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.HttpMethod;

/**
 * <AUTHOR>
 * @Date 2025/3/3 11:40
 * @Description:
 */

@Getter
@AllArgsConstructor
public enum HEYTRIPGOHttpPathEnum {

    ACCOMMODATION_AVAILABLEACCOMMODATIONIDS("/Accommodation/AvailableAccommodationIds", "获取有效酒店编号", HttpMethod.GET.toString()),
    ACCOMMODATION_ACCOMMODATIONSDETAILS("/Accommodation/AccommodationsDetails", "获取酒店房型相关静态信息", HttpMethod.POST.toString()),
    ACCOMMODATION_AVAILABILITY("/Accommodation/Availability", "获取酒店报价", HttpMethod.POST.toString()),
    ACCOMMODATION_PRICECHECK("/Accommodation/PriceCheck", "下单前验证报价是否可订", HttpMethod.POST.toString()),
    ACCOMMODATION_QUOTEDHOTELSPRICE("Accommodation/QuotedHotelsPrice", "酒店起价查询接口", HttpMethod.POST.toString()),
    ORDER_CREATE("/Order/Create", "创建订单", HttpMethod.POST.toString()),
    ORDER_CANCEL("/Order/Cancel", "取消订单", HttpMethod.POST.toString()),
    ORDER_PREVIEW("/Order/Preview", "查询订单", HttpMethod.GET.toString()),
    ACCOMMODATION_GETHOTELINCREMENT("/Accommodation/GetHotelIncrement", "新增热销酒店Id获取接口", HttpMethod.GET.toString());

    private String value;
    private String desc;
    private String httpMethod;

}
