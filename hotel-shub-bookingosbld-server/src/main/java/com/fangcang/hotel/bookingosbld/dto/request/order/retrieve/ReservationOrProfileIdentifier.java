package com.fangcang.hotel.bookingosbld.dto.request.order.retrieve;


import com.fangcang.hotel.bookingosbld.dto.response.order.addmultielements.Reservation;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/29 18:45
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "reservationOrProfileIdentifier")
public class ReservationOrProfileIdentifier {

    @XmlElement(name = "reservation")
    private Reservation reservation;

    public Reservation getReservation() {
        return reservation;
    }

    public void setReservation(Reservation reservation) {
        this.reservation = reservation;
    }
}
