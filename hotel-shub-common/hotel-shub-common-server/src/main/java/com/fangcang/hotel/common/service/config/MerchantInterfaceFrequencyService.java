package com.fangcang.hotel.common.service.config;

import com.fangcang.hotel.common.controller.admin.config.vo.MerchantInterfaceFrequencyAditReqVO;
import com.fangcang.hotel.common.controller.admin.config.vo.MerchantInterfaceFrequencyPageReqVO;
import com.fangcang.hotel.common.controller.admin.config.vo.MerchantInterfaceFrequencyQueryReqVO;
import com.fangcang.hotel.common.dal.db.config.MerchantInterfaceFrequencyDO;
import com.fangcang.hotel.framework.common.domain.PageResult;

import javax.validation.Valid;
import java.util.List;

/**
 * 商家频率 Service 接口
 *
 * <AUTHOR>
 */
public interface MerchantInterfaceFrequencyService {

    /**
     * 创建商家频率
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long merchantInterfaceFrequencyAdd(@Valid MerchantInterfaceFrequencyAditReqVO reqVO);


    /**
     * 创建商家频率
     *
     * @param reqVOS 创建信息
     * @return 编号
     */
    void merchantInterfaceFrequencyAdds(@Valid List<MerchantInterfaceFrequencyAditReqVO> reqVOS);

    /**
     * 更新商家频率
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long merchantInterfaceFrequencyEdit(@Valid MerchantInterfaceFrequencyAditReqVO reqVO);

    /**
     * 删除商家频率
     *
     * @param id 编号
     */
    void merchantInterfaceFrequencyDel(Long id);

    /**
     * 获得商家频率信息
     *
     * @param id 编号
     * @return 商家频率信息
     */
    MerchantInterfaceFrequencyDO merchantInterfaceFrequencyDetail(Long id);

    /**
     * 获得商家频率列表
     *
     * @param reqVO 查询条件
     * @return 商家频率列表
     */
    List<MerchantInterfaceFrequencyDO> merchantInterfaceFrequencyList(@Valid MerchantInterfaceFrequencyQueryReqVO reqVO);

    /**
     * 获得商家频率分页
     *
     * @param reqVO 查询条件
     * @return 商家频率分页
     */
    PageResult<MerchantInterfaceFrequencyDO> merchantInterfaceFrequencyPage(@Valid MerchantInterfaceFrequencyPageReqVO reqVO);

}
