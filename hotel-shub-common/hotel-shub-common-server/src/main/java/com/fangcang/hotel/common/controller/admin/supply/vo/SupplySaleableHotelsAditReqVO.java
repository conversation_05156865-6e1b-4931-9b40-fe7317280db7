package com.fangcang.hotel.common.controller.admin.supply.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 可售酒店范围 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SupplySaleableHotelsAditReqVO extends SupplySaleableHotelsBaseVO {

    /**
     * 批量酒店id 多个使用 , 分隔 接口逻辑拆分
     */
    private String spHotelIds;

}
