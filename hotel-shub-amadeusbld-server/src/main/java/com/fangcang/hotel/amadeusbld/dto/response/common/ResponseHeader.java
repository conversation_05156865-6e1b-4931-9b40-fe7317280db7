package com.fangcang.hotel.amadeusbld.dto.response.common;

import com.fangcang.hotel.amadeusbld.dto.common.AwsseSession;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/18 15:04
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "soap:Header")
public class ResponseHeader {

    @XmlElement(name = "wsa:To")
    private String wsaTo = "http://www.w3.org/2005/08/addressing/anonymous";

    @XmlElement(name = "wsa:From")
    private WsaFrom wsaFrom;

    @XmlElement(name = "wsa:Action")
    private String wsaAction;

    @XmlElement(name = "wsa:MessageID")
    private String wsaMessageID;

    @XmlElement(name = "wsa:RelatesTo")
    private WsaRelatesTo wsaRelatesTo;

    @XmlElement(name = "awsse:Session")
    private AwsseSession awsseSession;

    public WsaFrom getWsaFrom() {
        return wsaFrom;
    }

    public void setWsaFrom(WsaFrom wsaFrom) {
        this.wsaFrom = wsaFrom;
    }

    public String getWsaAction() {
        return wsaAction;
    }

    public void setWsaAction(String wsaAction) {
        this.wsaAction = wsaAction;
    }

    public String getWsaMessageID() {
        return wsaMessageID;
    }

    public void setWsaMessageID(String wsaMessageID) {
        this.wsaMessageID = wsaMessageID;
    }

    public WsaRelatesTo getWsaRelatesTo() {
        return wsaRelatesTo;
    }

    public void setWsaRelatesTo(WsaRelatesTo wsaRelatesTo) {
        this.wsaRelatesTo = wsaRelatesTo;
    }

    public AwsseSession getAwsseSession() {
        return awsseSession;
    }

    public void setAwsseSession(AwsseSession awsseSession) {
        this.awsseSession = awsseSession;
    }
}
