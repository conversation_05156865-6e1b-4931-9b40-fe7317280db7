package com.fangcang.hotel.amadeusosbld.utils;

import com.fangcang.hotel.amadeusosbld.dto.common.*;
import lombok.extern.slf4j.Slf4j;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2021/7/29 10:54
 */

@Slf4j
public class CreateHeaderUtil {

    private static String jsString;

    static {
        try {
            jsString = readJSFile();
        } catch (Exception e) {
            log.error("系统异常", e);
        }
    }

    /**
     * @param actionUrl
     * @param isSecurity   是否需要用户名密码
     * @param awsseSession
     * @return
     * @throws Exception
     */
    public static SoapHeader creatSoapHeader(String actionUrl, boolean isSecurity, AwsseSession awsseSession) throws Exception {
        SoapHeader soapHeader = new SoapHeader();
        soapHeader.setMessageID(new AddMessageID());
        AddAction addAction = new AddAction();
        addAction.setValue(actionUrl);
        soapHeader.setAddAction(addAction);
        AddTo addTo = new AddTo();
        addTo.setValue("https://nodeD3.production.webservices.amadeus.com/1ASIWHOTWTX");
        soapHeader.setAddTo(addTo);
        if (isSecurity) {
            soapHeader.setTransactionFlowLink(new TransactionFlowLink());
            OasUsernameToken oasUsernameToken = new OasUsernameToken();
            oasUsernameToken.setOasUsername("WSWTXHOT");

            String nonce = Base64.getEncoder().encodeToString(randomString2(8).getBytes());
            String timestamp = zeroZoneTime("yyyy-MM-dd'T'HH:mm:ss:SSS") + "Z";

            String passwordEncoded;
            try {
                passwordEncoded = doPasswordDigest("N9%aRjE#", timestamp, nonce);
            } catch (Exception e) {
                nonce = Base64.getEncoder().encodeToString(randomString2(8).getBytes());
                timestamp = zeroZoneTime("yyyy-MM-dd'T'HH:mm:ss:SSS") + "Z";
                passwordEncoded = doPasswordDigest("N9%aRjE#", timestamp, nonce);
            }
            OasNonce oasNonce = new OasNonce();
            oasNonce.setValue(nonce);
            oasUsernameToken.setOasNonce(oasNonce);
            oasUsernameToken.setOas1Created(timestamp);
            OasPassword oasPassword = new OasPassword();
            oasPassword.setValue(passwordEncoded);
            oasUsernameToken.setOasPassword(oasPassword);
            OasSecurity oasSecurity = new OasSecurity();
            oasSecurity.setOasUsernameToken(oasUsernameToken);
            soapHeader.setOasSecurity(oasSecurity);
            AMASecurityHostedUser amaSecurityHostedUser = new AMASecurityHostedUser();
            UserID userID = new UserID();
            userID.setAgentDutyCode("SU");
            userID.setPosType("1");
            userID.setPseudoCityCode("WUHCC21TX");
            userID.setRequestorType("U");
            amaSecurityHostedUser.setUserID(userID);
            soapHeader.setAmaSecurityHostedUser(amaSecurityHostedUser);
        }
        if (awsseSession != null) {
            soapHeader.setAwsseSession(awsseSession);
        }
        return soapHeader;
    }

    public static String randomString2(int len) {
        String text = "";
        String possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        for (int i = 0; i < len; i++) {
            text += possible.charAt(new Double(Math.floor(Math.random() * possible.length())).intValue());
        }
        return text;
    }


    /**
     * 获取当前零时区时间
     *
     * @param format
     * @return
     */
    public static final String zeroZoneTime(String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
        return dateFormat.format(new Date());
    }

    private static String doPasswordDigest(String pwd, String timestamp, String base64Nonce) throws Exception {
        ScriptEngineManager mgr = new ScriptEngineManager();
        ScriptEngine engine = mgr.getEngineByName("javascript");
        if (StringUtil.isValidString(jsString)) {
            engine.eval(jsString);
        } else {
            jsString = readJSFile();
            engine.eval(jsString);
        }
        Invocable inv = (Invocable) engine;
        Object digestPwd = inv.invokeFunction("WbsPassword", new String[]{pwd, timestamp, base64Nonce});
        return (String) digestPwd;
    }

    private static String readJSFile() throws Exception {
        StringBuffer script = new StringBuffer();
        InputStream is = null;
        BufferedReader bufferreader = null;
        try {
            is = CreateSoapHeaderUtil.class.getClassLoader().getResourceAsStream("amadues.js");
            bufferreader = new BufferedReader(new InputStreamReader(is));
            String tempString = null;
            while ((tempString = bufferreader.readLine()) != null) {
                script.append(tempString).append("\n");
            }
        } catch (Exception e) {
            throw e;
        } finally {
            if (bufferreader != null) {
                bufferreader.close();
            }
            if (is != null) {
                is.close();
            }
        }
        return script.toString();
    }

}
