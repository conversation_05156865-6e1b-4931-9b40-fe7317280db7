package com.fangcang.hotel.data.api.enums;

/**
 * @description:
 * @author: qiu
 * @create: 2023-12-28 13:56
 */

public enum VipOrderEnum {
    VIP_ORDER(1, "会员订单"),
    NON_VIP_ORDER(0, "非会员订单"),
    ;
    private Integer code;
    private String desc;

    private VipOrderEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static VipOrderEnum getEnumByCode(Integer code) {
        for (VipOrderEnum vipOrderEnum : VipOrderEnum.values()) {
            if (vipOrderEnum.getCode().equals(code)) {
                return vipOrderEnum;
            }
        }
        return null;
    }


    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}