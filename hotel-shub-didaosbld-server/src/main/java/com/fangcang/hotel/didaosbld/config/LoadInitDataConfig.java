package com.fangcang.hotel.didaosbld.config;

import com.fangcang.hotel.core.util.StringUtilExtend;
import com.fangcang.hotel.didaosbld.constants.DIDAOSBLDConstants;
import com.fangcang.hotel.didaosbld.util.LogPrintUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @Date 2024/12/11 18:36
 * @Description:
 */

@Component
public class LoadInitDataConfig implements ApplicationRunner {

    @Autowired
    private LogPrintUtil logPrintUtil;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        CompletableFuture.runAsync(() -> {
            //用于将中文的姓名进行拆分,拆分成姓和名
            try {
                if (StringUtilExtend.isValidString(DIDAOSBLDConstants.LAST_NAME)) {
                    DIDAOSBLDConstants.lastNameSet.addAll(Arrays.asList(DIDAOSBLDConstants.LAST_NAME.split(",")));
                } else {
                    logPrintUtil.simplePrintErrorLog("LoadNationalityInfos", "姓氏集合为空");
                }
            } catch (Exception e) {
                logPrintUtil.simplePrintErrorLog("LoadNationalityInfos", "初始化姓氏集合异常 error:{}", e.getMessage());
            }

            // 创建文件夹,用于落地酒店静态信息时,保存对应下载的压缩包
            try {
                // 拼接本地临时文件路径
                String directoryPath = System.getProperty("user.dir") + File.separator + "static";
                Path path = Paths.get(directoryPath);
                // 检查文件夹是否存在，如果不存在则创建它
                if (!Files.exists(path)) {
                    Files.createDirectory(path);
                    logPrintUtil.simplePrintInfoLog("LoadNationalityInfos", "文件夹创建成功:" + directoryPath);
                } else {
                    logPrintUtil.simplePrintInfoLog("LoadNationalityInfos", "文件夹已经存在:" + directoryPath);
                }
            } catch (Exception e) {
                logPrintUtil.simplePrintErrorLog("LoadNationalityInfos", "初始化创建文件夹异常 error:{}", e.getMessage());
            }
        });
    }
}
