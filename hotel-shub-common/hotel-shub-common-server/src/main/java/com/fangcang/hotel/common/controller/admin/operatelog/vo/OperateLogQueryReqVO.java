package com.fangcang.hotel.common.controller.admin.operatelog.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.fangcang.hotel.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 操作日志 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class OperateLogQueryReqVO {

    /** 操作id 业务id 用于和业务数据关联 */
    private Long operateId;

    /**
     * 供应类型
     */
    private String supplyClass;

    /** 日志类型 [商家来源,供应商模板] */
    private Integer logType;

    /** 操作类型 [创建,删除,修改] */
    private Integer operateType;

    /** 修改前数据 */
    private String oldData;

    /** 修改后数据 */
    private String newData;


    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

}
