package com.fangcang.hotel.epsosbld.entity.mongodb;

import com.fangcang.hotel.epsosbld.entity.EventData;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 *
 */
@Data
@Document("OrderChangeInfo")
public class OrderChangeInfo {

    /**
     * 信息id
     */
    @Id
    private String id;


    /** 商家订单号 */
    private String merchantOrderCode;

    /** 供应商订单号 */
    private String supplyOrderCode;

    private Date updateDate;
    /**
     * 0 未处理，1 已经处理
     */
    private Integer processStatus;

    /**
     * eps 推送过来的数据
     */
    private String data;

    /**
     * eps请求头数据
     */
    private String authorization;
}
