package com.fangcang.hotel.amadeusbld.dto.response.order.completereservationdetails;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/29 21:03
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "marketingText")
public class MarketingText {

    @XmlElement(name = "freeTextDetails")
    private FreeTextDetails freeTextDetails;

    @XmlElement(name = "freeText")
    private List<String> freeText;

    public FreeTextDetails getFreeTextDetails() {
        return freeTextDetails;
    }

    public void setFreeTextDetails(FreeTextDetails freeTextDetails) {
        this.freeTextDetails = freeTextDetails;
    }

    public List<String> getFreeText() {
        return freeText;
    }

    public void setFreeText(List<String> freeText) {
        this.freeText = freeText;
    }
}
