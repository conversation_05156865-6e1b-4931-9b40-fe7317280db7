<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.hotel.common.dal.mysql.order.SupplyOrderItemMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="supplyOrderItemList" resultType="com.fangcang.hotel.common.api.common.order.dto.SupplyOrderItemRespDTO">
        SELECT
        t.id as id,
        t.merchant_source as merchantSource,
        t.merchant_code as merchantCode,
        t.supply_order_code as supplyOrderCode,
        t.supply_class as supplyClass,
        t.supply_code as supplyCode,
        t.sale_date as saleDate,
        t.price as price,
        t.break_fast_num as breakFastNum,
        t.break_fast_type as breakFastType,
        t.`status` as status,
        t.created_by as createdBy,
        t.created_dt as createdDt,
        t.updated_by as updatedBy,
        t.updated_dt as updatedDt
        FROM
        t_htlsync_supply_order_item t
        <where>
            <if test="supplyOrderCode !=null">and t.supply_order_code= #{supplyOrderCode}</if>
            <if test="supplyClass !=null">and  t.supply_class= #{supplyClass}</if>
            <if test="supplyCode !=null">and t.supply_code= #{supplyCode}</if>
            <if test="merchantSource !=null">and t.merchant_source= #{merchantSource}</if>
            <if test="merchantCode !=null">and t.merchant_code= #{merchantCode}</if>
        </where>
    </select>
</mapper>
