package com.fangcang.hotel.didaosbld.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/12/2 17:22
 * @Description:
 */

@RefreshScope //动态刷新配置
@Component //注入到spring容器中
@Data //lombok注解，自动生成get/set方法
@ConfigurationProperties(prefix = "product-thread")
public class DIDAOSBLDThreadPoolConfig {

    public Integer corePoolSize = 3;

    public Integer maxPoolSize = 6;

    public Integer keepAliveSeconds = 60;

    public Integer queueCapacity = 1000;

    public Integer taskCount = 3;

//    @Bean
//    public ThreadPoolExecutor poolExecutor() {
//        return new ThreadPoolExecutor(
//                corePoolSize,
//                maxPoolSize,
//                keepAliveTime,
//                TimeUnit.SECONDS,
//                new LinkedBlockingDeque<>(capacity),
//                Executors.defaultThreadFactory(),
//                new ThreadPoolExecutor.AbortPolicy());
//    }
}
