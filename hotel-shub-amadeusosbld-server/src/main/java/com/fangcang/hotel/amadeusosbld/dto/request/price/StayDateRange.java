package com.fangcang.hotel.amadeusosbld.dto.request.price;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/18 11:54
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "StayDateRange")
public class StayDateRange {

    @XmlAttribute(name = "Start")
    private String start;

    @XmlAttribute(name = "End")
    private String end;

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }
}
