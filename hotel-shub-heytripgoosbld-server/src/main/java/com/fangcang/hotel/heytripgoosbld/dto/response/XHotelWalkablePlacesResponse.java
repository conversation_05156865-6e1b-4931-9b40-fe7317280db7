package com.fangcang.hotel.heytripgoosbld.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/26 16:06
 * @Description:
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class XHotelWalkablePlacesResponse {

    /**
     * 标题
     */
    @JSONField(name = "Title")
    private String title;

    /**
     * 描述
     */
    @JSONField(name = "Desc")
    private String desc;

    /**
     * 分类
     */
    @JSONField(name = "Categories")
    private List<XHotelWalkableCategoryResponse> categories;

}
