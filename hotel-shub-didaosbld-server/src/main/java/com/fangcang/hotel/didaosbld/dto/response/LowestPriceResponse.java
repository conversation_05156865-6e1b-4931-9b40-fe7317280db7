package com.fangcang.hotel.didaosbld.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/12/4 10:46
 * @Description: 酒店最低价
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LowestPriceResponse {

    /**
     * 最低价币种
     */
    @JSONField(name = "Currency")
    private String currency;

    @JSONField(name = "RatePlanID")
    private String ratePlanID;

    @JSONField(name = "Value")
    private BigDecimal value;

}
