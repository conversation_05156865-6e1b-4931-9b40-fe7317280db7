package com.fangcang.hotel.hzbld.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 *
 *  获取token响应参数
 *
 * <AUTHOR>
 * @date 2023-12-11 14:22:34
 */
@Data
public class TokenResponse {

    @JSONField(name="access_token")
    private String accessToken;

    @JSONField(name="token_type")
    private String tokenType;

    @JSONField(name="expires_in")
    private Integer expiresIn;

    @JSONField(name="scope")
    private String scope;
}
