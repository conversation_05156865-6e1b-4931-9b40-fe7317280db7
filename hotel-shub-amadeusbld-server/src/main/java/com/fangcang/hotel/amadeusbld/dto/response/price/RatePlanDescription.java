package com.fangcang.hotel.amadeusbld.dto.response.price;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/18 18:53
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "RatePlanDescription")
public class RatePlanDescription {

    @XmlElement(name = "Text")
    private String text;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}
