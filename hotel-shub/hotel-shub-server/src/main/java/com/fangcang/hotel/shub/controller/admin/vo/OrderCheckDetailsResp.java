package com.fangcang.hotel.shub.controller.admin.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 订单入住明细响应
 * @author: qiu
 * @create: 2023-12-03 14:26
 */
@Data
public class OrderCheckDetailsResp {

    /**
     * 是否存在每间价格明细
     */
    private Boolean existOrderCheckDetail;

    /**
     * 订单的入住状态，没有每日价格返回
     * CheckInStateEnum
     */
    private String checkInState;

    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;

    /**
     * 币种
     * CurrencyEnum
     */
    private Integer curreny;

    /**
     * 实际入住情况费用(退订费)
     */
    private BigDecimal refundAmount;

    /** 商家订单号 */
    private String merchantOrderCode;

    /** 供应商订单号 */
    private String supplyOrderCode;

    /**
     * 房间入住明细
     */
    private List<RoomCheckDetail> roomCheckDetails;
}