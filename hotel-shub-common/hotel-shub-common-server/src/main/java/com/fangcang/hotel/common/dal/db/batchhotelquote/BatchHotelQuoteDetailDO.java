package com.fangcang.hotel.common.dal.db.batchhotelquote;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.hotel.core.pojo.BaseDOX;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2025/4/3
 * @Description: 批量酒店报价导入明细
 */
@Data
@TableName("t_batch_hotel_quote_detail")
@KeySequence("t_batch_hotel_quote_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BatchHotelQuoteDetailDO extends BaseDOX {
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;
    /**
     * 文件id
     */
    private Long fileId;
    /**
     * 商家来源
     */
    private String merchantSource;

    /**
     * 商家编码
     */
    private String merchantCode;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 供应商酒店id
     */
    private String spHotelId;
    /**
     * 入住日期
     */
    private String checkInDate;
    /**
     * 批次
     */
    private Integer batchCount;
    /**
     * 供应商类型编码
     */
    private String supplyClass;
    /**
     * 状态 0 待处理 1已处理
     */
    private Integer status;
    /**
     * 导入文档顺序
     */
    private Integer sorted;
}
