package com.fangcang.hotel.amadeusosbld.dto.request.order.addmultielements;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/28 17:01
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "traveller")
public class Traveller {

    @XmlElement(name = "surname")
    private String surname;

    @XmlElement(name = "quantity")
    private String quantity;

    public String getSurname() {
        return surname;
    }

    public void setSurname(String surname) {
        this.surname = surname;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }
}
