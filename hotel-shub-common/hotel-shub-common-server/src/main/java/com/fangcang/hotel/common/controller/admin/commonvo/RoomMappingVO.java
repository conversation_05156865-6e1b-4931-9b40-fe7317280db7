package com.fangcang.hotel.common.controller.admin.commonvo;

import com.fangcang.hotel.data.base.annotation.Desc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 此类只用于展示注释模板使用，不会被实际使用
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoomMappingVO  {

    /** id */
    @Desc("id")
    private Long id;

    /** 供应商房型ID */
    @Desc(value = "供应商房型ID")
    private String spRoomId;

    /** 房型ID */
    @Desc(value = "房型ID")
    private String roomId;

    /** 酒店id */
    @Desc(value = "酒店id")
    private String hotelId;

    /** 供应商酒店ID */
    @Desc(value = "供应商酒店ID")
    private String spHotelId;

    /** 供应商房型名称 */
    @Desc(value = "供应商房型名称")
    private String spRoomName;

    /** 供应商房型名称英文 */
    @Desc(value = "供应商房型名称英文")
    private String spRoomNameEn;

    /** 房型名称 */
    @Desc(value = "房型名称")
    private String roomName;

    /** 房型名称英文 */
    @Desc(value = "房型英文名称")
    private String roomNameEn;

    /** 供应类型 */
    @Desc(value = "供应类型")
    private String supplyClass;

    /** 状态 [有效,无效] */
    @Desc(value = "状态")
    private Integer status;

    /**
     * 创建时间
     */
    @Desc(value = "创建时间")
    private LocalDateTime createdDt;
    /**
     * 最后更新时间
     */
    @Desc(value = "最后更新时间")
    private LocalDateTime updatedDt;
    /**
     * 创建者
     */
    @Desc(value = "创建人")
    private String createdBy;
    /**
     * 更新者
     */
    @Desc(value = "更新人")
    private String updatedBy;

}
