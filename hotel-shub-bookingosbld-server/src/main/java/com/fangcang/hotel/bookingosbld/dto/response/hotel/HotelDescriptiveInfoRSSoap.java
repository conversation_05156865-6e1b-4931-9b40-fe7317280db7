package com.fangcang.hotel.bookingosbld.dto.response.hotel;


import com.fangcang.hotel.bookingosbld.dto.response.common.ResponseHeader;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2021/6/22 10:58
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "soap:Envelope")
public class HotelDescriptiveInfoRSSoap {

    @XmlAttribute(name = "xmlns:soap")
    private String xmlnsSoap = "http://schemas.xmlsoap.org/soap/envelope/";

    @XmlAttribute(name = "xmlns:awsse")
    private String xmlnsAwsse = "http://xml.amadeus.com/2010/06/Session_v3";

    @XmlAttribute(name = "xmlns:wsa")
    private String xmlnsWsa = "http://www.w3.org/2005/08/addressing";

    @XmlElement(name = "soap:Header")
    private ResponseHeader responseHeader;

    @XmlElement(name = "soap:Body")
    private HotelDescriptiveInfoRSBody hotelDescriptiveInfoRSBody;

    public ResponseHeader getResponseHeader() {
        return responseHeader;
    }

    public void setResponseHeader(ResponseHeader responseHeader) {
        this.responseHeader = responseHeader;
    }

    public HotelDescriptiveInfoRSBody getHotelDescriptiveInfoRSBody() {
        return hotelDescriptiveInfoRSBody;
    }

    public void setHotelDescriptiveInfoRSBody(HotelDescriptiveInfoRSBody hotelDescriptiveInfoRSBody) {
        this.hotelDescriptiveInfoRSBody = hotelDescriptiveInfoRSBody;
    }
}
