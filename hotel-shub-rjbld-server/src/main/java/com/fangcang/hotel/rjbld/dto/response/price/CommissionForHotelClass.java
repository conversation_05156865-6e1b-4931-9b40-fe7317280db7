package com.fangcang.hotel.rjbld.dto.response.price;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  酒店品牌佣金列表
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:15
 */
@Data
public class CommissionForHotelClass implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 品牌
     */
    @JSONField(name = "Brandld")
    private String brandld;

    /**
     * 佣金值
     */
    @JSONField(name = "Value")
    private String value;

}
