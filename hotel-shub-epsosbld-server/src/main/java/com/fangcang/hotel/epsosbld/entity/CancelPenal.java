package com.fangcang.hotel.epsosbld.entity;

import lombok.Data;

/**
 * @description:
 * @author: qiu
 * @create: 2024-07-24 14:58
 */
@Data
public class CancelPenal {

    /**
     * 罚金类型
     * 1. *晚
     * 2. 固定金额
     * 3. 百分比
     * 4. 首晚百分比
     */
    private Integer penaltiesType;

    /**
     * 开始时间 yyyy-MM-dd'T'HH:mm:ss.SSSXXX
     * 2022-09-04T23:59:00.000+07:00
     * 带有时区偏移量的日期时间字符串 一般都是酒店当地时间
     */
    private String startDate;

    /**
     * 结束时间 yyyy-MM-dd'T'HH:mm:ss.SSSXXX
     * 2022-09-04T23:59:00.000+07:00
     * 带有时区偏移量的日期时间字符串 一般都是酒店当地时间
     */
    private String endData;

    /**
     * 罚金值
     */
    private String penaltiesValue;

    /**
     * 罚金币种
     */
    private String currencyCode;
}
