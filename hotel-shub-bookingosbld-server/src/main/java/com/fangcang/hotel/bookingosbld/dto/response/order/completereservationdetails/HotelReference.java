package com.fangcang.hotel.bookingosbld.dto.response.order.completereservationdetails;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/29 20:47
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "hotelReference")
public class HotelReference {

    @XmlElement(name = "chainCode")
    private String chainCode;

    @XmlElement(name = "cityCode")
    private String cityCode;

    @XmlElement(name = "hotelCode")
    private String hotelCode;

    public String getChainCode() {
        return chainCode;
    }

    public void setChainCode(String chainCode) {
        this.chainCode = chainCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getHotelCode() {
        return hotelCode;
    }

    public void setHotelCode(String hotelCode) {
        this.hotelCode = hotelCode;
    }
}
