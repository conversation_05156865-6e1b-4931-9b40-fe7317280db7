package com.fangcang.hotel.amadeusosbld.dto.response.order.addmultielements;


import com.fangcang.hotel.amadeusosbld.dto.request.order.addmultielements.Reference;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/7/1 12:48
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "referenceForSegment")
public class ReferenceForSegment {

    @XmlElement(name = "reference")
    private Reference reference;

    public Reference getReference() {
        return reference;
    }

    public void setReference(Reference reference) {
        this.reference = reference;
    }
}
