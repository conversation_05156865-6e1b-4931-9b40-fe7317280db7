package com.fangcang.hotel.common.convert.mapping;

import com.fangcang.hotel.common.api.common.mapping.dto.AreasReqDTO;
import com.fangcang.hotel.common.api.common.mapping.dto.CityMappingQueryReqDTO;
import com.fangcang.hotel.common.api.common.mapping.dto.CityMappingReqDTO;
import com.fangcang.hotel.common.api.common.mapping.dto.CityMappingRespDTO;
import com.fangcang.hotel.common.controller.admin.mapping.vo.AreasReqVO;
import com.fangcang.hotel.common.controller.admin.mapping.vo.CityMappingAditReqVO;
import com.fangcang.hotel.common.controller.admin.mapping.vo.CityMappingQueryReqVO;
import com.fangcang.hotel.common.controller.admin.mapping.vo.CityMappingRespVO;
import com.fangcang.hotel.common.dal.db.mapping.CityMappingDO;
import com.fangcang.hotel.framework.common.domain.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 城市映射 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CityMappingConvert {

    CityMappingConvert INSTANCE = Mappers.getMapper(CityMappingConvert.class);

    CityMappingDO convert(CityMappingAditReqVO bean);

    CityMappingRespVO convert(CityMappingDO bean);

    CityMappingQueryReqVO convert(CityMappingQueryReqDTO bean);

    AreasReqVO convert(AreasReqDTO bean);

    List<CityMappingRespVO> convertList(List<CityMappingDO> list);

    List<CityMappingAditReqVO> converCityMappingAditList(List<CityMappingReqDTO> list);

    CityMappingAditReqVO map(CityMappingReqDTO cityMappingReqDTO);

    List<CityMappingRespDTO> convertDTOList(List<CityMappingDO> list);

    List<CityMappingDO> convertMappingDOList(List<CityMappingAditReqVO> list);

    PageResult<CityMappingRespVO> convertPage(PageResult<CityMappingDO> page);

}
