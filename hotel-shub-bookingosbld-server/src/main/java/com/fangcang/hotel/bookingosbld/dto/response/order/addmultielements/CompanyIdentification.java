package com.fangcang.hotel.bookingosbld.dto.response.order.addmultielements;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/7/1 12:41
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "companyIdentification")
public class CompanyIdentification {

    @XmlElement(name = "travelSector")
    private String travelSector;

    @XmlElement(name = "companyCodeContext")
    private String companyCodeContext;

    @XmlElement(name = "companyCode")
    private String companyCode;

    @XmlElement(name = "companyName")
    private String companyName;

    public String getTravelSector() {
        return travelSector;
    }

    public void setTravelSector(String travelSector) {
        this.travelSector = travelSector;
    }

    public String getCompanyCodeContext() {
        return companyCodeContext;
    }

    public void setCompanyCodeContext(String companyCodeContext) {
        this.companyCodeContext = companyCodeContext;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
}
