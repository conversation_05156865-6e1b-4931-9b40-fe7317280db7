package com.fangcang.hotel.common.controller.admin.config.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 商家频率 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class MerchantInterfaceFrequencyBaseVO implements Serializable {

    /** id */
    private Long id;

    /** 商家来源 SAAS 飞天 */
    @NotEmpty(message = "商家来源不能为空")
    private String merchantSource;

    /** 商家编码 */
    @NotEmpty(message = "商家编码不能为空")
    private String merchantCode;

    /** 接口类型 */
    @NotEmpty(message = "接口类型不能为空")
    private String interfaceType;

    /** 调用次数 */
    @NotNull(message = "调用次数不能为空")
    private Integer invokeTimes;

    /** 状态 [有效,无效] */
    @NotNull(message = "状态 [有效,无效]不能为空")
    private Integer status;

}
