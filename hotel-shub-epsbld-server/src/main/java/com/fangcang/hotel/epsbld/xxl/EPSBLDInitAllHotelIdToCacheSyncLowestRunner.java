package com.fangcang.hotel.epsbld.xxl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.fangcang.hotel.common.api.common.mapping.HotelMappingApi;
import com.fangcang.hotel.common.api.common.mapping.dto.HotelMappingPageRespDTO;
import com.fangcang.hotel.common.api.common.mapping.dto.HotelMappingQueryReqDTO;
import com.fangcang.hotel.common.api.common.supply.SupplyHotelLowestPriceApi;
import com.fangcang.hotel.common.api.common.supply.dto.SupplyHotelLowestPriceReqDTO;
import com.fangcang.hotel.data.api.dto.*;
import com.fangcang.hotel.data.api.enums.SupplyClassEnum;
import com.fangcang.hotel.data.api.enums.SupplyTypeEnum;
import com.fangcang.hotel.data.api.service.SupplyProductService;
import com.fangcang.hotel.epsbld.constant.RedisCacheKeyConstants;
import com.fangcang.hotel.epsbld.properties.EPSBLDApplicationConfigProperties;
import com.fangcang.hotel.framework.common.domain.PageResult;
import com.fangcang.hotel.framework.common.domain.ResultX;
import com.fangcang.hotel.framework.common.util.CollUtilX;
import com.fangcang.hotel.framework.common.util.DateUtilX;
import com.fangcang.hotel.framework.redis.core.RedisTemplateX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 初始化所有有映射的酒店起价任务
 */
@Slf4j
@Component
public class EPSBLDInitAllHotelIdToCacheSyncLowestRunner {

    @Autowired
    private HotelMappingApi hotelMappingApi;

    @Autowired
    private SupplyProductService epsbldProductService;

    @Autowired
    private SupplyHotelLowestPriceApi supplyHotelLowestPriceApi;

    @Autowired
    private EPSBLDApplicationConfigProperties epsbldApplicationConfigProperties;

    /**
     * 初始化所有有映射的酒店id到缓存用于同步起价任务
     */
    @XxlJob("initAllHotelIdToCacheSyncLowestRunner")
    public void initAllHotelIdToCacheSyncLowestRunner() {
        try {
            XxlJobHelper.log("执行初始化所有有映射的酒店id到缓存用于同步起价任务开始");
            // 分页查询起价
            HotelMappingQueryReqDTO hotelMappingQueryReqDTO = new HotelMappingQueryReqDTO();
            hotelMappingQueryReqDTO.setSupplyClass(SupplyClassEnum.EPSBLD.getSupplierClass());
            hotelMappingQueryReqDTO.setPageSize(200L);
            hotelMappingQueryReqDTO.setCurrentPage(1L);
            long result = queryHotelMappingAndToCache(hotelMappingQueryReqDTO);
            if (result > 0L) {
                for (long i = 2L; i < result; i++) {
                    try {
                        hotelMappingQueryReqDTO.setCurrentPage(i);
                        queryHotelMappingAndToCache(hotelMappingQueryReqDTO);
                    } catch (Exception e) {
                        log.error("初始化所有有映射的酒店id到缓存用于同步起价任务异常", e);
                    }
                }
            }
            XxlJobHelper.log("执行初始化所有有映射的酒店id到缓存用于同步起价任务结束");
        } catch (Exception e) {
            log.error("执行初始化所有有映射的酒店id到缓存用于同步起价任务异常", e);
            XxlJobHelper.log("执行初始化所有有映射的酒店id到缓存用于同步起价任务异常", e);
        }
    }

    /**
     * 消费缓存同步全量起价数据任务
     */
    @XxlJob("consumerColdHotelIdSyncLowestRunner")
    public void consumerColdHotelIdSyncLowestRunner() {
        try {
            XxlJobHelper.log("执行消费缓存同步全量起价数据任务开始");
            if (RedisTemplateX.hasKey(RedisCacheKeyConstants.EPS_HOTEL_ID_COLD_LOWEST_PRICE)) {
                // 跑起价的天数
                Integer lowesPriceDay = epsbldApplicationConfigProperties.getLowesPriceDay();
                Integer checkInDay = epsbldApplicationConfigProperties.getCheckInDay();
                Integer dayTotal = epsbldApplicationConfigProperties.getDayTotal();
                // 获取当期时间 + 1
                LocalDate currentDate = LocalDate.now();

                int num = lowesPriceDay % checkInDay == 0 ? lowesPriceDay / checkInDay : lowesPriceDay / checkInDay + 1;

                // 获取酒店ID
                List<String> spHotelIdAndHotelIdList = RedisTemplateX.setPopCount(RedisCacheKeyConstants.EPS_HOTEL_ID_COLD_LOWEST_PRICE, epsbldApplicationConfigProperties.getConsumerHotelIdCount());

                // 构建查询产品参数
                SupplyProductRequest supplyProductRequest = buildSupplyProductRequest();
                for (String spHotelIdStr : spHotelIdAndHotelIdList) {
                    try {
                        String[] hotelIdArr = spHotelIdStr.split("-");
                        String spHotelId = hotelIdArr[0];
                        supplyProductRequest.setSpHotelId(spHotelId);

                        List<SupplyHotelLowestPriceReqDTO> supplyHotelLowestPriceReqDTOS = new ArrayList<>();
                        SupplyHotelLowestPriceReqDTO lastLowestPrice = null;
                        int count = 0;
                        for (int i = 1; i <= num; i++) {
                            LocalDate checkInDate = currentDate.plusDays(count + 1);

                            int end = (count += checkInDay) + 1;
                            end = end > lowesPriceDay ? lowesPriceDay + 1 : end;
                            LocalDate checkoutDate = currentDate.plusDays(end);

                            supplyProductRequest.setCheckInDate(checkInDate.toString());
                            supplyProductRequest.setCheckOutDate(checkoutDate.toString());
                            ResultX<SupplyProductResponse> responseResultX = epsbldProductService.querySupplyProduct(supplyProductRequest);

                            if (responseResultX.isError() || CollUtilX.isEmpty(responseResultX.getData().getProductDetails())) {
                                break;
                            }

                            List<ProductMiddleDTO> productDetails = responseResultX.getData().getProductDetails();
                            ProductMiddleDTO productMiddleDto;
                            if (productDetails.size() == 1) {
                                productMiddleDto = productDetails.get(0);
                            } else {
                                // 找到最低价
                                productMiddleDto = productDetails.stream().min(Comparator.comparing(ProductMiddleDTO::getTotalAmount)).get();
                            }

                            // 证明是第一次查询的
                            List<ProductDetailMiddleDTO> productDetailsList = productMiddleDto.getProductDetails();
                            if (CollUtilX.isEmpty(productDetailsList)) {
                                // 证明是第一次查询 价格信息就为 null。那么直接结束这个酒店后续日期查询。
                                break;
                            }

                            for (ProductDetailMiddleDTO productDetail : productDetailsList) {
                                SupplyHotelLowestPriceReqDTO supplyHotelLowestPriceReqDTO = new SupplyHotelLowestPriceReqDTO();
                                BigDecimal lowestPrice = productDetail.getBasePrice();
                                supplyHotelLowestPriceReqDTO.setLowestPrice(lowestPrice);
                                supplyHotelLowestPriceReqDTO.setLowestPriceCurreny(productMiddleDto.getBaseCurrency());
                                supplyHotelLowestPriceReqDTO.setHotelId(hotelIdArr[1]);
                                supplyHotelLowestPriceReqDTO.setSaleDate(DateUtilX.strToLocalDate(productDetail.getSaleDate()));
                                supplyHotelLowestPriceReqDTO.setSupplyClass(SupplyClassEnum.EPSBLD.getSupplierClass());
                                supplyHotelLowestPriceReqDTO.setMerchantSource(epsbldApplicationConfigProperties.getMerchantSource());
                                supplyHotelLowestPriceReqDTO.setMerchantCode(epsbldApplicationConfigProperties.getMerchantCode());
                                supplyHotelLowestPriceReqDTO.setSupplyCode(epsbldApplicationConfigProperties.getSupplyCode());
                                supplyHotelLowestPriceReqDTO.setSupplyType(SupplyTypeEnum.DOMESTIC.getSupplyType());
                                supplyHotelLowestPriceReqDTO.setCreatedDt(LocalDateTime.now());
                                supplyHotelLowestPriceReqDTO.setCreatedBy("syncHotelLowestRunner");
                                supplyHotelLowestPriceReqDTO.setStatus(1);
                                lastLowestPrice = supplyHotelLowestPriceReqDTO;
                                supplyHotelLowestPriceReqDTOS.add(supplyHotelLowestPriceReqDTO);
                            }
                        }

                        // 补全起价 查询10天起价 一共需要30天 后续20天使用通过最后一天来补偿
                        if (CollUtilX.isNotEmpty(supplyHotelLowestPriceReqDTOS) && lastLowestPrice != null) {
                            for (int i = supplyHotelLowestPriceReqDTOS.size(); i < dayTotal; i++) {
                                SupplyHotelLowestPriceReqDTO cpLowestPriceReqDTO = new SupplyHotelLowestPriceReqDTO();
                                BeanUtils.copyProperties(lastLowestPrice, cpLowestPriceReqDTO);
                                LocalDate localDate = currentDate.plusDays(i + 1);
                                cpLowestPriceReqDTO.setSaleDate(localDate);
                                supplyHotelLowestPriceReqDTOS.add(cpLowestPriceReqDTO);
                            }
                        }
                        // 不为空则落地起价
                        if (CollectionUtil.isNotEmpty(supplyHotelLowestPriceReqDTOS)) {
                            ResultX resultX = supplyHotelLowestPriceApi.hotelLowestPriceAdd(supplyHotelLowestPriceReqDTOS);
                        }
                    } catch (Exception e) {
                        log.error("消费缓存同步全量起价数据任务异常", e);
                    }
                }
            }
            XxlJobHelper.log("执行消费缓存同步全量起价数据任务结束");
        } catch (Exception e) {
            log.error("执行消费缓存同步全量起价数据任务异常", e);
            XxlJobHelper.log("执行消费缓存同步全量起价数据任务异常", e);
        }
    }

    /**
     * 构建查询产品参数
     */
    private SupplyProductRequest buildSupplyProductRequest() {
        SupplyProductRequest supplyProductRequest = new SupplyProductRequest();
        supplyProductRequest.setMerchantSource(epsbldApplicationConfigProperties.getMerchantSource());
        supplyProductRequest.setSupplyCode(epsbldApplicationConfigProperties.getSupplyCode());
        supplyProductRequest.setMerchantCode(epsbldApplicationConfigProperties.getMerchantCode());
        RoomGuestNumberDTO roomGuestNumberDTO = new RoomGuestNumberDTO();
        roomGuestNumberDTO.setRoomIndex(1);
        // 默认2成人
        roomGuestNumberDTO.setAdultNum(2);
        supplyProductRequest.setRoomGuestNumbers(Collections.singletonList(roomGuestNumberDTO));

        return supplyProductRequest;
    }


    /**
     * 分页初始化酒店id到缓存
     *
     * @param hotelMappingQueryReqDTO 查询条件
     * @return 总页数
     */
    private long queryHotelMappingAndToCache(HotelMappingQueryReqDTO hotelMappingQueryReqDTO) {
        ResultX<PageResult<HotelMappingPageRespDTO>> pageResultResultX = hotelMappingApi.querySpHotelIdHotelMappingPage(hotelMappingQueryReqDTO);
        if (pageResultResultX.isSuccess()
                && pageResultResultX.getData() != null
                && CollUtilX.isNotEmpty(pageResultResultX.getData().getList())) {
            List<HotelMappingPageRespDTO> list = pageResultResultX.getData().getList();
            List<String> spHotelIdList = new ArrayList<>();
            for (HotelMappingPageRespDTO hotelMappingPageRespDTO : list) {
                spHotelIdList.add(hotelMappingPageRespDTO.getSpHotelIdAndHotelId());
            }
            loadSpHotelIdToCache(spHotelIdList);
            HotelMappingPageRespDTO lastMapping = list.get(list.size() - 1);
            hotelMappingQueryReqDTO.setId(lastMapping.getId());
            return pageResultResultX.getData().getTotalCount();
        }
        return 0L;
    }

    /**
     * 加载酒店id到缓存
     */
    private void loadSpHotelIdToCache(List<String> spHotelIdList) {
        if (CollectionUtil.isNotEmpty(spHotelIdList)) {
            RedisTemplateX.setAdd(RedisCacheKeyConstants.EPS_HOTEL_ID_COLD_LOWEST_PRICE, spHotelIdList.toArray(new String[0]));
        }
    }
}