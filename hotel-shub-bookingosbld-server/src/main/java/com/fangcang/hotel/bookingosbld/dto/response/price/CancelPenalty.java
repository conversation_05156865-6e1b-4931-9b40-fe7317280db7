package com.fangcang.hotel.bookingosbld.dto.response.price;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/18 18:46
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "CancelPenalty")
public class CancelPenalty {

    @XmlAttribute(name = "PolicyCode")
    private String policyCode;

    @XmlAttribute(name = "NonRefundable")
    private String nonRefundable;

    @XmlElement(name = "Deadline")
    private Deadline deadline;

    @XmlElement(name = "AmountPercent")
    private AmountPercent amountPercent;

    @XmlElementWrapper(name = "PenaltyDescription")
    @XmlElement(name = "Text")
    private List<String> textList;

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public String getNonRefundable() {
        return nonRefundable;
    }

    public void setNonRefundable(String nonRefundable) {
        this.nonRefundable = nonRefundable;
    }

    public Deadline getDeadline() {
        return deadline;
    }

    public void setDeadline(Deadline deadline) {
        this.deadline = deadline;
    }

    public AmountPercent getAmountPercent() {
        return amountPercent;
    }

    public void setAmountPercent(AmountPercent amountPercent) {
        this.amountPercent = amountPercent;
    }

    public List<String> getTextList() {
        return textList;
    }

    public void setTextList(List<String> textList) {
        this.textList = textList;
    }
}
