package com.fangcang.hotel.data.api.dto;

import lombok.Data;

/**
 * 查询不落地供应商产品返回结果DTO
 * 用于缓存报价信息
 */
@Data
public class QuerySupplyCacheProductResponse {
    private String spHotelId;

    private String checkinDate;

    private String checkoutDate;

    private String supplyCode;

    private String merchantCode;

    /**成功：1   失败：0*/
    private Integer status;

    /**详情描述*/
    private String msg;

    /**不落地产品查询返回结果*/
    private String cacheProduct;

}
