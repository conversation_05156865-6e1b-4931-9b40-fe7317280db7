package com.fangcang.hotel.bookingosbld.dto.request.order.cancel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/29 19:14
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "cancelElements")
public class CancelElements {


    @XmlElement(name = "entryType")
    private String entryType;


    @XmlElement(name = "element")
    private Element element;

    public String getEntryType() {
        return entryType;
    }

    public void setEntryType(String entryType) {
        this.entryType = entryType;
    }

    public Element getElement() {
        return element;
    }

    public void setElement(Element element) {
        this.element = element;
    }
}
