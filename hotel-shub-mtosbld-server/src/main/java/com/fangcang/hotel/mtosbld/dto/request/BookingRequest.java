package com.fangcang.hotel.mtosbld.dto.request;

import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @Date 2024/8/16 下午6:39
 * @Description:
 */

@Data
public class BookingRequest extends BaseRequest {

    /**
     * 订单号
     */
    private String bookingReferenceId;

    /**
     * 入住日期，格式：2022-01-01
     */
    private String checkIn;

    /**
     * 离店日期，格式：2022-01-02
     */
    private String checkOut;

    /**
     * 酒店ID，酒店标识
     */
    private String hotel;

    /**
     * 房间ID，房间标识
     */
    private String roomCode;

    /**
     * 餐食，RO 不含餐；BB 含早餐
     */
    private String boardCode;

    /**
     * 价格计划ID
     */
    private String ratePlanCode;

    /**
     * 渠道，默认：1
     */
    private Integer channel;

    /**
     * 币种，默认：CNY
     */
    private String currency;

    /**
     * 房间数
     */
    private Integer quantity;

    /**
     * 订单金额
     */
    private BigDecimal totalPrice;

    /**
     * 客人信息
     */
    private Guest guest;

    /**
     * 特殊要求，长度小于500字符
     */
    private String specialRequests;

    /**
     * 客人数量
     */
    private GuestCount guestCount;

}
