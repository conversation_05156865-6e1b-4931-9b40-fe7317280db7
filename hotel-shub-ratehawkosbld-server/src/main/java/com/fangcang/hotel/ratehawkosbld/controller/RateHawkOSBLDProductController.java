package com.fangcang.hotel.ratehawkosbld.controller;


import com.fangcang.hotel.data.api.dto.HotelDetailResponse;
import com.fangcang.hotel.data.api.dto.HotelProductRequest;
import com.fangcang.hotel.data.api.remote.SupplyProductServiceRemote;
import com.fangcang.hotel.data.base.services.CommonProductService;
import com.fangcang.hotel.framework.common.domain.ResultX;
import com.fangcang.hotel.framework.operatelog.auto.SlsLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 查询供应的产品信息
 */
@RestController
@Slf4j
public class RateHawkOSBLDProductController implements SupplyProductServiceRemote {

    @Autowired
    private CommonProductService commonProductService;

    @Autowired
    private SlsLogger slsLogger;


    /**
     * 查询供应的产品信息。
     *
     * @param hotelProductRequest
     * @return
     */
    @Override
    public ResultX<HotelDetailResponse> querySupplyProduct(@RequestBody HotelProductRequest hotelProductRequest) {
        return commonProductService.queryHotelDetail(hotelProductRequest);
    }
}
