package com.fangcang.hotel.ratehawkosbld.util;

import java.util.List;

/**
 * <AUTHOR>
 * @date ： 2024/3/19
 */
public class PageUtil {

    /**
     * 获取集合的总页数
     *
     * @param list
     * @param <T>
     * @return
     */
    public static <T> Integer getTotalPage(List<T> list) {
        int totalCount = list.size();
        int pageSize = 2000;
        int totalPage = totalCount/2000;
        if(totalCount != totalPage*pageSize){
            totalPage ++;
        }
        return totalPage;
    }



    /**
     * 获取集合中的分页数据
     *
     * @param list
     * @param pageIndex 从1开始
     * @param <T>
     * @return
     */
    public static <T> List<T> page(List<T> list, int pageIndex) {
        int totalCount = list.size();
        int pageSize = 2000;

        int pageStartIndex = (pageIndex-1)*pageSize;
        int pageEndIndex = pageIndex*pageSize;
        if(pageEndIndex > totalCount) {
            pageEndIndex = totalCount;
        }
        return list.subList(pageStartIndex, pageEndIndex);
    }

}
