package com.fangcang.hotel.tmchubosbld.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 *  语言枚举
 */
@Getter
@AllArgsConstructor
public enum DHUBOSBLDLanguageEnum {
    zh_CN("zh_CN","zh-CN", "中文"),
    en_US("en_US","en-US","英文"),
    ;

    public final String code;
    public final String value;
    public final String msg;

    public static String getValueByCode(String code) {
        String value = null;
        for(DHUBOSBLDLanguageEnum languageEnum : DHUBOSBLDLanguageEnum.values()) {
            if(Objects.equals(languageEnum.code, code)) {
                value = languageEnum.value;
                break;
            }
        }
        return value;
    }

    public static String getCodeByValue(String value) {
        String code = null;
        for(DHUBOSBLDLanguageEnum languageEnum : DHUBOSBLDLanguageEnum.values()) {
            if(languageEnum.value .equals(value)) {
                code = languageEnum.code;
                break;
            }
        }
        return code;
    }

    public static DHUBOSBLDLanguageEnum getEnumByCode(String code){
        DHUBOSBLDLanguageEnum languageEnum = null;
        for(DHUBOSBLDLanguageEnum language : DHUBOSBLDLanguageEnum.values()) {
            if(Objects.equals(language.code, code)) {
                languageEnum = language;
                break;
            }
        }
        return languageEnum;
    }

    public static DHUBOSBLDLanguageEnum getEnumByValue(String value) {
        DHUBOSBLDLanguageEnum languageEnum = null;
        for(DHUBOSBLDLanguageEnum language : DHUBOSBLDLanguageEnum.values()) {
            if(Objects.equals(language.value, value)) {
                languageEnum = language;
                break;
            }
        }
        return languageEnum;
    }
}
