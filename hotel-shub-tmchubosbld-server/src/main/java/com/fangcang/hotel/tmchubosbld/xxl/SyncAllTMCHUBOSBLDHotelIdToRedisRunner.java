package com.fangcang.hotel.tmchubosbld.xxl;

import cn.hutool.core.util.StrUtil;
import com.fangcang.hotel.tmchubosbld.service.TMCHUBOSBLDHotelService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 获取tmchubos不落地可售酒店ID列表存进缓存任务
 */
@Slf4j
@Component
public class SyncAllTMCHUBOSBLDHotelIdToRedisRunner {

    @Autowired
    private TMCHUBOSBLDHotelService TMCHUBOSBLDHotelService;

    @XxlJob("syncAllTMCHUBOSBLDHotelIdToRedisRunner")
    public void syncAllTMCHUBOSBLDHotelIdToRedisRunner() {
        try {
            String jobParam = XxlJobHelper.getJobParam();
            XxlJobHelper.log("执行获取TMCHUBOS不落地可售酒店ID列表存进缓存任务开始");
            // 是否增量更新 true:增量更新 false:全量更新 默认增量更新
            TMCHUBOSBLDHotelService.syncAllTMCHUBOSBLDHotelIdToRedisTask(StrUtil.isNotBlank(jobParam) ? jobParam.equals("true") : true);
            XxlJobHelper.log("执行获取TMCHUBOS不落地可售酒店ID列表存进缓存任务结束");
        } catch (Exception e) {
            log.error("执行全量获取TMCHUBOS不落地酒店id存进缓存任务异常", e);
            XxlJobHelper.log("执行获取TMCHUBOS不落地可售酒店ID列表存进缓存任务异常", e);
        }
    }
}
