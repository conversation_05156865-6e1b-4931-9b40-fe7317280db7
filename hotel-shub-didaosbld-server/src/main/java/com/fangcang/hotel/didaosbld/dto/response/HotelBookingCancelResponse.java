package com.fangcang.hotel.didaosbld.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/12/4 17:45
 * @Description:
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HotelBookingCancelResponse extends BaseResponse {

    @JSONField(name = "Success")
    private HotelBookingCancelDataResponse success;

}
