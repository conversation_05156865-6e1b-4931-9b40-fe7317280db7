package com.fangcang.hotel.amadeusosbld.dto.common;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/15 17:02
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "UserID")
public class UserID {

    @XmlAttribute(name = "AgentDutyCode")
    private String agentDutyCode;

    @XmlAttribute(name = "POS_Type")
    private String posType;

    @XmlAttribute(name = "PseudoCityCode")
    private String pseudoCityCode;

    @XmlAttribute(name = "RequestorType")
    private String requestorType;

    public String getAgentDutyCode() {
        return agentDutyCode;
    }

    public void setAgentDutyCode(String agentDutyCode) {
        this.agentDutyCode = agentDutyCode;
    }

    public String getPosType() {
        return posType;
    }

    public void setPosType(String posType) {
        this.posType = posType;
    }

    public String getPseudoCityCode() {
        return pseudoCityCode;
    }

    public void setPseudoCityCode(String pseudoCityCode) {
        this.pseudoCityCode = pseudoCityCode;
    }

    public String getRequestorType() {
        return requestorType;
    }

    public void setRequestorType(String requestorType) {
        this.requestorType = requestorType;
    }
}
