package com.fangcang.hotel.bookingosbld.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.fangcang.hotel.bookingosbld.config.BookingOSBLDExtendConfig;
import com.fangcang.hotel.bookingosbld.utils.BookingOSBldSyncUtil;
import com.fangcang.hotel.common.api.common.config.dto.ConfigInfoRespDTO;
import com.fangcang.hotel.data.api.enums.SupplyClassEnum;
import com.fangcang.hotel.data.api.service.SupplyOrderService;
import com.fangcang.hotel.data.api.service.SupplyProductService;
import com.fangcang.hotel.data.base.annotation.SupplyConfigPlugin;
import com.fangcang.hotel.data.base.services.CacheService;
import com.fangcang.hotel.data.base.services.BaseSupplyConfigService;
import com.fangcang.hotel.framework.operatelog.auto.SlsLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("bookingosBldExtendsConfigService")
@SupplyConfigPlugin(supplyClass = "BOOKINGOSBLD")
public class BookingOSBLDExtendsConfigSupplyConfigServiceImpl extends BaseSupplyConfigService {

    @Autowired
    private CacheService cacheService;

    @Autowired
    private SupplyOrderService bookingosBldOrderService;

    private static Map<String, BookingOSBLDExtendConfig> bookingosBldExtendConfigMap = new HashMap<>();


    @Autowired
    private SupplyProductService bookingosBldProductService;

    @Autowired
    private SlsLogger slsLogger;


    @Override
    public SupplyOrderService setOrderService() {
        return bookingosBldOrderService;
    }

    /**
     * 获取实时查询产品服务
     *
     * @return
     */
    @Override
    public SupplyProductService getSupplyProductService() {
        return bookingosBldProductService;
    }

    @Override
    public void getAllConfigInfo(List<ConfigInfoRespDTO> configInfoRespDTOS) {
        for (ConfigInfoRespDTO configInfoRespDTO : configInfoRespDTOS) {
            String key = configInfoRespDTO.getMerchantSource() + "_" + configInfoRespDTO.getMerchantCode()
                    + "_" + configInfoRespDTO.getSupplyCode();
            try {
                BookingOSBLDExtendConfig bookingOSBLDExtendConfig = (BookingOSBLDExtendConfig) cacheService.getCacheConfigExtend(configInfoRespDTO.getMerchantSource(),
                        configInfoRespDTO.getMerchantCode(), SupplyClassEnum.BOOKINGOSBLD, configInfoRespDTO.getSupplyCode(), BookingOSBLDExtendConfig.class);
                bookingosBldExtendConfigMap.put(key, bookingOSBLDExtendConfig);
            } catch (Exception e) {
                log.error("booking不落地供应商未配置:" + key, e);
                BookingOSBldSyncUtil.saveErrorSlsLog("error", SupplyClassEnum.BOOKINGOSBLD.getSupplierClass(),"booking不落地供应商未配置:" + key+ BookingOSBldSyncUtil.getStackTrace(e),slsLogger);
            }
        }
    }

    public Map<String, BookingOSBLDExtendConfig> getbookingOSBldExtendConfigMap() {
        if (CollectionUtil.isEmpty(bookingosBldExtendConfigMap)) {
            //重新注册
            registerService();
        }
        return bookingosBldExtendConfigMap;
    }
}
