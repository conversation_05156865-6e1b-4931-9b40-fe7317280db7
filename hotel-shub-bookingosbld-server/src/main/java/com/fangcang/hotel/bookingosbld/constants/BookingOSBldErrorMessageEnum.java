package com.fangcang.hotel.bookingosbld.constants;


import com.fangcang.hotel.bookingosbld.utils.StringUtil;

/**
 * <AUTHOR>
 * @date 2022/9/2 10:43
 */
public enum BookingOSBldErrorMessageEnum {

    UNABLE_TO_PROCESS_CONTACT_HELP_DESK("UNABLE TO PROCESS - CONTACT HELP DESK", "系统链接错误。请务必联系供应商，核实是否已经生成订单，若无单，请线下联系酒店询房手工预定"),
    NO_AVAILABILITY("No availability", "房间满房"),
    INVALID_MAX_NUMBER_OF_ROOMS_EXCEEDED("Invalid - max number of rooms exceeded", "超过最大预定间数，请拆单后重试"),
    UNABLE_TO_PROCESS("UNABLE TO PROCESS", "供应商需要进行担保，请修改订单的担保状态并再次点击发送预订单"),
    NO_ROOMS_AVAILABLE_AT_REQUESTED_PROPERTY("NO ROOMS AVAILABLE AT REQUESTED PROPERTY", "房间满房"),
    ROOM_TYPE_RATE_CODE_NOT_AVAILABLE("ROOM TYPE / RATE CODE NOT AVAILABLE", "房间满房"),
    CHECK_PASSENGER_ASSOCIATION("CHECK PASSENGER ASSOCIATION", "入住人姓名有误，请修改名字后重试"),
    INVALID_FORM_OF_GUARANTEE("Invalid Form of Guarantee", "供应商需要进行担保，请修改订单的担保状态并再次点击发送预订单"),
    DEPOSIT_REQUIRED("DEPOSIT REQUIRED", "供应商需要进行担保，请修改订单的担保状态并再次点击发送预订单"),
    INVALID_CREDIT_CARD_NUMBER("INVALID CREDIT CARD NUMBER","无效卡号"),
    CODE_378("378", "超过最大预定间数，请拆单后重试"),
    ID_NOT_RECOGNISED("/ID- NOT RECOGNISED", "入住人会员卡信息错误，请核实后修改为正确会员卡后重试"),
    INVALID_PASSENGER_ASSOCIATION("INVALID PASSENGER ASSOCIATION", "无效的入住人姓名，请修改名字后重试"),
    GUARANTEE_REQUIRED("GUARANTEE REQUIRED", "必须担保"),
    CODE_450("450", "系统链接错误。请务必联系供应商，核实是否已经生成订单，若无单，请线下联系酒店询房手工预定"),
    CODE_696("696", "系统链接错误。请务必联系供应商，核实是否已经生成订单，若无单，请线下联系酒店询房手工预定"),
    CODE_366("366", "系统链接错误。请务必联系供应商，核实是否已经生成订单，若无单，请线下联系酒店询房手工预定"),
    CODE_CTL("CTL", "系统链接错误。请务必联系供应商，核实是否已经生成订单，若无单，请线下联系酒店询房手工预定"),
    CODE_437("437", "产品不可用。请务必联系供应商，核实是否已经生成订单，若无单，确认是否满房后，可新建供货单重试发单"),
    CODE_322("322", "房间满房"),
    CODE_SCI("SCI", "预定产品信息失败，请务必联系供应商，核实是否已经生成订单，若无单，确认是否满房后，可新建供货单重试发单"),
    CODE_784("784", "Time out。请稍后重试");

    public String message;
    public String value;

    BookingOSBldErrorMessageEnum(String message, String value) {
        this.message = message;
        this.value = value;
    }

    public static String getValueByMessage(String key) {
        if (!StringUtil.isValidString(key)) {
            return null;
        }
        for (BookingOSBldErrorMessageEnum responseEnum : BookingOSBldErrorMessageEnum.values()) {
            if (responseEnum.message.toLowerCase().replaceAll(" ", "").equals(key.toLowerCase().replaceAll(" ", ""))) {
                return responseEnum.value;
            }
        }
        return null;
    }

}
