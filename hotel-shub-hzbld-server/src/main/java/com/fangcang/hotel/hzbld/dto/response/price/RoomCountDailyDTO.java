package com.fangcang.hotel.hzbld.dto.response.price;

import java.util.Date;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  每日房量信息
 *
 * <AUTHOR>
 * @date 2023-12-11 14:22:34
 */
@Data
public class RoomCountDailyDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 房型
     */
    private String roomTypeId;

    /**
     * 营业日
     */
    private String bizDate;

    /**
     * 保留房量
     */
    private Integer retainCount;

    /**
     * 共享房量
     */
    private Integer shareCount;

    /**
     * 可售房量
     */
    private Integer availableCount;

    /**
     * 是否禁用
     */
    private Boolean isForbidden;

    /**
     * 房型是否黑名单
     */
    private Boolean isBlack;

}
