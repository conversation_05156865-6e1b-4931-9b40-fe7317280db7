package com.fangcang.hotel.common.dal.db.operatelog;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 操作日志 DO
 *
 * <AUTHOR>
 */
@TableName("t_operate_log")
@KeySequence("t_operate_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OperateLogDO implements Serializable {

    /** id */
    @TableId
    private Long id;

    /** 操作id 业务id 用于和业务数据关联 */
    private Long operateId;

    /**
     * 供应类型
     */
    private String supplyClass;

    /** 日志类型 [商家来源,供应商模板] */
    private Integer logType;

    /** 操作类型 [创建,删除,修改] */
    private Integer operateType;

    /** 修改前数据 */
    private String oldData;

    /** 修改后数据 */
    private String newData;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdDt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT, jdbcType = JdbcType.VARCHAR)
    private String createdBy;
}
