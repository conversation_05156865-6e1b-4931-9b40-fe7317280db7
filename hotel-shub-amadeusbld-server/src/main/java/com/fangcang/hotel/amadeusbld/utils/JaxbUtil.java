package com.fangcang.hotel.amadeusbld.utils;

import org.springframework.web.util.HtmlUtils;
import org.xml.sax.EntityResolver;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.XMLReader;
import org.xml.sax.helpers.DefaultHandler;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.parsers.SAXParserFactory;
import javax.xml.transform.sax.SAXSource;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;

public class JaxbUtil {
    private static final String ENCODING = "UTF-8";

    private static JAXBContext getJaxbInstance(Class clazz) throws JAXBException {
        return JAXBContext.newInstance(clazz);
    }

    /**
     * 将 model 转为 xml 字符串
     *
     * @param o
     * @param fragment 是否省略xml头信息
     * @return
     * @throws JAXBException
     */
    public static String toXml(Object o, boolean fragment, boolean outputFormat) throws JAXBException {
        JAXBContext jaxbInstance = getJaxbInstance(o.getClass());
        Marshaller marshaller = jaxbInstance.createMarshaller();

        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, outputFormat);
        marshaller.setProperty(Marshaller.JAXB_ENCODING, ENCODING);
        marshaller.setProperty(Marshaller.JAXB_FRAGMENT, fragment);

        StringWriter stringWriter = new StringWriter();
        marshaller.marshal(o, stringWriter);
        return HtmlUtils.htmlUnescape(stringWriter.toString());
    }

    /**
     * 将 xml 字符串 转为 model
     *
     * @param clazz
     * @param xml   要转的xml字符串
     * @return
     * @throws JAXBException
     */
    public static <T> T toModel(Class<T> clazz, String xml) throws JAXBException {
        Unmarshaller unmarshaller = getJaxbInstance(clazz).createUnmarshaller();
        SAXSource source = new SAXSource();
        StringReader stringReader = new StringReader(xml);
        source.setInputSource(new InputSource(stringReader));
        source.setXMLReader(getXMLReader());
        T model = (T) unmarshaller.unmarshal(source);
        return model;
    }


    protected static XMLReader getXMLReader() throws JAXBException {
        XMLReader reader;
        try {
            SAXParserFactory parserFactory;
            parserFactory = SAXParserFactory.newInstance();
            parserFactory.setNamespaceAware(false);

            parserFactory.setValidating(false);
            reader = parserFactory.newSAXParser().getXMLReader();
            reader.setErrorHandler(new DefaultHandler());
            reader.setEntityResolver(new EntityResolver() {
                @Override
                public InputSource resolveEntity(String publicId, String systemId)
                        throws SAXException, IOException {
                    return new InputSource();
                }
            });
        } catch (ParserConfigurationException | SAXException e) {
            throw new JAXBException(e);
        }
        return reader;
    }


}
