package com.fangcang.hotel.irevebld.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品退订费
 */
@AllArgsConstructor
@Getter
public enum IREVEBLDProductRestrictEnum {

	/**    取消类型   */
	CANCEL_NOT(0,"不可取消"),
	CANCEL_FREE(1,"可取消，免费"),
	CANCEL_NOT_FREE(2,"可取消，收费"),

	/**    担保类型   */
	GUARANTEE_TIME(0, "到店时间担保"),
	GUARANTEE_NUM(1, "预订间数担保"),
	GUARANTEE_IMMEDIATELY(2, "即订即保"),

	/**    退订费类型   */
	FULL(0,"全额"),
	FULL_NOT(1,"首晚");


	public final int key;
	public final String value;
	
}
