package com.fangcang.hotel.common.service.baseinfo;

import com.fangcang.hotel.common.api.common.baseinfo.dto.*;
import com.fangcang.hotel.common.controller.admin.baseinfo.vo.HotelInfoReqVO;
import com.fangcang.hotel.common.controller.admin.baseinfo.vo.HotelInfoRespVO;
import com.fangcang.hotel.common.controller.admin.baseinfo.vo.RoomInfoReqVO;
import com.fangcang.hotel.common.controller.admin.baseinfo.vo.RoomInfoRespVO;
import com.fangcang.hotel.framework.common.domain.PageResult;
import com.fangcang.hotel.framework.common.domain.ResultX;

import javax.validation.Valid;
import java.util.List;

/**
 * @description: 基础信息
 * @author: qiu
 * @create: 2024-03-25 11:30
 */
public interface BaseInfoService {

    /**
     * 查询酒店列表
     *
     * @param reqVo 请求参数
     * @return 结果
     */
    ResultX<List<HotelInfoRespVO>> queryHotelList(@Valid HotelInfoReqVO reqVo);

    /**
     * 查询房型列表
     * @param reqVo 请求参数
     * @return 结果
     */
    ResultX<List<RoomInfoRespVO>> queryRoomList(@Valid RoomInfoReqVO reqVo);


    /**
     * 查询酒店所属城市信息
     * @param commonReqDTO
     * @return
     */
    ResultX<List<LocationDTO>> queryHotelLocationList(@Valid CommonReqDTO commonReqDTO);


    /**
     * 获取商家币种
     * @return
     */
    ResultX<String> queryMerchatnCurrency();

    /**
     * 查询汇率
     * @param reqDTO
     * @return
     */
    ResultX<PageResult<ExchangeRateRespDTO>> queryExchangeRate(@Valid QueryExchangeReqDTO reqDTO);

}