package com.fangcang.hotel.ratehawkosbld.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  价格是否发生改变
 *
 * <AUTHOR>
 * @date 2024-05-30 17:04:51
 */
@Data
public class Changes implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 新费率中的价格是否与原始价格不同。
     */
    @JSONField(name = "price_changed")
    private Boolean priceChanged;

}
