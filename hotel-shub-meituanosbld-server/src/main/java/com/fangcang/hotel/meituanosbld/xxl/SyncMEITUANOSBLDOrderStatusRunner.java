package com.fangcang.hotel.meituanosbld.xxl;

import com.fangcang.hotel.meituanosbld.service.impl.MEITUANOSBLDOrderServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 起价获取meituanos不落地酒店id存进缓存任务
 */
@Slf4j
@Component
public class SyncMEITUANOSBLDOrderStatusRunner {

    @Autowired
    private MEITUANOSBLDOrderServiceImpl meituanosbldOrderService;

    @XxlJob("syncMEITUANOSBLDOrderStatusTask")
    public void syncLowestPriceMEITUANOSBLDHotelIdToRedisRunner() {
        try {
            XxlJobHelper.log("执行同步meituanos不落地订单状态任务开始");
            meituanosbldOrderService.syncMEITUANOSBLDOrderStatusTask();
            XxlJobHelper.log("执行同步meituanos不落地订单状态结束");
        } catch (Exception e) {
            log.error("执行起价获取meituanos不落地酒店id存进缓存任务异常", e);
            XxlJobHelper.log("执行同步meituanos不落地订单状态异常", e);
        }
    }
}
