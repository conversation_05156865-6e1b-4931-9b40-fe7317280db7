package com.fangcang.hotel.common.api.common.mapping.dto;

import com.fangcang.hotel.common.api.common.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 房型映射DTO
 * @author: qiu
 * @create: 2023-08-18 14:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HotelMappingErrorReqDTO extends BaseDTO {

    private Long id;

    /** 酒店id */
    private String hotelId;

    /** 酒店名称 */
    private String hotelName;

    /** 房型ID */
    private String roomId;

    /** 房型名称 */
    private String roomName;

    /** 供应商酒店ID */
    private String spHotelId;

    /** 供应商房型ID */
    private String spRoomId;

    /** 类型：1-国内 2-海外 ["国内","海外"] */
    private String supplyType;

    /** 数据状态 0-未处理 1-已处理 */
    private Integer dataStatus;

    /** 数据类型 1:酒店 2:房型 3:映射 */
    private String dataType;

    /** 错误原因 */
    private String errorCause;

}