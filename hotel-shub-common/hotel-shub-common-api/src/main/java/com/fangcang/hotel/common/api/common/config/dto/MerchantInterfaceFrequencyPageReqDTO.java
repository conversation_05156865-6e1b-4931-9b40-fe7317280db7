package com.fangcang.hotel.common.api.common.config.dto;

import com.fangcang.hotel.framework.common.domain.PageParam;
import lombok.Data;

/**
 * @description:
 * @author: qiu
 * @create: 2023-12-26 21:00
 */
@Data
public class MerchantInterfaceFrequencyPageReqDTO extends PageParam {

    /** 商家来源 */
    private String merchantSource;

    /** 商家编码 */
    private String merchantCode;

    /** 接口类型 */
    private String interfaceType;

    /** 调用次数 */
    private Integer invokeTimes;

    /** 状态 [有效,无效] */
    private Integer status;

}