package com.fangcang.hotel.irevebld.dto.response.product;

import com.fangcang.hotel.irevebld.enums.IREVEBLDRoomStatusEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PriceQuotaDto {
    /**
     * 售卖日期
     */
    private String saleDate;
    /**
     * 售价
     */
    private BigDecimal salePrice;
    /**
     * 剩余配额数
     */
    private Integer remainingQuota;
    /**
     * 房态
     * @see IREVEBLDRoomStatusEnum
     */
    private Integer roomStatus;
}
