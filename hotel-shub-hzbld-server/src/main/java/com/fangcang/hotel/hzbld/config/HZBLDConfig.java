package com.fangcang.hotel.hzbld.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 *
 *  供应商对接配置参数
 * 读取 nacos yml配置文件的属性
 * 一般用于配置一些公共的属性 例如：接口地址、超时时间等
 * <AUTHOR>
 * @date 2023-12-15 11:30:30
 */

@RefreshScope //动态刷新配置
@Component //注入到spring容器中
@Data  //lombok注解，自动生成get/set方法
public class HZBLDConfig{

    /**
     * 请求超时（毫秒）
     */
    @Value("${hzbld.application.config.connectTimeOut}")
    private Integer connectTimeOut = 5000;

    /**
     * 响应超时（毫秒）
     */
    @Value("${hzbld.application.config.socketTimeOut}")
    private Integer socketTimeOut = 20000;

    /**
     * 授权范围
     */
    @Value("${hzbld.application.config.scope}")
    private String scope;

    /**
     * 授权方式
     */
    @Value("${hzbld.application.config.grantType}")
    private String grantType;

    /**
     * 鉴权接口地址
     */
    @Value("${hzbld.application.config.tokenUrl}")
    private String tokenUrl;

    /**
     * 获取token接口
     */
    @Value("${hzbld.application.config.tokenPath}")
    private String tokenPath;

    /**
     * 业务接口地址
     */
    @Value("${hzbld.application.config.apiUrl}")
    private String apiUrl;

    /** 商家来源 */
    @Value("${hzbld.application.config.merchantSource}")
    private String merchantSource;

    /**
     * 商家编码
     */
    @Value("${hzbld.application.config.merchantCode}")
    private String merchantCode;

    /**
     * 供应类型
     */
    @Value("${hzbld.application.config.supplyClass}")
    private String supplyClass;

    /**
     * 供应商编码
     */
    @Value("${hzbld.application.config.supplyCode}")
    private String supplyCode;

    /**
     * 房仓sass鉴权接口地址
     */
    @Value("${hzbld.application.config.fcSassTokenUrl}")
    private String fcSassTokenUrl;

    /**
     * 房仓sass获取token接口
     */
    @Value("${hzbld.application.config.fcSassTokenPath}")
    private String fcSassTokenPath;

    /**
     * 房仓sass请求ID
     */
    @Value("${hzbld.application.config.fcSassRequestId}")
    private String fcSassRequestId;

    /**
     * 房仓sass供应商编码
     */
    @Value("${hzbld.application.config.fcSassSupplyCode}")
    private String fcSassSupplyCode;

    /**
     * 房仓sass密钥
     */
    @Value("${hzbld.application.config.fcSassAppSecret}")
    private String fcSassAppSecret;

    /**
     * 订单发送超时时间
     */
    @Value("${hzbld.application.config.sendOrderTimeOut}")
    private Integer sendOrderTimeOut = 60000;

}

