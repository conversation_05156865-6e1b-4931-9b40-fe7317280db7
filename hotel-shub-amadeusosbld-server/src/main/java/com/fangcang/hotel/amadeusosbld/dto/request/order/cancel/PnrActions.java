package com.fangcang.hotel.amadeusosbld.dto.request.order.cancel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/29 19:11
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "pnrActions")
public class PnrActions {

    @XmlElement(name = "optionCode")
    private String optionCode;

    public String getOptionCode() {
        return optionCode;
    }

    public void setOptionCode(String optionCode) {
        this.optionCode = optionCode;
    }
}
