package com.fangcang.hotel.amadeusbld.dto.response.hotel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/22 12:14
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "GuestRoomInfo")
public class GuestRoomInfo {

    @XmlAttribute(name = "Code")
    private String code;

    @XmlAttribute(name = "Quantity")
    private String quantity;

    @XmlElementWrapper(name = "MultimediaDescriptions")
    @XmlElement(name = "MultimediaDescription")
    private List<MultimediaDescription> multimediaDescriptionList;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public List<MultimediaDescription> getMultimediaDescriptionList() {
        return multimediaDescriptionList;
    }

    public void setMultimediaDescriptionList(List<MultimediaDescription> multimediaDescriptionList) {
        this.multimediaDescriptionList = multimediaDescriptionList;
    }
}
