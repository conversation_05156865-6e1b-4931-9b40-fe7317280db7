package com.fangcang.hotel.common.service.mapping;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.fangcang.hotel.common.api.common.mapping.dto.AreasReqDTO;
import com.fangcang.hotel.common.api.common.mapping.dto.AreasRespDTO;
import com.fangcang.hotel.common.config.CommonConfig;
import com.fangcang.hotel.common.constants.CommonConstants;
import com.fangcang.hotel.common.controller.admin.mapping.vo.AreasReqVO;
import com.fangcang.hotel.common.controller.admin.mapping.vo.CityMappingAditReqVO;
import com.fangcang.hotel.common.controller.admin.mapping.vo.CityMappingPageReqVO;
import com.fangcang.hotel.common.controller.admin.mapping.vo.CityMappingQueryReqVO;
import com.fangcang.hotel.common.convert.mapping.CityMappingConvert;
import com.fangcang.hotel.common.dal.db.mapping.CityMappingDO;
import com.fangcang.hotel.common.dal.mysql.mapping.CityMappingMapper;
import com.fangcang.hotel.core.enums.GlobalErrorCodeEnum;
import com.fangcang.hotel.framework.common.domain.PageResult;
import com.fangcang.hotel.framework.common.domain.ResultX;
import com.fangcang.hotel.framework.common.util.HttpUtilX;
import com.fangcang.hotel.framework.mybatis.core.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;


import static com.fangcang.hotel.framework.common.exception.util.BizExceptionUtilX.exception;

/**
 * 城市映射 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CityMappingServiceImpl implements CityMappingService {

    @Resource
    private CityMappingMapper cityMappingMapper;


    @Autowired
    private CommonConfig commonConfig;

    /**
     * 查询城市列表
     *
     * @param areasReqDTO
     * @return
     */
    @Override
    public ResultX<List<AreasRespDTO>> queryAreasList(AreasReqDTO areasReqDTO) {
        try {
            String url = commonConfig.getHotelBasicsManageUrl() + CommonConstants.AREALIST_URL;

            AreasReqVO areasReqVO = CityMappingConvert.INSTANCE.convert(areasReqDTO);
            areasReqVO.setKeyWord(areasReqDTO.getCityName());

            String result = HttpUtil.post(url, JSONUtil.toJsonStr(areasReqVO));
            return JSONUtil.toBean(result, new TypeReference<ResultX<List<AreasRespDTO>>>() {
            }, false);
        } catch (Exception e) {
            log.error("查询城市列表异常", e);
            return ResultX.error(GlobalErrorCodeEnum.SYSTEM_ERROR);
        }
    }


    @Override
    public ResultX<List<AreasRespDTO>> queryCountryList(AreasReqDTO areasRequest) {
        try {
            String url = commonConfig.getHotelBasicsManageUrl() + CommonConstants.AREALIST_URL;

            AreasReqVO areasReqVO = CityMappingConvert.INSTANCE.convert(areasRequest);
            areasReqVO.setKeyWord(areasRequest.getCountryName());

            String result = HttpUtil.post(url, JSONUtil.toJsonStr(areasReqVO));
            return JSONUtil.toBean(result, new TypeReference<ResultX<List<AreasRespDTO>>>() {
            }, false);
        } catch (Exception e) {
            log.error("查询城市列表异常", e);
            return ResultX.error(GlobalErrorCodeEnum.SYSTEM_ERROR);
        }
    }

    @Override
    public Long cityMappingAdd(CityMappingAditReqVO reqVO) {
        // 插入
        CityMappingDO cityMapping = CityMappingConvert.INSTANCE.convert(reqVO);
        cityMappingMapper.insert(cityMapping);
        // 返回
        return cityMapping.getId();
    }


    @Override
    public void cityMappingAdd(List<CityMappingAditReqVO> reqVO) {
        if (CollectionUtil.isEmpty(reqVO)){
            return;
        }


        //供应类型
        String supplyClass = reqVO.get(0).getSupplyClass();

        //城市编码集合
        List<String> spCityCodeList = new ArrayList<String>();

        for (CityMappingAditReqVO city : reqVO) {
            spCityCodeList.add(city.getSpCityCode());
        }

        LambdaQueryWrapperX<CityMappingDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.in(CityMappingDO::getSpCityCode, spCityCodeList);
        queryWrapper.in(CityMappingDO::getSupplyClass, supplyClass);
        //查询已存在的映射
        List<CityMappingDO> cityMappingDOS = cityMappingMapper.selectList(queryWrapper);
        if(CollectionUtil.isNotEmpty(cityMappingDOS)){
            Set<String> spCityCodes = new HashSet<String>();
            for (CityMappingDO cityMappingDO : cityMappingDOS) {
                spCityCodes.add(cityMappingDO.getSpCityCode());
            }
            //删除已经存在的城市映射
            Iterator<CityMappingAditReqVO> iterator = reqVO.iterator();
            while (iterator.hasNext()) {
                // 剔除已映射城市
                if (spCityCodes.contains(iterator.next().getSpCityCode())){
                    iterator.remove();
                }
            }
        }

        //落地城市映射
        if (CollectionUtil.isNotEmpty(reqVO)) {
            List<CityMappingDO> cityMappings = CityMappingConvert.INSTANCE.convertMappingDOList(reqVO);
            cityMappingMapper.insertBatch(cityMappings, cityMappings.size());
        }
    }

    @Override
    public Long cityMappingEdit(CityMappingAditReqVO reqVO) {
        // 校验存在
        this.cityMappingValidateExists(reqVO.getId());
        // 更新
        CityMappingDO cityMapping = CityMappingConvert.INSTANCE.convert(reqVO);
        cityMappingMapper.updateById(cityMapping);
        // 返回
        return cityMapping.getId();
    }

    @Override
    public void cityMappingDel(Long id) {
        // 校验存在
        this.cityMappingValidateExists(id);
        // 删除
        cityMappingMapper.deleteById(id);
    }

    private CityMappingDO cityMappingValidateExists(Long id) {
        CityMappingDO cityMapping = cityMappingMapper.selectById(id);
        if (cityMapping == null) {
            throw exception(GlobalErrorCodeEnum.CITY_MAPPING_NOT_EXISTS);
        }
        return cityMapping;
    }

    @Override
    public CityMappingDO cityMappingDetail(Long id) {
        return cityMappingMapper.selectById(id);
    }

    @Override
    public List<CityMappingDO> cityMappingList(CityMappingQueryReqVO reqVO) {
        return cityMappingMapper.selectList(reqVO);
    }

    @Override
    public PageResult<CityMappingDO> cityMappingPage(CityMappingPageReqVO reqVO) {
        return cityMappingMapper.selectPage(reqVO);
    }

}
