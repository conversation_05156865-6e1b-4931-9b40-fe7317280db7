package com.fangcang.hotel.amadeusosbld.dto.request.hotel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/22 10:44
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "FacilityInfo")
public class FacilityInfo {

    @XmlAttribute(name = "SendMeetingRooms")
    private String sendMeetingRooms;

    @XmlAttribute(name = "SendGuestRooms")
    private String sendGuestRooms;

    @XmlAttribute(name = "SendRestaurants")
    private String sendRestaurants;

    public String getSendMeetingRooms() {
        return sendMeetingRooms;
    }

    public void setSendMeetingRooms(String sendMeetingRooms) {
        this.sendMeetingRooms = sendMeetingRooms;
    }

    public String getSendGuestRooms() {
        return sendGuestRooms;
    }

    public void setSendGuestRooms(String sendGuestRooms) {
        this.sendGuestRooms = sendGuestRooms;
    }

    public String getSendRestaurants() {
        return sendRestaurants;
    }

    public void setSendRestaurants(String sendRestaurants) {
        this.sendRestaurants = sendRestaurants;
    }
}
