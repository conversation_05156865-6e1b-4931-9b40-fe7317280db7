package com.fangcang.hotel.heytripgoosbld.util;

import com.fangcang.hotel.framework.operatelog.auto.SlsLogger;
import com.fangcang.hotel.framework.operatelog.core.sls.SlsEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/3/3 14:11
 * @Description:
 */

@Component
@Slf4j
public class LogPrintUtil {

    @Autowired
    private SlsLogger slsLogger;

    private final String resource = "hotel-shub-heytripgoosbld-server";

    public void simplePrintInfoLog(String topic, String message) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "info");
            map.put(SlsEnum.MESSAGE.getType(), message);
            map.put(SlsEnum.NAME.getType(), resource);
            slsLogger.saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintInfoLog(String topic, String message, Object arg1) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "info");
            map.put(SlsEnum.MESSAGE.getType(), String.format(message.replace("{}", "%s"), arg1));
            map.put(SlsEnum.NAME.getType(), resource);
            slsLogger.saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintInfoLog(String topic, String message, Object arg1, Object arg2) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "info");
            map.put(SlsEnum.MESSAGE.getType(), String.format(message.replace("{}", "%s"), arg1, arg2));
            map.put(SlsEnum.NAME.getType(), resource);
            slsLogger.saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintInfoLog(String topic, String message, Object arg1, Object arg2, Object arg3) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "info");
            map.put(SlsEnum.MESSAGE.getType(), String.format(message.replace("{}", "%s"), arg1, arg2, arg3));
            map.put(SlsEnum.NAME.getType(), resource);
            slsLogger.saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintInfoLog(String topic, String message, Object... args) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "info");
            map.put(SlsEnum.MESSAGE.getType(), String.format(message.replace("{}", "%s"), args));
            map.put(SlsEnum.NAME.getType(), resource);
            slsLogger.saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintErrorLog(String topic, String message) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "error");
            map.put(SlsEnum.MESSAGE.getType(), message);
            map.put(SlsEnum.NAME.getType(), resource);
            slsLogger.saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintErrorLog(String topic, String message, Object arg1) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "error");
            map.put(SlsEnum.MESSAGE.getType(), String.format(message.replace("{}", "%s"), arg1));
            map.put(SlsEnum.NAME.getType(), resource);
            slsLogger.saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintErrorLog(String topic, String message, Object arg1, Object arg2) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "error");
            map.put(SlsEnum.MESSAGE.getType(), String.format(message.replace("{}", "%s"), arg1, arg2));
            map.put(SlsEnum.NAME.getType(), resource);
            saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintErrorLog(String topic, String message, Object arg1, Object arg2, Object arg3) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "error");
            map.put(SlsEnum.MESSAGE.getType(), String.format(message.replace("{}", "%s"), arg1, arg2, arg3));
            map.put(SlsEnum.NAME.getType(), resource);
            slsLogger.saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintErrorLog(String topic, String message, Object... args) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "error");
            map.put(SlsEnum.MESSAGE.getType(), String.format(message.replace("{}", "%s"), args));
            map.put(SlsEnum.NAME.getType(), resource);
            slsLogger.saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintErrorLog(String topic, Exception error, String message) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "error");
            map.put(SlsEnum.MESSAGE.getType(), message);
            map.put(SlsEnum.NAME.getType(), resource);
            map.put("error", getStackTraceAsString(error));
            slsLogger.saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintErrorLog(String topic, Exception error, String message, Object arg1) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "error");
            map.put(SlsEnum.MESSAGE.getType(), String.format(message.replace("{}", "%s"), arg1));
            map.put(SlsEnum.NAME.getType(), resource);
            map.put("error", getStackTraceAsString(error));
            slsLogger.saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintErrorLog(String topic, Exception error, String message, Object arg1, Object arg2) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "error");
            map.put(SlsEnum.MESSAGE.getType(), String.format(message.replace("{}", "%s"), arg1, arg2));
            map.put(SlsEnum.NAME.getType(), resource);
            map.put("error", getStackTraceAsString(error));
            saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintErrorLog(String topic, Exception error, String message, Object arg1, Object arg2, Object arg3) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "error");
            map.put(SlsEnum.MESSAGE.getType(), String.format(message.replace("{}", "%s"), arg1, arg2, arg3));
            map.put(SlsEnum.NAME.getType(), resource);
            map.put("error", getStackTraceAsString(error));
            slsLogger.saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void simplePrintErrorLog(String topic, Exception error, String message, Object... args) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "error");
            map.put(SlsEnum.MESSAGE.getType(), String.format(message.replace("{}", "%s"), args));
            map.put(SlsEnum.NAME.getType(), resource);
            map.put("error", getStackTraceAsString(error));
            slsLogger.saveLog(map, topic, resource);
        } catch (Exception e) {
            slsLogger.saveErrorLog(e);
        }
    }

    public void saveErrorLog(Exception e) {
        slsLogger.saveErrorLog(e);
    }

    public void saveLog(Map<String, String> map, String topic, String source) {
        slsLogger.saveLog(map, topic, source);
    }

    private static String getStackTraceAsString(Exception e) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.toString();
    }

}