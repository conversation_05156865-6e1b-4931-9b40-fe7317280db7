package com.fangcang.hotel.amadeusosbld.dto.request.order.completereservationdetails;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/29 19:32
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "retrievalKey")
public class RetrievalKey {

    @XmlElement(name = "reservation")
    private ReservationCancel reservation;

    public ReservationCancel getReservation() {
        return reservation;
    }

    public void setReservation(ReservationCancel reservation) {
        this.reservation = reservation;
    }
}
