package com.fangcang.hotel.common.controller.admin.mapping.vo;

import lombok.*;

import com.fangcang.hotel.framework.common.domain.PageParam;

/**
 * 错误映射 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HotelMappingErrorPageReqVO extends PageParam {

    /** 酒店id */
    private String hotelId;

    /** 酒店名称 */
    private String hotelName;

    /** 房型ID */
    private String roomId;

    /** 房型名称 */
    private String roomName;

    /** 供应商酒店ID */
    private String spHotelId;

    /** 供应商房型ID */
    private String spRoomId;

    /** 类型：1-国内 2-海外 ["国内","海外"] */
    private String supplyType;

    /** 数据状态 0-未处理 1-已处理 */
    private Integer dataStatus;

    /** 数据类型 1:酒店 2:房型 3:映射 */
    private String dataType;

    /** 错误原因 */
    private String errorCause;

    /** 状态 [有效,无效] */
    private Integer status;

}
