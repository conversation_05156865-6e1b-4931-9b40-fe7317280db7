package com.fangcang.hotel.common.controller.admin.open.vo.supplyroom;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: qiu
 * @create: 2024-05-24 14:36
 */
@Data
public class TranslationRoomTypeNameDTO  {


    @NotBlank(message = "酒店id不能为空")
    private String hotelId;

    @NotBlank(message = "供应商酒店id不能为空")
    private String spHotelId;

    @NotBlank(message = "待翻译数据不能为空")
    private String key;

    /**
     * 供应商房型编码 用于组装最终房型编码
     */
    private String roomTypeCode;

    //描述信息
    private List<String> desc;

    // 是否映射 true 触发房型映射
    private Boolean isMapping;

    @NotBlank(message = "语言类型不能为空")
    private String language;

    @NotNull(message = "供应类型不能为空")
    private String supplyClass;
}