package com.fangcang.hotel.mtosbld.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/8/30 17:43
 * @Description:
 */

@Getter
@AllArgsConstructor
public enum MTOSBLDOrderStatusEnum {

    _0(0, "待确认"),
    _1(1, "已确认"),
    _10(10, "已拒单"),
    _201(201, "已取消"),
    _202(202, "取消失败"),
    _20(20, "待取消");

    private Integer key;
    private String desc;

}
