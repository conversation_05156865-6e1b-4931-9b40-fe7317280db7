package com.fangcang.hotel.hzbld.util;

import com.fangcang.hotel.framework.common.util.DateUtilX;
import com.fangcang.hotel.framework.common.util.StrUtilX;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class HZBLDUtil {
	
	 /**
     *  获取查询天数
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return
     */
    public static Set<String> getQueryDays(Date startDate, Date endDate) {
        Set<String> queryDays = new HashSet<String>();
        long days = DateUtilX.getDay(startDate, endDate);
        while (days-- > 0) {
            queryDays.add(DateUtilX.dateToString(startDate));
            startDate = DateUtilX.getDate(startDate, 1, 0);
        }
        return queryDays;
    }

    /**
     * 根据关键字获取窗型名称
     * @param keyName
     * @return
     */
    public static String getIsHasWindowName(String keyName) {
        String isHasWindowName = "";
        if (StrUtilX.isNotEmpty(keyName)) {
            String noWindow = "无窗";
            String partWindow = "部分有窗";
            if (keyName.contains(noWindow)) {
                isHasWindowName = "(" + noWindow + ")";
            } else if (keyName.contains(partWindow)) {
                isHasWindowName = "(" + partWindow + ")";
            }
        }
        return isHasWindowName;
    }

    /**
     * 房仓数据 0-无窗，1-有窗，2-部分有窗
     * 携程数据 是否有窗 ,无窗,部分有窗 ,有窗
     */
    public static Integer getWindowType(String isHasWindow) {
        if (StrUtilX.isNotEmpty(isHasWindow)) {
            String noWindow = "无窗";
            String partWindow = "部分有窗";
            String hasWindow = "有窗";
            if (isHasWindow.contains(noWindow)) {
                return 0;
            } else if (isHasWindow.contains(partWindow)) {
                return 2;
            } else if (isHasWindow.contains(hasWindow)) {
                return 1;
            }
        }
        return null;
    }

    /**
     * 获取最低价
     * @param lowerPrice 底价价MAp
     * @param key 酒店_日期
     * @param price 当前价格
     */
    public static void gethotelLowesPrice(Map<String, BigDecimal> lowerPrice, String key, BigDecimal price) {
        if (null != lowerPrice) {
            //从map中获取value
            BigDecimal value = lowerPrice.get(key);
            if (value == null) {
                //不存在则设置当前价格为最低价
                lowerPrice.put(key, price);
            } else if (price.compareTo(value) == -1) {
                //当前价格小于之前价格则进行替换
                lowerPrice.replace(key, price);
            }
        }
    }

}
