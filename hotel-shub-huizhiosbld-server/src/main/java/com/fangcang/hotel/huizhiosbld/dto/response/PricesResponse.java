package com.fangcang.hotel.huizhiosbld.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/30 10:59
 * @Description:
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PricesResponse {

    /**
     * 酒店Id
     */
    private Integer hid;

    /**
     * 房型Id
     */
    private Integer rid;

    /**
     * 价格计划id
     */
    private String rpid;

    /**
     * 房型名称
     */
    private String room_name;

    /**
     * 房型英文名称
     */
    private String room_en_name;

    /**
     * 价格计划名称
     */
    private String name;

    /**
     * 价格计划英文名称
     */
    private String en_name;

    /**
     * 最小提前预订小时数
     * <p>
     * 最小提前预订小时按入住时间的23:59:59(一般认为24点)来计算
     */
    private Integer min_adv_hours;

    /**
     * 最小入住天数（1-365）
     */
    private Integer min_days;

    /**
     * 最大入住天数（1-365）
     */
    private Integer max_days;

    /**
     * 入住日期
     */
    private String checkin;

    /**
     * 离店日期
     */
    private String checkout;

    /**
     *最大入住人数
     */
    private Integer max_occupancy;

    /**
     *早餐数
     */
    private Integer breakfast_count;

    /**
     *回收时间
     */
    private Integer cutoff_hour;

    /**
     *取消政策(最晚免费取消日期)
     */
    private String cancel_policy;

    /**
     *取消政策(最晚免费取消时间)
     */
    private String new_cancel_policy;

    /**
     *国籍限制
     */
    private String national_codes;

    /**
     *国籍限制
     */
    private String national_names;

    /**
     *
     */
    private List<NightlyrateResponse> nightlyrate;

    /**
     *
     */
    private Integer isdirect;

    /**
     *
     */
    private Integer is_reunion_room;

    /**
     *
     */
    private Integer reunion_room_min_count;

}
