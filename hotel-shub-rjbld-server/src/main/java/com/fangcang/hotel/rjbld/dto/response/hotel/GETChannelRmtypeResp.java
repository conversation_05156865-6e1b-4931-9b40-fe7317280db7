package com.fangcang.hotel.rjbld.dto.response.hotel;

import java.util.List;
import com.fangcang.hotel.rjbld.dto.response.hotel.ChannelRmtype;

import com.fangcang.hotel.rjbld.dto.response.BaseResponse;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  查询渠道合作酒店房型返回参数
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:14
 */
@Data
public class GETChannelRmtypeResp extends BaseResponse implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 渠道类型
     */
    @JSONField(name = "ChannelRmtypes")
    private List<ChannelRmtype> channelRmtypes;

}
