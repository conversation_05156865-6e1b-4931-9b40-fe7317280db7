/**
 * Copyright (c) 2006-2015 Fangcang Ltd. All Rights Reserved. 
 *  
 * This code is the confidential and proprietary information of   
 * Fangcang. You shall not disclose such Confidential Information   
 * and shall use it only in accordance with the terms of the agreements   
 * you entered into with Fangcang,http://www.fangcang.com.
 *  
 */
package com.fangcang.hotel.data.api.dto.order;

import com.fangcang.hotel.data.api.dto.TaxDetailsDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PriceInfoDetail {

    /**
     * 日期
     * yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date saleDate;

    /**
     * 售价
     *  这里的价格为 每天多间价格的总价，如果需要每间价格，需要除以房间数量 ！！！
     */
    private BigDecimal basePrice;

    /**
     * 价格明细
     */
    private TaxDetailsDTO taxDetail;


    /**
     * 是否待查(1:待查 0：非待查)
     */
    private Integer needQuery;

    /**
         * 早餐类型
     */
    private Integer breakfastType;

    /**
     * 早餐数量
     */
    private Integer breakfastNum;

    /**
     * 每日是否可定订 1:可定 0：不可订
     */
    private Integer canbook;

    /**
     * 可售房间数量
     */
    private Integer quotaNum;

    /**
     * 房态（同fangcang-core） 1：有房 2：待查 3：满房
     */
    private Integer roomStatus;

    /**
     * 是否可超(1：可超 0：不可超)
     */
    private Integer canOverDraft = 0;

    /**
     * 提前天数
     */
    private Integer aheadDays;

    /**
     * 提前时间点
     */
    private String aheadTime;

    /**
     * 连住天数
     */
    private Integer continueDays;

    /**
     * 每日返佣
     */
    private BigDecimal profitOnline;
    
}
