package com.fangcang.hotel.amadeusosbld.dto.response.order.addmultielements;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/7/1 12:01
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "optionDetail")
public class GeneralOption {

    @XmlElement(name = "optionDetail")
    private OptionDetail optionDetail;

    public OptionDetail getOptionDetail() {
        return optionDetail;
    }

    public void setOptionDetail(OptionDetail optionDetail) {
        this.optionDetail = optionDetail;
    }
}
