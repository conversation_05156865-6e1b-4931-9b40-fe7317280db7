package com.fangcang.hotel.data.api.dto.order.query;

import com.fangcang.hotel.data.api.dto.BaseRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuerySupplyOrderRequest extends BaseRequest {

    /**
     * 商家订单号
     */
    private String merchantOrderCode;

    /**
     * 供应商订单号
     */
    private String supplyOrderCode;


    /** 供应商酒店id */
    private String spHotelId;

    /** 供应商房型id */
    private String spRoomId;

    /** 入住日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkInDate;

    /** 离店日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkOutDate;

    /**确认号*/
    private String confrimNo;

    /**
     * 供货单订单总金额 不含加幅
     */
    private BigDecimal totalAmount;

}
