package com.fangcang.hotel.ratehawkosbld.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 *
 *  ${bean.classDesc}
 *
 * <AUTHOR>
 * @date 2024-05-30 17:04:51
 */
@Data
public class RoomGuestData implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 成人人数
     */
    @JSONField(name = "adults_number")
    private Integer adultsNumber;

    /**
     * 儿童人数
     */
    @JSONField(name = "children_number")
    private Integer childrenNumber;

    /**
     * 客人姓名
     */
    @JSONField(name = "guests")
    private List<GuestData> guests;

}
