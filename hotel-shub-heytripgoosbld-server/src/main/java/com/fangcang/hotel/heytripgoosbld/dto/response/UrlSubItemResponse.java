package com.fangcang.hotel.heytripgoosbld.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/2/26 13:52
 * @Description:
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UrlSubItemResponse {

    @JSONField(name = "Type")
    private String type;

    @JSONField(name = "Url")
    private String url;

}
