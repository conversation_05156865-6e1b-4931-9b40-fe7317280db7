package com.fangcang.hotel.common.dal.mysql.config;

import com.fangcang.hotel.common.controller.admin.config.vo.ConfigTempletPageReqVO;
import com.fangcang.hotel.common.controller.admin.config.vo.ConfigTempletQueryReqVO;
import com.fangcang.hotel.framework.common.domain.PageResult;
import com.fangcang.hotel.framework.mybatis.core.mapper.BaseMapperX;
import com.fangcang.hotel.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.fangcang.hotel.common.dal.db.config.ConfigTempletDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 配置模板 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ConfigTempletMapper extends BaseMapperX<ConfigTempletDO> {

    default PageResult<ConfigTempletDO> selectPage(ConfigTempletPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ConfigTempletDO>lambdaQueryX()
                .eqIfPresent(ConfigTempletDO::getSupplyClass, reqVO.getSupplyClass())
                .eqIfPresent(ConfigTempletDO::getTempletpara, reqVO.getTempletpara())
                .eqIfPresent(ConfigTempletDO::getDescription, reqVO.getDescription())
                .eqIfPresent(ConfigTempletDO::getConfigType, reqVO.getConfigType())
                .eqIfPresent(ConfigTempletDO::getExample, reqVO.getExample())
                .eqIfPresent(ConfigTempletDO::getStatus, reqVO.getStatus())
                .orderByDesc(ConfigTempletDO::getId));
    }

    default List<ConfigTempletDO> selectList(ConfigTempletQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ConfigTempletDO>lambdaQueryX()
                .eqIfPresent(ConfigTempletDO::getSupplyClass, reqVO.getSupplyClass())
                .eqIfPresent(ConfigTempletDO::getTempletpara, reqVO.getTempletpara())
                .eqIfPresent(ConfigTempletDO::getDescription, reqVO.getDescription())
                .eqIfPresent(ConfigTempletDO::getConfigType, reqVO.getConfigType())
                .eqIfPresent(ConfigTempletDO::getExample, reqVO.getExample())
                .eqIfPresent(ConfigTempletDO::getStatus, reqVO.getStatus())
                .orderByDesc(ConfigTempletDO::getId));
    }

}
