package com.fangcang.hotel.rjbld.dto.response.hotel;


import com.alibaba.fastjson.annotation.JSONField;
import com.fangcang.hotel.rjbld.enums.HotelTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 *
 *  酒店信息
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:14
 */
@Data
public class HotelInfo implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 酒店房型数据
     */
    private GetHotelRmMCResponse hotelRmMCResponse;

    /**
     * 酒店设施详情
     */
    private HotelFacilitiesListDataResp hotelFacilitiesListDataResp;

    /**
     * 酒店名称
     */
    @JSONField(name = "s_Hotel")
    private String hotel;

    /**
     * 酒店编号
     */
    @JSONField(name = "s_HotelCd")
    private String hotelCd;

    /**
     * 酒店名称
     */
    @JSONField(name = "s_HotelNm")
    private String hotelNm;

    /**
     * 酒店英文名称
     */
    @JSONField(name = "s_HotelNm_En")
    private String hotelNmEn;

    /**
     * 酒店品牌代码
     */
    @JSONField(name = "s_HotelClass")
    private String hotelClass;

    /**
     * 酒店类型
     */
    @JSONField(name = "s_HotelType")
    private String hotelType;

    /**
     * 合同编号
     */
    @JSONField(name = "s_ContractNo")
    private String contractNo;

    /**
     * 酒店地址
     */
    @JSONField(name = "s_Address")
    private String address;

    /**
     * 酒店地址
     */
    @JSONField(name = "s_sAddress")
    private String sAddress;

    /**
     * 酒店地标
     */
    @JSONField(name = "s_remarkAddress")
    private String remarkAddress;

    /**
     * 酒店电话
     */
    @JSONField(name = "s_Tel")
    private String tel;

    /**
     * 酒店传真
     */
    @JSONField(name = "s_Fax")
    private String fax;

    /**
     * 邮编
     */
    @JSONField(name = "s_Zip")
    private String zip;

    /**
     * 暂无用？（联系人）
     */
    @JSONField(name = "s_Contact")
    private String contact;

    /**
     * 酒店邮箱(无数据)
     */
    @JSONField(name = "s_Email")
    private String email;

    /**
     * 酒店描述
     */
    @JSONField(name = "s_Notice")
    private String notice;

    /**
     * 是否有效
     */
    @JSONField(name = "s_RJPMS")
    private Boolean rJPMS;

    /**
     * 暂无用？
     */
    @JSONField(name = "s_ResRoom")
    private String resRoom;

    /**
     * 城市编码
     */
    @JSONField(name = "s_Region")
    private String region;

    /**
     * 暂无用？（行政区）
     */
    @JSONField(name = "s_RegionDes")
    private String regionDes;

    /**
     * 暂无用？
     */
    @JSONField(name = "s_ResvClass")
    private String resvClass;

    /**
     * 暂无用？（无）
     */
    @JSONField(name = "s_BAuditD")
    private String bAuditD;

    /**
     * 暂无用？（无）
     */
    @JSONField(name = "s_TAuditD")
    private String tAuditD;

    /**
     * 暂无用？
     */
    @JSONField(name = "s_Savetel")
    private String savetel;

    /**
     * 暂无用？
     */
    @JSONField(name = "s_Savenet")
    private String savenet;

    /**
     * 暂无用？（无）
     */
    @JSONField(name = "s_CostPoInteger")
    private String costPoInteger;

    /**
     * 暂无用？
     */
    @JSONField(name = "s_AssessTp")
    private String assessTp;

    /**
     * 暂无用？
     */
    @JSONField(name = "s_HideWeekRate")
    private String hideWeekRate;

    /**
     * 城市代码
     */
    @JSONField(name = "s_CityCode")
    private String cityCode;

    /**
     * 暂无用？（无）
     */
    @JSONField(name = "s_PayNet")
    private String payNet;

    /**
     * 暂无用？
     */
    @JSONField(name = "s_Sort")
    private String sort;

    /**
     * 暂无用？（无）
     */
    @JSONField(name = "s_SellerEmail")
    private String sellerEmail;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_ArrdTime")
    private String arrdTime;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_UpperLimit_Discount")
    private String upperLimitDiscount;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_LowerLimit_Discount")
    private String lowerLimitDiscount;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_HotelLevel")
    private String hotelLevel;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_Detail")
    private String detail;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_Toairport")
    private String toairport;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_ToStation")
    private String toStation;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_ToCenter")
    private String toCenter;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_Sight")
    private String sight;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_Establishment")
    private String establishment;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_Dish")
    private String dish;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_BreakFast")
    private String breakFast;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_Img")
    private String img;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_Map")
    private String map;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_Note")
    private String note;

    /**
     * 开业时间
     */
    @JSONField(name = "s_HotelOpen")
    private String hotelOpen;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_Fitment")
    private String fitment;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_Card")
    private String card;

    /**
     * 暂无用
     */
    @JSONField(name = "s_PaymentTp")
    private String paymentTp;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_LandMarkCd")
    private String landMarkCd;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_LandMarkNm")
    private String landMarkNm;

    /**
     * 经度（高德）
     */
    @JSONField(name = "lon")
    private String lon;

    /**
     * 纬度（高德）
     */
    @JSONField(name = "lat")
    private String lat;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "AverageNum")
    private String averageNum;

    /**
     * 暂无用？
     */
    @JSONField(name = "IncrementNum")
    private String incrementNum;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "CountNum")
    private String countNum;

    /**
     * 暂无用
     */
    @JSONField(name = "Decoration")
    private String decoration;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "PoInteger")
    private String poInteger;

    /**
     * 暂无用（无）
     */
    @JSONField(name = "s_HotelGroupCode")
    private String hotelGroupCode;

    /**
     * 暂无用？
     */
    @JSONField(name = "s_HotelStar")
    private String hotelStar;

}
