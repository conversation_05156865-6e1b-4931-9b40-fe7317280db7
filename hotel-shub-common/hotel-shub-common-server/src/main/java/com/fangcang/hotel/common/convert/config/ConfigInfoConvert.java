package com.fangcang.hotel.common.convert.config;

import com.fangcang.hotel.common.api.common.config.dto.ConfigInfoQueryReqDTO;
import com.fangcang.hotel.common.api.common.config.dto.ConfigInfoRespDTO;
import com.fangcang.hotel.common.controller.admin.config.vo.ConfigInfoAditReqVO;
import com.fangcang.hotel.common.controller.admin.config.vo.ConfigInfoQueryReqVO;
import com.fangcang.hotel.common.controller.admin.config.vo.ConfigInfoRespVO;
import com.fangcang.hotel.common.dal.db.config.ConfigInfoDO;
import com.fangcang.hotel.framework.common.domain.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 供应商信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ConfigInfoConvert {

    ConfigInfoConvert INSTANCE = Mappers.getMapper(ConfigInfoConvert.class);

    ConfigInfoDO convert(ConfigInfoAditReqVO bean);

    ConfigInfoRespVO convert(ConfigInfoDO bean);

    ConfigInfoQueryReqVO convert(ConfigInfoQueryReqDTO bean);

    List<ConfigInfoRespVO> convertList(List<ConfigInfoDO> list);

    List<ConfigInfoRespDTO> convertApiList(List<ConfigInfoDO> list);

    PageResult<ConfigInfoRespVO> convertPage(PageResult<ConfigInfoDO> page);

}
