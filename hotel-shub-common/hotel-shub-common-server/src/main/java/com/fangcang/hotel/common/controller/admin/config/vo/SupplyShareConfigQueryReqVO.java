package com.fangcang.hotel.common.controller.admin.config.vo;

import lombok.*;

import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.fangcang.hotel.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 供应商共享配置 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class SupplyShareConfigQueryReqVO {

    /** 提供方商家来源 */
    private String providerMerhcantSource;

    /** 提供方商家编码 */
    private String providerMerhcantCode;

    /** 提供方商家名称 */
    private String providerMerhcantName;

    /** 提供方供应商编码 */
    private String providerSupplyCode;

    /** 提供方供应商名称 */
    private String providerSupplyName;

    /** 共享方商家来源 */
    private String sharingMerchantSource;

    /** 共享方商家编码 */
    private String sharingMerchantCode;

    /** 共享方商家名称 */
    private String sharingMerchantName;

    /** 共享方供应商编码 */
    private String sharingSupplyCode;

    /** 共享方供应商名称 */
    private String sharingSupplyName;

    /** 共享类型 [0起价共享,1 账号共享] */
    private Integer sharingType;

    /** 状态 [有效,无效] */
    private Integer status;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

}
