package com.fangcang.hotel.didaosbld.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/12/10 14:53
 * @Description:
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ErrorResponse {

    @JSONField(name = "Message")
    private String message;


    @JSONField(name = "Code")
    private String code;

}
