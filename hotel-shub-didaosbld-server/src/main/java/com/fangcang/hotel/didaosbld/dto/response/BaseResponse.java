package com.fangcang.hotel.didaosbld.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/10/29 19:44
 * @Description:
 */
@Data
//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
public class BaseResponse implements Serializable {


    @JSONField(name = "Error")
    private ErrorResponse error;

}
