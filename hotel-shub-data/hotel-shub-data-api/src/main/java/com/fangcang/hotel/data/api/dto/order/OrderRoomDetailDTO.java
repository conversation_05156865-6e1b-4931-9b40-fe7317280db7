package com.fangcang.hotel.data.api.dto.order;

import com.fangcang.hotel.data.api.dto.TaxDetailsDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 房型明细
 * @author: qiu
 * @create: 2023-11-03 09:20
 */
@Data
public class OrderRoomDetailDTO {

    /**
     * 房间序号，多间房多个入住人使用
     */
    private Integer roomIndex;

    /**
     * 成人数
     */
    private Integer adultNum;

    /**
     * 儿童年龄 多个儿童年龄用逗号分隔 例如：1,2,3
     */
    private String childAges;

    /**
     * 每间 每次入住的价格明细
     */
    private TaxDetailsDTO taxDetails;

    /**
     * 到店另付币种 CurrencyEnum
     */
    private Integer payInStoreCurrency;

    /**
     * 到店另付费用 海外存在 预付后 需要到店另付款情况
     */
    private BigDecimal payInStorePrice;

    /**
     * 供应商到店另付币种
     */
    private Integer supplyPayInStoreCurrency;

    /**
     * 供应商到店另付费用
     */
    private BigDecimal supplyPayInStorePrice;

    /**
     * 每一天的试预定结果详情
     */
    private List<PriceInfoDetail> priceInfoDetails;
}