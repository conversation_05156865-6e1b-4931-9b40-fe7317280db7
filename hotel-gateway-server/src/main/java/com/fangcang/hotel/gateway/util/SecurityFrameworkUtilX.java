package com.fangcang.hotel.gateway.util;

import cn.hutool.core.map.MapUtil;
import com.fangcang.hotel.framework.common.constant.HttpConstant;
import com.fangcang.hotel.framework.common.domain.LoginUser;
import com.fangcang.hotel.framework.common.util.JsonUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;


/**
 * 安全服务工具类
 *
 * security 的 SecurityFrameworkUtilX 类
 *
 */
@Slf4j
public class SecurityFrameworkUtilX {

    private static final String LOGIN_USER_ID_ATTR = "login-user-id";
    private static final String LOGIN_USER_TYPE_ATTR = "login-user-type";

    private SecurityFrameworkUtilX() {}

    /**
     * 从请求中，获得认证 Token
     *
     * @param exchange 请求
     * @return 认证 Token
     */
    public static String obtainAuthorization(ServerWebExchange exchange) {
        return exchange.getRequest().getHeaders().getFirst(HttpConstant.TOKEN);
    }

    /**
     * 设置登录用户
     *
     * @param exchange 请求
     * @param user 用户
     */
    public static void setLoginUser(ServerWebExchange exchange, LoginUser user) {
        exchange.getAttributes().put(LOGIN_USER_ID_ATTR, user.getUserId());
    }

    /**
     * 移除请求头的用户
     *
     * @param exchange 请求
     * @return 请求
     */
    public static ServerWebExchange removeLoginUser(ServerWebExchange exchange) {
        // 如果不包含，直接返回
        if (!exchange.getRequest().getHeaders().containsKey(HttpConstant.LOGIN_USER_HEADER)) {
            return exchange;
        }
        // 如果包含，则移除。参考 RemoveRequestHeaderGatewayFilterFactory 实现
        ServerHttpRequest request = exchange.getRequest().mutate()
                .headers(httpHeaders -> httpHeaders.remove(HttpConstant.LOGIN_USER_HEADER)).build();
        return exchange.mutate().request(request).build();
    }

    /**
     * 获得登录用户的编号
     *
     * @param exchange 请求
     * @return 用户编号
     */
    public static Long getLoginUserId(ServerWebExchange exchange) {
        return MapUtil.getLong(exchange.getAttributes(), LOGIN_USER_ID_ATTR);
    }

    /**
     * 获得登录用户的类型
     *
     * @param exchange 请求
     * @return 用户类型
     */
    public static Integer getLoginUserType(ServerWebExchange exchange) {
        return MapUtil.getInt(exchange.getAttributes(), LOGIN_USER_TYPE_ATTR);
    }

    /**
     * 将 user 并设置到 login-user 的请求头，使用 json 存储值
     *
     * @param builder 请求
     * @param loginUser 用户
     */
    public static void setLoginUserHeader(ServerHttpRequest.Builder builder, LoginUser loginUser) {
        String user = JsonUtilX.toJsonString(loginUser);

        // TODO 网关乱码问题
        // 解决方法：把请求头的值用url编码，在过滤器那里进行解码
//        try {
//            if (user != null){
//                user = URLEncoder.encode(user, "utf-8");
//            }
//        } catch (UnsupportedEncodingException e) {
//            throw new RuntimeException(e);
//        }
        builder.header(HttpConstant.LOGIN_USER_HEADER, user);
    }

}
