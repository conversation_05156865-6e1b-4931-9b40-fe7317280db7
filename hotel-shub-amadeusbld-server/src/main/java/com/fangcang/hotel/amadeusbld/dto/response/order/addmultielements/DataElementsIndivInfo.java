package com.fangcang.hotel.amadeusbld.dto.response.order.addmultielements;

import com.fangcang.hotel.amadeusbld.dto.request.order.addmultielements.TicketElement;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/7/9 15:40
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "dataElementsIndiv")
public class DataElementsIndivInfo {

    @XmlElement(name = "elementManagementData")
    private ElementManagementDataInfo elementManagementData;

    @XmlElement(name = "ticketElement")
    private TicketElement ticketElement;

    @XmlElement(name = "otherDataFreetext")
    private OtherDataFreetext otherDataFreetext;

    @XmlElement(name = "elementErrorInformation")
    private ElementErrorInformation elementErrorInformation;

    @XmlElement(name = "miscellaneousRemarks")
    private MiscellaneousRemarks miscellaneousRemarks;

    public ElementManagementDataInfo getElementManagementData() {
        return elementManagementData;
    }

    public void setElementManagementData(ElementManagementDataInfo elementManagementData) {
        this.elementManagementData = elementManagementData;
    }

    public TicketElement getTicketElement() {
        return ticketElement;
    }

    public void setTicketElement(TicketElement ticketElement) {
        this.ticketElement = ticketElement;
    }

    public OtherDataFreetext getOtherDataFreetext() {
        return otherDataFreetext;
    }

    public void setOtherDataFreetext(OtherDataFreetext otherDataFreetext) {
        this.otherDataFreetext = otherDataFreetext;
    }

    public ElementErrorInformation getElementErrorInformation() {
        return elementErrorInformation;
    }

    public void setElementErrorInformation(ElementErrorInformation elementErrorInformation) {
        this.elementErrorInformation = elementErrorInformation;
    }

    public MiscellaneousRemarks getMiscellaneousRemarks() {
        return miscellaneousRemarks;
    }

    public void setMiscellaneousRemarks(MiscellaneousRemarks miscellaneousRemarks) {
        this.miscellaneousRemarks = miscellaneousRemarks;
    }
}
