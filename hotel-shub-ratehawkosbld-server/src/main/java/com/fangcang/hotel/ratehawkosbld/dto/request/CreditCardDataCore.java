package com.fangcang.hotel.ratehawkosbld.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  信用卡数据信息
 *
 * <AUTHOR>
 * @date 2024-05-30 17:04:51
 */
@Data
public class CreditCardDataCore implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 有效期年
     */
    @JSONField(name = "year")
    private String year;

    /**
     * 信用卡号
     */
    @JSONField(name = "card_number")
    private String cardNumber;

    /**
     * 持卡人的姓名
     */
    @JSONField(name = "card_holder")
    private String cardHolder;

    /**
     * 有效期至一个月
     */
    @JSONField(name = "month")
    private String month;

}
