package com.fangcang.hotel.bookingosbld.dto.request.price;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2021/6/18 11:56
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "RatePlanCandidate")
public class RatePlanCandidate {

    @XmlAttribute(name = "RatePlanCode")
    private String ratePlanCode;

    public String getRatePlanCode() {
        return ratePlanCode;
    }

    public void setRatePlanCode(String ratePlanCode) {
        this.ratePlanCode = ratePlanCode;
    }
}
