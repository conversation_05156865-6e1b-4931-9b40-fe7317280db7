package com.fangcang.hotel.tmchubosbld.xxl;

import com.fangcang.hotel.tmchubosbld.service.impl.DHUBOSBLDOrderServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 起价获取dhubos不落地酒店id存进缓存任务
 */
@Slf4j
@Component
public class SyncDHUBOSBLDOrderStatusRunner {

    @Autowired
    private DHUBOSBLDOrderServiceImpl dhubosbldOrderService;

    @XxlJob("syncDHUBOSBLDOrderStatusTask")
    public void syncLowestPriceDHUBOSBLDHotelIdToRedisRunner() {
        try {
            XxlJobHelper.log("执行同步dhubos不落地订单状态任务开始");
            dhubosbldOrderService.syncDHUBOSBLDOrderStatusTask();
            XxlJobHelper.log("执行同步dhubos不落地订单状态结束");
        } catch (Exception e) {
            log.error("执行起价获取dhubos不落地酒店id存进缓存任务异常", e);
            XxlJobHelper.log("执行同步dhubos不落地订单状态异常", e);
        }
    }
}
