package com.fangcang.hotel.common.controller.admin.supply.vo;

import lombok.Data;


/**
 * 供应商付款配置 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class SupplyPayConfigQueryReqVO {

    /** 商家编码 */
    private String merchantCode;

    /** 商家名称 */
    private String merchantName;

    /** 商家来源 */
    private String merchantSource;

    /** 供应类型 */
    private String supplyClass;

    /** 供应商编码 */
    private String supplyCode;

    /** 供应商名称 */
    private String supplyName;

    /** 支付类型 [1现付,2 预付] */
    private Integer payMethod;

    /** 付款分类 [0:billBack,1:动态VCC,2.固定VCC] */
    private Integer modeOfPay;

    /** 付款维度 [0:供应类型,1.供应商,2.供应商+国家,3.供应商+酒店，4.客户] */
    private Integer payDimension;

    /** 属性名 */
    private String attributeName;

    /** 属性值 */
    private String attributeValue;

    /**
     * 付款主体编码 用于区分不同付款主体
     */
    private String paymentSubjectCode;

    /**
     * 付款主体名称
     */
    private String paymentSubjectName;

    /**
     * 付款主体币种
     */
    private String paymentSubjectCurrency;

    /** 支付VCCId */
    private Long payVccId;

    /** 备注 备注信息不会进行拼接，配置什么传入什么给amadeus。 */
    private String remark;

    /** 状态 [有效,无效] */
    private Integer status;

}
