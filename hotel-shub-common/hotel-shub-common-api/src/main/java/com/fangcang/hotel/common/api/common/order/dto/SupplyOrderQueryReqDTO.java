package com.fangcang.hotel.common.api.common.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @description: 供应商订单查询请求参数
 * @author: qiu
 * @create: 2023-09-25 10:59
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplyOrderQueryReqDTO {

    private Long id;

    /** 商家订单号 */
    private String merchantOrderCode;

    /** 供应商订单号 */
    private String supplyOrderCode;

    /** 供应商确认号 */
    private String supplyConfirmNo;

    /** 供应商订单状态 */
    private String supplyOrderStatus;

    /** 订单状态 SHub 订单确认结果 */
    private Integer orderStatus;

    /** 供应类型 */
    private String supplyClass;

    /** 供应商编码 */
    private String supplyCode;

    /** 入住时间 */
    private LocalDate checkInDate;

    /** 离店时间 */
    private LocalDate checkOutDate;

    /** 订单总金额 */
    private Long totalOrderAmount;

    /** 币种 */
    private Integer currency;

    /** 房间数量 */
    private Integer numberOfRooms;

    /** 价格计划ID */
    private String supply;

    /** 入住人 */
    private String guests;

    /** 供应商酒店ID */
    private String spHotelId;

    /** 商家来源 SAAS 还是 飞天 */
    private String merchantSource;

    /** 商家编码 */
    private String merchantCode;

    /** 酒店id */
    private String hotelId;

    /** 供应商房型ID */
    private String spRoomId;

    /** 房型ID */
    private String roomId;

    /** 创建时间 大于等于*/
    private LocalDateTime createdDt;
}