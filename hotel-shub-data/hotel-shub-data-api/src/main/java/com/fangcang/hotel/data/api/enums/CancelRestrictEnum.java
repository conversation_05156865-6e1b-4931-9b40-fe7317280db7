package com.fangcang.hotel.data.api.enums;

/**
 * Created by <PERSON> on 2016/11/28.
 */

/**
 * 取消条款枚举类
 */
public enum CancelRestrictEnum {

    NO_CANCEL(1, "一经预订不可更改或取消"), PARTIAL_CANCEL(2, "预订后规定时间前可取消或收取部分费用"), FULL_CANCEL(3, "预订后规定时间前不可取消或收取全部费用");

    public int key;
    public String value;

    private CancelRestrictEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(int key) {
        for(CancelRestrictEnum cancelRestrictEnum : CancelRestrictEnum.values()) {
            if(cancelRestrictEnum.key == key) {
                return cancelRestrictEnum.value;
            }
        }
        return null;
    }
}
