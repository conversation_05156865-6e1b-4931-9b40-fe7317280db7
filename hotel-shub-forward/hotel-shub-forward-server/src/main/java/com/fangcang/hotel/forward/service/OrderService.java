package com.fangcang.hotel.forward.service;

import com.fangcang.hotel.data.api.dto.order.OrderCheckDetailsResponse;
import com.fangcang.hotel.data.api.dto.order.QueryCheckDetailRequest;
import com.fangcang.hotel.data.api.dto.order.cancel.CancelSupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.cancel.CancelSupplyOrderResponse;
import com.fangcang.hotel.data.api.dto.order.create.CreateSupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.create.CreateSupplyOrderResponse;
import com.fangcang.hotel.data.api.dto.order.prebooking.PreBookingSupplyRequest;
import com.fangcang.hotel.data.api.dto.order.prebooking.PreBookingSupplyResponse;
import com.fangcang.hotel.data.api.dto.order.query.QuerySupplyOrderRequest;
import com.fangcang.hotel.data.api.dto.order.query.QuerySupplyOrderResponse;
import com.fangcang.hotel.framework.common.domain.ResultX;

/**
 * @description: 订单服务
 * @author: qiu
 * @create: 2023-08-12 14:19
 */
public interface OrderService {

    /**
     * 获取供应商订单入住明细
     * @param queryCheckDetailRequest 订单入住明细请求
     * @return 入住明细
     */
    ResultX<OrderCheckDetailsResponse> querySupplyOrderCheckDetail(QueryCheckDetailRequest queryCheckDetailRequest);

    /**
     * 试预定
     * @param preBookingSupplyRequest 试预定请求
     * @return 试预定结果
     */
    ResultX<PreBookingSupplyResponse> proBooking(PreBookingSupplyRequest preBookingSupplyRequest);

    /**
     * 下单
     * @param createSupplyOrderRequest 下单请求
     * @return 下单结果
     */
    ResultX<CreateSupplyOrderResponse> createSupplyOrder(CreateSupplyOrderRequest createSupplyOrderRequest);

    /**
     * 查询订单
     * @param querySupplyOrderRequest 查询订单请求
     * @return 查询订单结果 订单明细
     */
    ResultX<QuerySupplyOrderResponse> querySupplyOrder(QuerySupplyOrderRequest querySupplyOrderRequest);

    /**
     * 取消订单
     * @param cancelSupplyOrderRequest 取消订单请求
     * @return 订单取消结果 取消成功|取消失败
     */
    ResultX<CancelSupplyOrderResponse> cancelSupplyOrder(CancelSupplyOrderRequest cancelSupplyOrderRequest);

}