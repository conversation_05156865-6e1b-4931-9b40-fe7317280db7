package com.fangcang.hotel.ratehawkosbld.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * <AUTHOR>
 * @date ： 2024/6/25
 */
@Slf4j
@Configuration
@EnableAsync
public class SyncLowestPriceThreadConfig {

    //===============================================同步起价线程池配置==================================================
    @Value("${sync-lowestPrice-thread.corePoolSize}")
    private Integer corePoolSize;

    @Value("${sync-lowestPrice-thread.maxPoolSize}")
    private Integer maxPoolSize;

    @Value("${sync-lowestPrice-thread.queueCapacity}")
    private Integer queueCapacity;

    @Value("${sync-lowestPrice-thread.waitForTasksToCompleteOnShutdown}")
    private boolean waitForTasksToCompleteOnShutdown;

    @Value("${sync-lowestPrice-thread.awaitTerminationSeconds}")
    private Integer waitTerminationSeconds;

    @Value("${sync-lowestPrice-thread.keepAliveSeconds}")
    private Integer keepAliveSeconds;

    @Value("${sync-lowestPrice-thread.threadNamePrefix}")
    private String threadNamePrefix;

    @Bean("syncLowestPricePoolTaskExecutor")
    public ThreadPoolTaskExecutor productPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数：线程池创建的时候初始化的线程数
        executor.setCorePoolSize(corePoolSize);
        // 最大线程数：线程池最大的线程数，只有缓冲队列满了之后才会申请超过核心线程数的线程
        executor.setMaxPoolSize(maxPoolSize);
        // 缓冲队列：用来缓冲执行任务的队列
        executor.setQueueCapacity(queueCapacity);
        // 线程池关闭：等待所有任务都完成再关闭
        executor.setWaitForTasksToCompleteOnShutdown(waitForTasksToCompleteOnShutdown);
        // 等待时间：等待5秒后强制停止
        executor.setAwaitTerminationSeconds(waitTerminationSeconds);
        // 允许空闲时间：超过核心线程之外的线程到达60秒后会被销毁
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 线程名称前缀
        executor.setThreadNamePrefix(threadNamePrefix);
        // 初始化线程
        executor.initialize();

        return executor;
    }
}
