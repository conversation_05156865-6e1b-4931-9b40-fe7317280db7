package com.fangcang.hotel.amadeusbld.dto.request.order.sell;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/29 16:59
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "roomList")
public class RoomList {

    @XmlElement(name = "markerRoomstayQuery")
    private MarkerRoomstayQuery markerRoomstayQuery;

    @XmlElement(name = "roomRateDetails")
    private RoomRateDetails roomRateDetails;

    @XmlElement(name = "customerInfo")
    private CustomerInfo customerInfo;

    @XmlElement(name = "guaranteeOrDeposit")
    private GuaranteeOrDeposit guaranteeOrDeposit;

    @XmlElement(name = "supplementaryInfo")
    private SupplementaryInfo supplementaryInfo;

    @XmlElement(name = "guestList")
    private List<GuestList> guestLists;

    private List<CustomerReferences> customerReferencesList;

    public MarkerRoomstayQuery getMarkerRoomstayQuery() {
        return markerRoomstayQuery;
    }

    public void setMarkerRoomstayQuery(MarkerRoomstayQuery markerRoomstayQuery) {
        this.markerRoomstayQuery = markerRoomstayQuery;
    }

    public RoomRateDetails getRoomRateDetails() {
        return roomRateDetails;
    }

    public void setRoomRateDetails(RoomRateDetails roomRateDetails) {
        this.roomRateDetails = roomRateDetails;
    }

    public GuaranteeOrDeposit getGuaranteeOrDeposit() {
        return guaranteeOrDeposit;
    }

    public void setGuaranteeOrDeposit(GuaranteeOrDeposit guaranteeOrDeposit) {
        this.guaranteeOrDeposit = guaranteeOrDeposit;
    }

    public SupplementaryInfo getSupplementaryInfo() {
        return supplementaryInfo;
    }

    public void setSupplementaryInfo(SupplementaryInfo supplementaryInfo) {
        this.supplementaryInfo = supplementaryInfo;
    }

    public List<GuestList> getGuestLists() {
        return guestLists;
    }

    public void setGuestLists(List<GuestList> guestLists) {
        this.guestLists = guestLists;
    }

    public List<CustomerReferences> getCustomerReferencesList() {
        return customerReferencesList;
    }

    public void setCustomerReferencesList(List<CustomerReferences> customerReferencesList) {
        this.customerReferencesList = customerReferencesList;
    }
    public CustomerInfo getCustomerInfo() {
        return customerInfo;
    }
    public void setCustomerInfo(CustomerInfo customerInfo) {
        this.customerInfo = customerInfo;
    }
}
