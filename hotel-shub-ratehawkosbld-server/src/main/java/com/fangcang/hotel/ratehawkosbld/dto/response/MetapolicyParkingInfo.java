package com.fangcang.hotel.ratehawkosbld.dto.response;

import java.math.BigDecimal;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 *
 *  停车信息
 *
 * <AUTHOR>
 * @date 2024-05-30 17:04:51
 */
@Data
public class MetapolicyParkingInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 币种
     */
    @JSONField(name = "currency")
    private String currency;

    /**
     * 包含 (unspecified, included, not_included)
     */
    @JSONField(name = "inclusion")
    private String inclusion;

    /**
     * 价格
     */
    @JSONField(name = "price")
    private BigDecimal price;

    /**
     * 价格单位  (unspecified, per_guest_per_night, per_guest_per_stay, per_room_per_night, per_room_per_stay, per_hour, per_week)
     */
    @JSONField(name = "price_unit")
    private String priceUnit;

    /**
     * 停车区域类型
     */
    @JSONField(name = "territory_type")
    private String territoryType;

}
