package com.fangcang.hotel.ratehawkosbld.manger;

import com.fangcang.hotel.ratehawkosbld.config.RateHawkOSBLDExtendConfig;
import com.fangcang.hotel.ratehawkosbld.dto.request.*;
import com.fangcang.hotel.ratehawkosbld.dto.response.*;

/**
 * 供应商接口实现类
 *
 * <AUTHOR>
 * @date 2024-01-22 09:38:15
 */
public interface RateHawkOSBLDManager {


    /**
     * 全量查询酒店静态信息
     *
     * @return
     */
    HotelDataDumpResponse queryAllHotelBasicInfo(HotelDataDumpRequest request, RateHawkOSBLDExtendConfig extendConfig);

    /**
     * 增量查询酒店静态信息
     *
     * @return
     */
    HotelDataDumpResponse queryIncrementHotelBasicInfo(HotelDataDumpRequest request, RateHawkOSBLDExtendConfig extendConfig);

    /**
     * 查询酒店详情
     *
     * @return
     */
    HotelDataSearchResponse queryHotelDetail(HotelDataSearchRequest request, RateHawkOSBLDExtendConfig extendConfig);


    /**
     * 按多酒店查询酒店产品详情
     *
     * @return
     */
    HotelsSearchEngineResultsPageResponse queryRoomPriceList(HotelsSearchEngineResultsPageRequest request, RateHawkOSBLDExtendConfig extendConfig);

    /**
     * 查询酒店产品详情
     *
     * @return
     */
    HotelPageResponse queryRoomPrice(HotelPageRequest request, RateHawkOSBLDExtendConfig extendConfig);


    /**
     * 试预订
     *
     * @return
     */
    PreBookResponse preBookingOrder(PreBookRequest request, RateHawkOSBLDExtendConfig extendConfig);

    /**
     * 开始预定
     *
     * @return
     */
    BaseResponse<OrderBookingResponse> orderBooking(OrderBookingRequest request, RateHawkOSBLDExtendConfig extendConfig);

    /**
     * 发送信用卡信息
     *
     * @return
     */
    BaseResponse<String> sendCreditCardData(CreditCardDataRequest request, RateHawkOSBLDExtendConfig extendConfig);

    /**
     * 预订完成
     *
     * @return
     */
    BaseResponse orderBookingFinish(OrderBookingFinishRequest request, RateHawkOSBLDExtendConfig extendConfig);

    /**
     * 查询订单状态
     *
     * @return
     */
    BaseResponse<OrderBookingFinishStatusResponse> queryOrderStatus(OrderBookingFinishStatusRequest request, RateHawkOSBLDExtendConfig extendConfig);


    /**
     * 查询订单详情
     *
     * @return
     */
    OrderInformationResponse queryOrder(OrderInformationRequest request, RateHawkOSBLDExtendConfig extendConfig);


    /**
     * 取消订单
     *
     * @return
     */
    OrderCancellationResponse cancelOrder(OrderCancellationRequest request, RateHawkOSBLDExtendConfig extendConfig);

    /**
     * 下载城市列表信息转储文件
     *
     * @return
     */
    HotelDataDumpResponse downloadHotelRegionDump(RateHawkOSBLDExtendConfig extendConfig);

}
