package com.fangcang.hotel.amadeusosbld.dto.response.hotel;


import com.fangcang.hotel.amadeusosbld.dto.response.common.ResponseError;
import com.fangcang.hotel.amadeusosbld.dto.response.common.ResponseWarning;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/22 11:07
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "OTA_HotelDescriptiveInfoRS")
public class OTAHotelDescriptiveInfoRS {


    @XmlElementWrapper(name = "Errors")
    @XmlElement(name = "Error")
    private List<ResponseError> errorList;

    @XmlElementWrapper(name = "Warnings")
    @XmlElement(name = "Warning")
    private List<ResponseWarning> warningList;

    @XmlElementWrapper(name = "HotelDescriptiveContents")
    @XmlElement(name = "HotelDescriptiveContent")
    private List<HotelDescriptiveContent> hotelDescriptiveContentList;

    public List<ResponseError> getErrorList() {
        return errorList;
    }

    public void setErrorList(List<ResponseError> errorList) {
        this.errorList = errorList;
    }

    public List<ResponseWarning> getWarningList() {
        return warningList;
    }

    public void setWarningList(List<ResponseWarning> warningList) {
        this.warningList = warningList;
    }

    public List<HotelDescriptiveContent> getHotelDescriptiveContentList() {
        return hotelDescriptiveContentList;
    }

    public void setHotelDescriptiveContentList(List<HotelDescriptiveContent> hotelDescriptiveContentList) {
        this.hotelDescriptiveContentList = hotelDescriptiveContentList;
    }
}
