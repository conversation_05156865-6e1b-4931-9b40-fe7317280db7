package com.fangcang.hotel.ratehawkosbld.dto.request;

import java.util.Date;

import com.fangcang.hotel.ratehawkosbld.dto.response.*;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 订单预订流程完成
 *
 * <AUTHOR>
 * @date 2024-05-30 17:04:51
 */
@Data
public class OrderBookingFinishRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 预计抵达酒店的时间
     */
    @JSONField(name = "arrival_datetime")
    private Date arrivalDatetime;

    /**
     * yuyan
     */
    @JSONField(name = "language")
    private String language;

    /**
     * 合作伙伴的信息
     */
    @JSONField(name = "partner")
    private HotelOrderBookingFinishPartner partner;

    /**
     * 订单付款信息
     */
    @JSONField(name = "payment_type")
    private HotelOrderBookingFinishPaymentType paymentType;

    /**
     * 订单追加销售信息
     */
    @JSONField(name = "upsell_data")
    private List<HotelOrderBookingFinishUpsellData> upsellData;

    /**
     * 域名地址
     */
    @JSONField(name = "return_path")
    private String returnPath;

    /**
     * 客房的客人数据
     */
    @JSONField(name = "rooms")
    private List<HotelOrderBookingFinishRoom> rooms;

    /**
     * 合作伙伴的经理附加信息
     */
    @JSONField(name = "user")
    private HotelOrderBookingFinishUser user;

    /**
     * 发起预订的用户的联系方式
     */
    @JSONField(name = "supplier_data")
    private HotelOrderBookingFinishSupplierData supplierData;

}
