package com.fangcang.hotel.mtosbld.manger.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fangcang.hotel.core.enums.GlobalErrorCodeEnum;
import com.fangcang.hotel.core.util.StringUtilExtend;
import com.fangcang.hotel.data.api.dto.HttpResponse;
import com.fangcang.hotel.data.api.dto.SendRequest;
import com.fangcang.hotel.data.base.util.HttpClientUtil;
import com.fangcang.hotel.framework.common.exception.BizException;
import com.fangcang.hotel.framework.common.util.StrUtilX;
import com.fangcang.hotel.mtosbld.config.MTOSBLDConfig;
import com.fangcang.hotel.mtosbld.config.MTOSBLDExtendConfig;
import com.fangcang.hotel.mtosbld.constants.MTOSBLDConstants;
import com.fangcang.hotel.mtosbld.dto.request.*;
import com.fangcang.hotel.mtosbld.dto.response.*;
import com.fangcang.hotel.mtosbld.enums.MTHttpPathEnum;
import com.fangcang.hotel.mtosbld.enums.ResponseStatusEnum;
import com.fangcang.hotel.mtosbld.manger.MTOSBLDManager;
import com.fangcang.hotel.mtosbld.util.LogPrintUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.*;

@Slf4j
@Service
public class MTOSBLDManagerImpl implements MTOSBLDManager {

    @Autowired
    private MTOSBLDConfig mtosbldConfig;

    @Autowired
    private LogPrintUtil logPrintUtil;

    /**
     * POST 酒店基础信息
     *
     * @param request
     * @param extendConfig
     * @return
     */
    @Override
    public HotelsResponse getHotels(HotelsRequest request, MTOSBLDExtendConfig extendConfig) {
        HotelsResponse response = null;
        String desc = MTHttpPathEnum.HOTELS.getDesc();
        try {
            String resp = commonRequest(request, extendConfig, MTHttpPathEnum.HOTELS.getValue());
            response = JSON.parseObject(resp, new TypeReference<HotelsResponse>() {
            });
        } catch (Exception e) {
            logPrintUtil.simplePrintErrorLog("getHotels", "error:{}", e.getMessage());
            logPrintUtil.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), desc);
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }
        return response;
    }

    /**
     * POST 查询酒店价格
     *
     * @param request
     * @param extendConfig
     * @return
     */
    @Override
    public HotelSearchResponse getHotelSearch(HotelSearchRequest request, MTOSBLDExtendConfig extendConfig) {
        HotelSearchResponse response = null;
        String desc = MTHttpPathEnum.HOTEL_SEARCH.getDesc();
        try {
            String resp = commonRequest(request, extendConfig, MTHttpPathEnum.HOTEL_SEARCH.getValue());
            response = JSON.parseObject(resp, new TypeReference<HotelSearchResponse>() {
            });
        } catch (Exception e) {
            logPrintUtil.simplePrintErrorLog("getHotelSearch", "error:{}", e.getMessage());
            logPrintUtil.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), desc);
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }
        return response;
    }

    /**
     * POST 检查酒店价格
     *
     * @param request
     * @param extendConfig
     * @return
     */
    @Override
    public CheckResponse check(CheckRequest request, MTOSBLDExtendConfig extendConfig) {
        CheckResponse response = null;
        String desc = MTHttpPathEnum.CHECK.getDesc();
        try {
            String resp = commonRequest(request, extendConfig, MTHttpPathEnum.CHECK.getValue());
            response = JSON.parseObject(resp, new TypeReference<CheckResponse>() {
            });
        } catch (Exception e) {
            logPrintUtil.simplePrintErrorLog("check", "error:{}", e.getMessage());
            logPrintUtil.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), desc);
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }
        return response;
    }

    /**
     * POST 预定酒店
     *
     * @param request
     * @param extendConfig
     * @return
     */
    @Override
    public BookingResponse booking(BookingRequest request, MTOSBLDExtendConfig extendConfig) {
        BookingResponse response = null;
        String desc = MTHttpPathEnum.BOOKING.getDesc();
        try {
            String resp = commonRequest(request, extendConfig, MTHttpPathEnum.BOOKING.getValue());
            response = JSON.parseObject(resp, new TypeReference<BookingResponse>() {
            });
        } catch (Exception e) {
            logPrintUtil.simplePrintErrorLog("booking", "error:{}", e.getMessage());
            logPrintUtil.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), desc);
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }
        return response;
    }

    /**
     * POST 取消预定
     *
     * @param request
     * @param extendConfig
     * @return
     */
    @Override
    public CancelResponse cancel(CancelRequest request, MTOSBLDExtendConfig extendConfig) {
        CancelResponse response = null;
        String desc = MTHttpPathEnum.CANCEL.getDesc();
        try {
            String resp = commonRequest(request, extendConfig, MTHttpPathEnum.CANCEL.getValue());
            response = JSON.parseObject(resp, new TypeReference<CancelResponse>() {
            });
        } catch (Exception e) {
            logPrintUtil.simplePrintErrorLog("cancel", "error:{}", e.getMessage());
            logPrintUtil.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), desc);
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }
        return response;
    }

    /**
     * POST 查询订单
     *
     * @param request
     * @param extendConfig
     * @return
     */
    @Override
    public QueryOrderResponse queryOrder(QueryOrderRequest request, MTOSBLDExtendConfig extendConfig) {
        QueryOrderResponse response = null;
        String desc = MTHttpPathEnum.QUERYORDER.getDesc();
        try {
            String resp = commonRequest(request, extendConfig, MTHttpPathEnum.QUERYORDER.getValue());
            response = JSON.parseObject(resp, new TypeReference<QueryOrderResponse>() {
            });
        } catch (Exception e) {
            logPrintUtil.simplePrintErrorLog("queryOrder", "error:{}", e.getMessage());
            logPrintUtil.saveErrorLog(e);
            String msg = MessageFormat.format(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION.getMsg(), desc);
            throw new BizException(GlobalErrorCodeEnum.SUPPLY_API_EXCEPTION, msg);
        }
        return response;
    }

    /**
     * 供应商接口请求公共实现方法
     *
     * @param request      请求参数
     * @param extendConfig 配置
     * @param api          接口地址或者方法名
     * @return
     */
    public String commonRequest(BaseRequest request, MTOSBLDExtendConfig extendConfig, String api) {
        //检查配置是否正确
        if (!StringUtilExtend.isValidString(extendConfig.getUserName()) || !StringUtilExtend.isValidString(extendConfig.getPassword())) {
            return null;
        }
        HttpResponse httpResponse = null;
        String requestString = JSON.toJSONString(request);
        Map<String, String> headerParams = new HashMap<>();
        headerParams.put("Authorization", getAuthorization(extendConfig));
        try {

            //发单需要单独配置超时时间
            Integer connectTimeOut = mtosbldConfig.getConnectTimeOut();
            Integer socketTimeOut = mtosbldConfig.getSocketTimeOut();
            if (StringUtilExtend.isValidString(api)
                    && api.equals(MTHttpPathEnum.BOOKING.getValue())) {
                connectTimeOut = mtosbldConfig.getSendOrderTimeOut();
                socketTimeOut = mtosbldConfig.getSendOrderTimeOut();
            }

            // 连接超时重试次数
            int retryCount = mtosbldConfig.getConnectTimeOutRetrySize();
            for (int i = 0; i <= retryCount; i++) {

                String url = StringUtilExtend.uniteString(mtosbldConfig.getBaseUrl(), api);
                String forwardOrDirect = mtosbldConfig.getForwardOrDirect();

                if ("1".equals(forwardOrDirect)) {
                    SendRequest sendRequest = new SendRequest();
                    sendRequest.setRequestUrl(url);
                    sendRequest.setRequestInfo(requestString);
                    sendRequest.setConnectTime(connectTimeOut);
                    sendRequest.setSocketTimeOut(socketTimeOut);
                    sendRequest.setContentType(MTOSBLDConstants.CONTENTTYPE);
                    sendRequest.setHeaders(headerParams);
                    //走forward转发
                    httpResponse = doPost(mtosbldConfig.getForwardUrl(), JSON.toJSONString(sendRequest),
                            MTOSBLDConstants.CONTENTTYPE, connectTimeOut, socketTimeOut);
                } else {
                    //直接请求接口
                    httpResponse = HttpClientUtil.doPost(url, requestString, headerParams, MTOSBLDConstants.CONTENTTYPE,
                            connectTimeOut, socketTimeOut);
                }

                if (HttpResponse.STATUS.SUCCESS == httpResponse.status) {
                    break;
                }
            }

            if (HttpResponse.STATUS.CONNECT_TIME_OUT == httpResponse.status || HttpResponse.STATUS.READ_TIME_OUT == httpResponse.status) {
                logPrintUtil.simplePrintErrorLog("commonRequest", "请求萌兔接口失败,接口超时url:{}", api);
                return null;
            } else if (HttpResponse.STATUS.SYSTEM_ERROR == httpResponse.status) {
                logPrintUtil.simplePrintErrorLog("commonRequest", "请求萌兔接口失败,接口调用异常url:{}", api);
                return null;
            }
        } catch (Exception e) {
            logPrintUtil.simplePrintErrorLog("commonRequest", "萌兔发送请求失败,发生未知错误url:{},error:{}", api, e.getMessage());
            logPrintUtil.saveErrorLog(e);
            return null;
        }
        return httpResponse.getContent();
    }

    public static HttpResponse doPost(String url, String data, String contentType, int connectTimeout, int socketTimeout) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        RequestConfig config = RequestConfig.custom().setConnectTimeout(connectTimeout).setSocketTimeout(socketTimeout).build();
        httpPost.setConfig(config);
        HttpEntity entity = new StringEntity(data, "utf-8");
        httpPost.setEntity(entity);
        return sendRequest(httpClient, httpPost, contentType);
    }

    private static HttpResponse sendRequest(CloseableHttpClient httpClient, HttpUriRequest request,
                                            String contentType) {
        HttpResponse queryResponse = new HttpResponse();
        String content = null;
        CloseableHttpResponse response = null;
        try {
            request.setHeader("Content-Type", contentType);

            response = httpClient.execute(request);
            HttpEntity entity = response.getEntity();
            content = EntityUtils.toString(entity, "utf-8");
            queryResponse.setContent(content);
            queryResponse.setStatus(HttpResponse.STATUS.SUCCESS);
        } catch (ClientProtocolException e) {
            queryResponse.setStatus(HttpResponse.STATUS.CONNECT_TIME_OUT);
            log.error("http request error.uri:{}" , request.getURI(), e);
        } catch (ConnectTimeoutException e) {
            queryResponse.setStatus(HttpResponse.STATUS.CONNECT_TIME_OUT);
            log.error("http request connect time out.uri:{}", request.getURI(), e);
        } catch (SocketTimeoutException e) {
            queryResponse.setStatus(HttpResponse.STATUS.READ_TIME_OUT);
            log.error("http request socket time out.uri:{}" , request.getURI(), e);
        } catch (Exception e) {
            queryResponse.setStatus(HttpResponse.STATUS.SYSTEM_ERROR);
            log.error("http request error.uri:{}" , request.getURI(), e);
        } finally {
            try {
                if (response != null){
                    response.close();
                }
            } catch (IOException e) {
            }
            try {
                if (httpClient != null){
                    httpClient.close();
                }
            } catch (IOException e) {
            }
        }
        return queryResponse;
    }

    /**
     * 构建授权字符串
     *
     * @param extendConfig 扩展配置对象，包含身份验证信息
     * @return 返回经过Base64编码的授权字符串，格式为"Basic encodedString"
     */
    private String getAuthorization(MTOSBLDExtendConfig extendConfig) {
        // 获取用户名
        String username = extendConfig.getUserName();
        // 获取密码
        String password = extendConfig.getPassword();
        // 拼接用户名和密码，格式为username:password
        String authString = username + ":" + password;
        // 使用Base64对拼接后的字符串进行编码
        String encodedAuthString = Base64.getEncoder()
                .encodeToString(authString.getBytes(StandardCharsets.UTF_8));
        // 返回编码后的授权字符串，前缀为"Basic"
        return "Basic " + encodedAuthString;
    }

}
