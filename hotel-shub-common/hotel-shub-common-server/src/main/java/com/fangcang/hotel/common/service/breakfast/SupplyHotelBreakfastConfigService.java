package com.fangcang.hotel.common.service.breakfast;

import com.fangcang.hotel.common.controller.admin.breakfast.vo.SupplyHotelBreakfastConfigDelReqVO;
import com.fangcang.hotel.common.controller.admin.breakfast.vo.SupplyHotelBreakfastConfigPageReqVO;
import com.fangcang.hotel.common.controller.admin.breakfast.vo.SupplyHotelBreakfastConfigReqAditVO;
import com.fangcang.hotel.common.controller.admin.breakfast.vo.SupplyHotelBreakfastConfigReqVO;
import com.fangcang.hotel.common.controller.admin.prepaid.vo.SupplyPrepaidWhiteConfigDelReqVO;
import com.fangcang.hotel.common.controller.admin.prepaid.vo.SupplyPrepaidWhiteConfigPageReqVO;
import com.fangcang.hotel.common.controller.admin.prepaid.vo.SupplyPrepaidWhiteConfigReqAditVO;
import com.fangcang.hotel.common.dal.db.breakfast.SupplyHotelBreakfastConfigDO;
import com.fangcang.hotel.common.dal.db.prepaid.SupplyPrepaidWhiteConfigDO;
import com.fangcang.hotel.framework.common.domain.PageResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @Date 2023-12-06 10:22:38
 **/
@Validated
public interface SupplyHotelBreakfastConfigService {



    PageResult<SupplyHotelBreakfastConfigDO> queryHotelBreakfastConfigPage(SupplyHotelBreakfastConfigPageReqVO supplyHotelBreakfastConfigPageReqVO);

    List<SupplyHotelBreakfastConfigDO> queryHotelBreakfastConfigList(SupplyHotelBreakfastConfigReqVO hotelBreakfastConfigReqVO);

    void hotelBreakfastConfigDel(SupplyHotelBreakfastConfigDelReqVO supplyQueryProductConfigDelReqVO);

    void hotelBreakfastConfigAdd(@Valid SupplyHotelBreakfastConfigReqAditVO supplyHotelBreakfastConfigReqAditVO);
}
