var keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
function rotate_left(n, s)
{
    var t4 = ( n << s ) | (n >>> (32 - s));
    return t4;
}

function cvt_hex(val)
{
    var str = "";
    var i;
    var v;
    for( i = 7; i >= 0; i -- )
    {
        v = (val >>> (i * 4)) & 0x0f;
        str += v.toString(16);

    }
    return str;
}

function SHA1 (msg)
{

    function Utf8Encode(string)
    {

        string = string.replace(/\r\n/g, "\n");

        var utftext = "";
        for (var n = 0; n < string.length; n ++ )
        {
            var c = string.charCodeAt(n);
            if (c < 128)
            {
                utftext += String.fromCharCode(c);

            }
            else if((c > 127) && (c < 2048))
            {
                utftext += String.fromCharCode((c >> 6) | 192);
                utftext += String.fromCharCode((c & 63) | 128);

            }
            else
            {
                utftext += String.fromCharCode((c >> 12) | 224);
                utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                utftext += String.fromCharCode((c & 63) | 128);

            }
        }
        return utftext;

    };

    var blockstart;
    var i, j;
    var W = new Array(80);
    var H0 = 0x67452301;
    var H1 = 0xEFCDAB89;
    var H2 = 0x98BADCFE;
    var H3 = 0x10325476;
    var H4 = 0xC3D2E1F0;
    var A, B, C, D, E;
    var temp;
    msg = Utf8Encode(msg);
    var msg_len = msg.length;
    var word_array = new Array();

    for( i = 0; i < msg_len - 3; i += 4 )
    {
        j = msg.charCodeAt(i) << 24 | msg.charCodeAt(i + 1) << 16 | msg.charCodeAt(i + 2) << 8 | msg.charCodeAt(i + 3);
        word_array.push( j );

    }
    switch( msg_len % 4 )
    {
        case 0 :
            i = 0x080000000;
            break;
        case 1 :
            i = msg.charCodeAt(msg_len - 1) << 24 | 0x0800000;
            break;
        case 2 :
            i = msg.charCodeAt(msg_len - 2) << 24 | msg.charCodeAt(msg_len - 1) << 16 | 0x08000;
            break;
        case 3 :
            i = msg.charCodeAt(msg_len - 3) << 24 | msg.charCodeAt(msg_len - 2) << 16 | msg.charCodeAt(msg_len - 1) << 8    | 0x80;
            break;

    }
    word_array.push( i );
    while( (word_array.length % 16) != 14 )
        word_array.push( 0 );
    word_array.push( msg_len >>> 29 );
    word_array.push((msg_len << 3) & 0x0ffffffff );
    for ( blockstart = 0; blockstart < word_array.length; blockstart += 16 )
    {
        for( i = 0; i < 16; i ++ ) W[i] = word_array[blockstart + i];
        for( i = 16; i <= 79; i ++ ) W[i] = rotate_left(W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16], 1);
        A = H0;
        B = H1;
        C = H2;
        D = H3;
        E = H4;
        for( i = 0; i <= 19; i ++ )
        {
            temp = (rotate_left(A, 5) + ((B & C) | ( ~ B & D)) + E + W[i] + 0x5A827999) & 0x0ffffffff;
            E = D;
            D = C;
            C = rotate_left(B, 30);
            B = A;
            A = temp;

        }
        for( i = 20; i <= 39; i ++ )
        {
            temp = (rotate_left(A, 5) + (B ^ C ^ D) + E + W[i] + 0x6ED9EBA1) & 0x0ffffffff;
            E = D;
            D = C;
            C = rotate_left(B, 30);
            B = A;
            A = temp;

        }
        for( i = 40; i <= 59; i ++ )
        {
            temp = (rotate_left(A, 5) + ((B & C) | (B & D) | (C & D)) + E + W[i] + 0x8F1BBCDC) & 0x0ffffffff;
            E = D;
            D = C;
            C = rotate_left(B, 30);
            B = A;
            A = temp;
        }
        for( i = 60; i <= 79; i ++ )
        {
            temp = (rotate_left(A, 5) + (B ^ C ^ D) + E + W[i] + 0xCA62C1D6) & 0x0ffffffff;
            E = D;
            D = C;
            C = rotate_left(B, 30);
            B = A;
            A = temp;

        }
        H0 = (H0 + A) & 0x0ffffffff;
        H1 = (H1 + B) & 0x0ffffffff;
        H2 = (H2 + C) & 0x0ffffffff;
        H3 = (H3 + D) & 0x0ffffffff;
        H4 = (H4 + E) & 0x0ffffffff;

    }
    var temp = cvt_hex(H0) + cvt_hex(H1) + cvt_hex(H2) + cvt_hex(H3) + cvt_hex(H4);
    return temp.toUpperCase();
}

function SHA1Bytes (msg)
{
    var blockstart;
    var i, j;
    var W = new Array(80);
    var H0 = 0x67452301;
    var H1 = 0xEFCDAB89;
    var H2 = 0x98BADCFE;
    var H3 = 0x10325476;
    var H4 = 0xC3D2E1F0;
    var A, B, C, D, E;
    var temp;
    var msg_len = msg.length;
    var word_array = new Array();
    for( i = 0; i < msg_len - 3; i += 4 )
    {
        if (msg[i] > 255 || msg[i + 1] > 255 || msg[i + 2] > 255 || msg[i + 3] > 255) alert('Not a byte!');
        j = msg[i] << 24 | msg[i + 1] << 16 | msg[i + 2] << 8 | msg[i + 3];
        word_array.push( j );

    }
    switch( msg_len % 4 )
    {
        case 0 : i = 0x080000000;
            break;
        case 1 : i = msg[msg_len - 1] << 24 | 0x0800000;
            break;
        case 2 : i = msg[msg_len - 2] << 24 | msg[msg_len - 1] << 16 | 0x08000;
            break;
        case 3 : i = msg[msg_len - 3] << 24 | msg[msg_len - 2] << 16 | msg[msg_len - 1] << 8    | 0x80;
            break;

    }
    word_array.push( i );
    while( (word_array.length % 16) != 14 )
        word_array.push( 0 );
    word_array.push( msg_len >>> 29 );
    word_array.push( (msg_len << 3) & 0x0ffffffff );
    for ( blockstart = 0; blockstart < word_array.length; blockstart += 16 )
    {
        for( i = 0; i < 16; i ++ ) W[i] = word_array[blockstart + i];
        for( i = 16; i <= 79; i ++ ) W[i] = rotate_left(W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16], 1);
        A = H0;
        B = H1;
        C = H2;
        D = H3;
        E = H4;
        for( i = 0; i <= 19; i ++ )
        {
            temp = (rotate_left(A, 5) + ((B & C) | ( ~ B & D)) + E + W[i] + 0x5A827999) & 0x0ffffffff;
            E = D;
            D = C;
            C = rotate_left(B, 30);
            B = A;
            A = temp;

        }
        for( i = 20; i <= 39; i ++ )
        {
            temp = (rotate_left(A, 5) + (B ^ C ^ D) + E + W[i] + 0x6ED9EBA1) & 0x0ffffffff;
            E = D;
            D = C;
            C = rotate_left(B, 30);
            B = A;
            A = temp;

        }
        for( i = 40; i <= 59; i ++ )
        {
            temp = (rotate_left(A, 5) + ((B & C) | (B & D) | (C & D)) + E + W[i] + 0x8F1BBCDC) & 0x0ffffffff;
            E = D;
            D = C;
            C = rotate_left(B, 30);
            B = A;
            A = temp;
        }
        for( i = 60; i <= 79; i ++ )
        {
            temp = (rotate_left(A, 5) + (B ^ C ^ D) + E + W[i] + 0xCA62C1D6) & 0x0ffffffff;
            E = D;
            D = C;
            C = rotate_left(B, 30);
            B = A;
            A = temp;

        }
        H0 = (H0 + A) & 0x0ffffffff;
        H1 = (H1 + B) & 0x0ffffffff;
        H2 = (H2 + C) & 0x0ffffffff;
        H3 = (H3 + D) & 0x0ffffffff;
        H4 = (H4 + E) & 0x0ffffffff;

    }
    var temp = cvt_hex(H0) + cvt_hex(H1) + cvt_hex(H2) + cvt_hex(H3) + cvt_hex(H4);
    return temp.toUpperCase();
}



function encode64Bytes(input)
{
    var output = '';
    var chr1, chr2, chr3;
    var enc1, enc2, enc3, enc4;
    var i = 0;
    while (i < input.length)
    {
        chr1 = input[i ++ ];
        chr2 = input[i ++ ];
        chr3 = input[i ++ ];
        enc1 = chr1 >> 2;
        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
        enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
        enc4 = chr3 & 63;
        if (isNaN(chr2))
        {
            enc3 = enc4 = 64;

        }
        else if (isNaN(chr3))
        {
            enc4 = 64;

        }
        output = output + keyStr.charAt(enc1) + keyStr.charAt(enc2) + keyStr.charAt(enc3) + keyStr.charAt(enc4);

    }
    return output;
}
function decode64Bytes(input)
{
    var output = [];
    var chr1, chr2, chr3;
    var enc1, enc2, enc3, enc4;
    var i = 0;
    // remove all characters that are not A - Z, a - z, 0 - 9, + , / , or =
    input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
    while (i < input.length)
    {
        enc1 = keyStr.indexOf(input.charAt(i ++ ));
        enc2 = keyStr.indexOf(input.charAt(i ++ ));
        enc3 = keyStr.indexOf(input.charAt(i ++ ));
        enc4 = keyStr.indexOf(input.charAt(i ++ ));
        chr1 = (enc1 << 2) | (enc2 >> 4);
        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
        chr3 = ((enc3 & 3) << 6) | enc4;
        output.push(chr1);
        if (enc3 != 64)
        {
            output.push(chr2);

        }
        if (enc4 != 64)
        {
            output.push(chr3);

        }
    }
    return output;
}
function parseHexaBytes(iText)
{
    var aResult = [];
    for (var i = 0; i < iText.length; i = i + 2)
    {
        var aValue = parseInt(iText.substr(i, 2), 16);
        // if (aValue > 255) alert('Too large!');
        // if (aValue == 0)
            // alert('Null value!');
        aResult.push(aValue);

    }
    return aResult;
}
function stringToArray(iText)
{
    var aResult = [];
    for (var i = 0; i < iText.length; i = i + 1)
    {
        aResult.push(iText.charCodeAt(i));

    }
    return aResult;
}
function WbsPassword(pwd, timestamp, nonceB64)
{
    var aPwd = parseHexaBytes(SHA1(pwd));

    var aNonce = decode64Bytes(nonceB64);//alert(aNonce);
    var aTime = stringToArray(timestamp);
    var aHash = SHA1Bytes(aNonce.concat(aTime.concat(aPwd)));//alert(aHash);
    var HshPwd = encode64Bytes(parseHexaBytes(aHash));//alert(HshPwd);

    return HshPwd;
}